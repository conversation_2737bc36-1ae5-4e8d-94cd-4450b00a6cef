<?php
/**
 * Générateur de Données Synthétiques pour FluidMotion Labs
 * Génère des données de test réalistes et cohérentes avec variance séquentielle contrôlée
 *
 * Fonctionnalités principales:
 * - Génération de données cohérentes avec variance ±10% entre points consécutifs
 * - Simulation de patterns réalistes d'essais hydrauliques
 * - Corrélation entre pression, débit et température
 * - Limites de sécurité pour valeurs hydrauliques réalistes
 * - Configuration personnalisable du pourcentage de variance
 */

require_once(__DIR__ . '/../config/database.php');
require_once(__DIR__ . '/affaires.php');
require_once(__DIR__ . '/essais.php');
require_once(__DIR__ . '/courbes.php');
require_once(__DIR__ . '/pv.php');
require_once(__DIR__ . '/rendement.php');

class DataGenerator
{
    private $db;
    private $user_id;

    // Configuration pour la génération de données cohérentes
    private $variance_percentage = 10; // Variance de ±10% entre points consécutifs

    // Données de référence pour la génération
    private $clients_industriels = [
        'Airbus Helicopters', 'Safran Hydraulics', 'Liebherr Aerospace', 'Thales Avionics',
        'Bosch Rexroth', 'Parker Hannifin', 'Eaton Hydraulics', 'Danfoss Power Solutions',
        'Hydac Technology', 'Poclain Hydraulics', 'Bucher Hydraulics', 'Hawe Hydraulik',
        'Moog Controls', 'Atos Hydraulics', 'Yuken Europe', 'Vickers Hydraulics',
        'Caterpillar Inc.', 'Komatsu Europe', 'Volvo Construction', 'JCB Hydraulics',
        'Manitou Group', 'Terex Corporation', 'CNH Industrial', 'Doosan Infracore',
        'Hyundai Heavy', 'Kubota Europe', 'New Holland', 'Case Construction'
    ];

    private $types_essais = [
        'Test de Vérin Simple Effet', 'Test de Vérin Double Effet', 'Test de Vérin Télescopique',
        'Test de Vérin Rotatif', 'Test de Vérin à Amortissement', 'Test de Vérin Haute Pression',
        'Test de Vérin Basse Pression', 'Test de Vérin de Levage', 'Test de Vérin de Poussée',
        'Test de Vérin de Traction', 'Test de Vérin de Serrage', 'Test de Vérin de Positionnement',
        'Test de Vérin de Sécurité', 'Test de Vérin Étanche', 'Test de Vérin Résistant',
        'Test de Vérin de Précision', 'Test de Vérin Rapide', 'Test de Vérin Lent'
    ];

    private $descriptions_projets = [
        'Développement vérin télescopique pour excavatrice 20T',
        'Optimisation vérin de levage nacelle élévatrice',
        'Validation vérin haute pression pour presse industrielle',
        'Caractérisation vérin de positionnement aéronautique',
        'Test endurance vérin télescopique grue mobile',
        'Qualification vérin de direction tracteur agricole',
        'Validation vérin étanche pour machine-outil marine',
        'Test performance vérin rapide ligne d\'assemblage',
        'Caractérisation vérin de sécurité haute pression',
        'Optimisation vérin double effet pour robot industriel',
        'Validation vérin amortisseur pour système de suspension',
        'Test résistance vérin de serrage presse hydraulique',
        'Caractérisation vérin rotatif pour table tournante',
        'Validation vérin de traction pour treuil hydraulique',
        'Test thermique vérin de poussée four industriel'
    ];

    // Statuts valides pour chaque table
    private $valid_statuts = [
        'affaires' => ['En cours', 'Terminé', 'Annulé'],
        'essais' => ['En attente', 'En cours', 'Terminé', 'Annulé'],
        'pv' => ['Brouillon', 'Finalisé', 'Envoyé', 'Annulé']
    ];

    public function __construct()
    {
        $this->db = Database::getInstance()->getConnection();
        $this->user_id = $_SESSION['user']['id'] ?? 1;
    }

    /**
     * Générer un jeu de données complet selon la taille spécifiée
     */
    public function generateDataset($size)
    {
        $config = $this->getDatasetConfig($size);
        $results = [
            'affaires_created' => 0,
            'essais_created' => 0,
            'courbes_created' => 0,
            'pv_created' => 0,
            'rendements_created' => 0,
            'duration_seconds' => 0
        ];

        $start_time = microtime(true);

        try {
            $this->db->beginTransaction();

            // 1. Générer les affaires
            $affaires_ids = $this->generateAffaires($config['affaires']);
            $results['affaires_created'] = count($affaires_ids);

            // 2. Générer les essais
            $essais_ids = $this->generateEssais($affaires_ids, $config['essais_per_affaire']);
            $results['essais_created'] = count($essais_ids);

            // 3. Générer les courbes
            $courbes_count = $this->generateCourbes($essais_ids);
            $results['courbes_created'] = $courbes_count;

            // 4. Générer les PV
            $pv_count = $this->generatePV($essais_ids);
            $results['pv_created'] = $pv_count;

            // 5. Calculer les rendements
            $rendements_count = $this->generateRendements($essais_ids);
            $results['rendements_created'] = $rendements_count;

            $this->db->commit();

        } catch (Exception $e) {
            $this->db->rollBack();
            throw new Exception("Erreur lors de la génération: " . $e->getMessage());
        }

        $results['duration_seconds'] = round(microtime(true) - $start_time, 2);
        return $results;
    }

    /**
     * Configuration des jeux de données selon la taille
     */
    private function getDatasetConfig($size)
    {
        switch ($size) {
            case 'small':
                return [
                    'affaires' => 10,
                    'essais_per_affaire' => [2, 4], // min, max
                ];
            case 'medium':
                return [
                    'affaires' => 50,
                    'essais_per_affaire' => [2, 5],
                ];
            case 'large':
                return [
                    'affaires' => 200,
                    'essais_per_affaire' => [2, 6],
                ];
            default:
                throw new Exception("Taille de dataset non reconnue: $size");
        }
    }

    /**
     * Générer des affaires synthétiques
     */
    private function generateAffaires($count)
    {
        $affaires_ids = [];
        $base_date = new DateTime('-2 years');

        for ($i = 0; $i < $count; $i++) {
            // Générer des données réalistes
            $numero = 'SYN-' . date('Y') . '-' . str_pad($i + 1, 4, '0', STR_PAD_LEFT);
            $client = $this->clients_industriels[array_rand($this->clients_industriels)];
            $description = $this->descriptions_projets[array_rand($this->descriptions_projets)];

            // Tags réalistes
            $all_tags = ['hydraulique', 'test', 'validation', 'développement', 'qualification',
                'performance', 'endurance', 'prototype', 'série', 'maintenance'];
            $tags = array_slice($all_tags, 0, rand(2, 4));

            // Statut réaliste selon l'âge
            $age_days = rand(0, 730); // 0 à 2 ans
            if ($age_days < 30) {
                $statut = 'En cours';
            } elseif ($age_days < 365) {
                $statut = rand(0, 1) ? 'En cours' : 'Terminé';
            } else {
                $statut = 'Terminé';
            }

            // Valider le statut
            $this->validateStatut('affaires', $statut);

            // Créer l'affaire
            $affaire_id = Affaire::create($numero, $client, $description, $tags, $this->user_id);
            if ($affaire_id) {
                $affaires_ids[] = $affaire_id;

                // Mettre à jour le statut et la date si nécessaire
                if ($statut !== 'En cours') {
                    $date_creation = clone $base_date;
                    $date_creation->add(new DateInterval('P' . $age_days . 'D'));
                    Affaire::updateStatusAndDate($affaire_id, $statut, $date_creation->format('Y-m-d H:i:s'));
                }

                // Marquer comme synthétique
                Affaire::markAsSynthetic($affaire_id);
            }
        }

        return $affaires_ids;
    }

    /**
     * Valider un statut pour une table donnée
     */
    private function validateStatut($table, $statut)
    {
        if (!isset($this->valid_statuts[$table])) {
            throw new Exception("Table '$table' non reconnue pour la validation de statut");
        }

        if (!in_array($statut, $this->valid_statuts[$table])) {
            throw new Exception("Statut '$statut' non valide pour la table '$table'. Valeurs autorisées: " . implode(', ', $this->valid_statuts[$table]));
        }

        return true;
    }

    /**
     * Générer un nombre aléatoire selon une distribution normale (approximation Box-Muller)
     * Équivalent de random.gauss() en Python
     */
    private function gaussianRandom($mean, $std_dev)
    {
        static $spare = null;
        static $has_spare = false;

        if ($has_spare) {
            $has_spare = false;
            return $spare * $std_dev + $mean;
        }

        $has_spare = true;
        $u = mt_rand() / mt_getrandmax();
        $v = mt_rand() / mt_getrandmax();

        $mag = $std_dev * sqrt(-2.0 * log($u));
        $spare = $mag * cos(2.0 * pi() * $v);

        return $mag * sin(2.0 * pi() * $v) + $mean;
    }

    /**
     * Générer un ensemble de propriétés réalistes pour un composant de système fluide
     * Basé sur des distributions normales et des corrélations entre paramètres
     * Équivalent de la fonction Python generer_proprietes_realistes()
     */
    private function generateRealisticProperties()
    {
        // Paramètres de base et de distribution (Moyenne, Écart-type)
        $params = [
            "pression" => ["mean" => 6, "std_dev" => 1.5, "min" => 1, "max" => 15],
            "debit" => ["mean" => 10, "std_dev" => 2, "min" => 2, "max" => 20],
            "temperature" => ["mean" => 40, "std_dev" => 15, "min" => 5, "max" => 90],
            "puissance" => ["mean" => 75, "std_dev" => 25, "min" => 10, "max" => 200],
            "viscosite_base" => ["mean" => 46, "std_dev" => 5]
        ];

        // Génération des valeurs indépendantes avec distribution normale
        $pression = $this->gaussianRandom($params["pression"]["mean"], $params["pression"]["std_dev"]);
        $debit = $this->gaussianRandom($params["debit"]["mean"], $params["debit"]["std_dev"]);
        $temperature = $this->gaussianRandom($params["temperature"]["mean"], $params["temperature"]["std_dev"]);

        // Génération des valeurs corrélées

        // Corrélation : La viscosité diminue lorsque la température augmente
        $viscosite_base = $this->gaussianRandom($params["viscosite_base"]["mean"], $params["viscosite_base"]["std_dev"]);
        $facteur_temp_visco = 0.8; // Facteur de corrélation
        $variation_temp = $temperature - $params["temperature"]["mean"];
        $viscosite = $viscosite_base - $variation_temp * $facteur_temp_visco;

        // Corrélation : La puissance dépend du débit et de la pression
        // Formule simplifiée : P ≈ k * Q * H (Puissance ≈ k * débit * pression)
        $k = 0.18; // Facteur de conversion et d'efficacité approximatif
        $puissance_calculee = $debit * $pression * $k;
        // Ajouter une variation aléatoire (bruit) autour de la valeur calculée
        $puissance = $this->gaussianRandom($puissance_calculee, $params["puissance"]["std_dev"] / 2);

        // Nettoyage et formatage (clamping et arrondi)
        $pression = max($params["pression"]["min"], min($pression, $params["pression"]["max"]));
        $debit = max($params["debit"]["min"], min($debit, $params["debit"]["max"]));
        $temperature = max($params["temperature"]["min"], min($temperature, $params["temperature"]["max"]));
        $puissance = max($params["puissance"]["min"], min($puissance, $params["puissance"]["max"]));
        $viscosite = max(5, $viscosite); // Viscosité minimale de 5 cSt

        // Générer des valeurs théoriques légèrement supérieures pour assurer des rendements < 100%
        // Les valeurs théoriques représentent les performances idéales/nominales
        $debit_theorique = $debit * (1.05 + rand(0, 10) / 100.0); // 5-15% supérieur au débit réel
        $puissance_theorique = $puissance * (1.10 + rand(0, 15) / 100.0); // 10-25% supérieur à la puissance réelle

        // Création du dictionnaire final avec les unités
        return [
            "pression_nominale" => round($pression, 1) . " bar",
            "debit_nominal" => round($debit_theorique, 1) . " L/min", // Utiliser le débit théorique
            "temperature_fluide" => round($temperature, 0) . " °C",
            "viscosite" => round($viscosite, 1) . " cSt",
            "puissance" => round($puissance_theorique, 0) . " W" // Utiliser la puissance théorique
        ];
    }

    /**
     * Générer des essais synthétiques
     */
    private function generateEssais($affaires_ids, $essais_range)
    {
        $essais_ids = [];

        foreach ($affaires_ids as $affaire_id) {
            $nb_essais = rand($essais_range[0], $essais_range[1]);

            for ($i = 0; $i < $nb_essais; $i++) {
                $type = $this->types_essais[array_rand($this->types_essais)];

                // Paramètres théoriques réalistes avec distributions normales et corrélations
                $parametres = $this->generateRealisticProperties();

                // Date d'essai cohérente
                $base_date = new DateTime('-' . rand(1, 365) . ' days');
                $date_essai = $base_date->format('Y-m-d');

                // Statut réaliste
                $age_days = (new DateTime())->diff($base_date)->days;
                if ($age_days < 7) {
                    $statut = rand(0, 1) ? 'En attente' : 'En cours';
                } else {
                    $statut = 'Terminé';
                }

                // Valider le statut
                $this->validateStatut('essais', $statut);

                $essai_id = Essai::create($affaire_id, $type, json_encode($parametres), $date_essai);
                if ($essai_id) {
                    $essais_ids[] = $essai_id;

                    // Mettre à jour le statut et marquer comme synthétique
                    $resultat = null;
                    if ($statut === 'Terminé') {
                        $resultat = 'Test réalisé avec succès. Paramètres conformes aux spécifications.';
                    }

                    Essai::updateStatusAndResult($essai_id, $statut, $resultat);
                    Essai::markAsSynthetic($essai_id);
                }
            }
        }

        return $essais_ids;
    }

    /**
     * Générer des courbes synthétiques réalistes
     */
    private function generateCourbes($essais_ids)
    {
        $courbes_count = 0;

        foreach ($essais_ids as $essai_id) {
            // Vérifier si l'essai est terminé
            $statut = Essai::getStatus($essai_id);

            if ($statut === 'Terminé') {
                // Générer des courbes CPA et CPB
                $courbes_count += $this->generateCourbeData($essai_id, 'CPA');
                $courbes_count += $this->generateCourbeData($essai_id, 'CPB');

                // Parfois générer une courbe résultante
                if (rand(0, 1)) {
                    $courbes_count += $this->generateCourbeData($essai_id, 'Résultante');
                }
            }
        }

        return $courbes_count;
    }

    /**
     * Générer une séquence de points de données cohérents avec variance contrôlée
     * Chaque point reste dans une variance de ±X% du point précédent
     */
    private function generateSequentialDataPoints($nb_points, $base_pressure, $base_flow, $base_temperature, $start_time)
    {
        $donnees = [];
        $variance_factor = $this->variance_percentage / 100.0; // Convertir en facteur (0.1 pour 10%)

        // Limites réalistes pour les systèmes hydrauliques
        $pressure_min = 10000;   // 0.1 bar minimum
        $pressure_max = 350000;  // 350 bar maximum
        $flow_min = 1.0;         // 1 L/min minimum
        $flow_max = 150.0;       // 150 L/min maximum
        $temp_min = 20;          // 20°C minimum
        $temp_max = 90;          // 90°C maximum

        // Valeurs initiales
        $current_pressure = $base_pressure;
        $current_flow = $base_flow;
        $current_temperature = $base_temperature;

        for ($i = 0; $i < $nb_points; $i++) {
            $time_offset = $i * 2; // Point toutes les 2 secondes
            $timestamp = clone $start_time;
            $timestamp->add(new DateInterval('PT' . $time_offset . 'S'));

            // Pour le premier point, utiliser les valeurs de base
            if ($i == 0) {
                $pressure = $current_pressure;
                $flow = $current_flow;
                $temperature = $current_temperature;
            } else {
                // Générer des variations cohérentes (±variance_percentage du point précédent)
                $pressure_variation = 1 + (rand(-100, 100) / 100.0) * $variance_factor;
                $flow_variation = 1 + (rand(-100, 100) / 100.0) * $variance_factor;
                $temp_variation = 1 + (rand(-50, 50) / 100.0) * $variance_factor; // Température varie moins

                // Appliquer les variations
                $pressure = $current_pressure * $pressure_variation;
                $flow = $current_flow * $flow_variation;
                $temperature = $current_temperature * $temp_variation;

                // Ajouter des patterns réalistes pour les essais hydrauliques
                $progress = $i / $nb_points;

                // Pattern de montée en pression en début d'essai
                if ($progress < 0.2) {
                    $pressure *= (1 + $progress * 0.1); // Augmentation graduelle
                }

                // Corrélation pression-débit (pression élevée = débit légèrement réduit)
                if ($pressure > $base_pressure * 1.2) {
                    $flow *= 0.95; // Réduction légère du débit
                }

                // Augmentation graduelle de température avec le temps
                $temperature += $progress * 5; // +5°C max sur la durée de l'essai

                // Appliquer les limites de sécurité
                $pressure = max($pressure_min, min($pressure_max, $pressure));
                $flow = max($flow_min, min($flow_max, $flow));
                $temperature = max($temp_min, min($temp_max, $temperature));

                // Correction de dérive excessive (éviter que les valeurs s'éloignent trop de la base)
                $max_drift = 0.3; // Maximum 30% de dérive par rapport aux valeurs de base
                if (abs($pressure - $base_pressure) / $base_pressure > $max_drift) {
                    $pressure = $base_pressure * (1 + ($pressure > $base_pressure ? $max_drift : -$max_drift));
                }
                if (abs($flow - $base_flow) / $base_flow > $max_drift) {
                    $flow = $base_flow * (1 + ($flow > $base_flow ? $max_drift : -$max_drift));
                }

                // Mettre à jour les valeurs courantes pour le prochain point
                $current_pressure = $pressure;
                $current_flow = $flow;
                $current_temperature = $temperature;
            }

            $donnees[] = [
                'timestamp' => $timestamp->format('Y-m-d H:i:s'),
                'pressure_pascal' => round($pressure),
                'pressure_bar' => round($pressure / 100000, 2),
                'flow_lpm' => round($flow, 2),
                'temperature' => round($temperature),
                'sequence' => $i + 1
            ];
        }

        return $donnees;
    }

    /**
     * Configurer le pourcentage de variance entre points consécutifs
     */
    public function setVariancePercentage($percentage)
    {
        if ($percentage > 0 && $percentage <= 50) {
            $this->variance_percentage = $percentage;
        }
    }

    /**
     * Générer des données de courbe réalistes avec cohérence séquentielle
     */
    private function generateCourbeData($essai_id, $type_courbe)
    {
        // Paramètres de base selon le type de courbe
        $base_pressure = rand(80000, 250000); // 80-250 bar en Pascal
        $base_flow = rand(8, 80); // 8-80 L/min
        $base_temperature = rand(45, 75); // Température de base

        // Ajustements selon le type
        switch ($type_courbe) {
            case 'CPA':
                $pressure_factor = 1.0;
                $flow_factor = 1.0;
                break;
            case 'CPB':
                $pressure_factor = 0.85; // Pression légèrement plus faible en sortie
                $flow_factor = 0.95;
                break;
            case 'Résultante':
                $pressure_factor = 0.92;
                $flow_factor = 0.97;
                break;
        }

        // Appliquer les facteurs de type de courbe
        $adjusted_pressure = $base_pressure * $pressure_factor;
        $adjusted_flow = $base_flow * $flow_factor;

        // Générer 50-200 points de données avec cohérence séquentielle
        $nb_points = rand(50, 200);
        $start_time = new DateTime('-' . rand(1, 30) . ' minutes');

        // Utiliser la nouvelle méthode de génération séquentielle cohérente
        $donnees = $this->generateSequentialDataPoints(
            $nb_points,
            $adjusted_pressure,
            $adjusted_flow,
            $base_temperature,
            $start_time
        );

        // Créer la courbe
        if (Courbe::create($essai_id, $type_courbe, json_encode($donnees))) {
            // Marquer comme synthétique
            Courbe::markAsSynthetic($essai_id, $type_courbe);
            return 1;
        }

        return 0;
    }

    /**
     * Générer des PV synthétiques
     */
    private function generatePV($essais_ids)
    {
        $pv_count = 0;

        foreach ($essais_ids as $essai_id) {
            // Vérifier si l'essai est terminé
            $essai = Essai::getCompletedWithAffaireInfo($essai_id);

            if ($essai && rand(0, 2) > 0) { // 66% de chance de générer un PV
                $numero_pv = 'PV-SYN-' . date('Y') . '-' . str_pad($essai_id, 6, '0', STR_PAD_LEFT);

                // Normaliser les clés de l'essai pour éviter les problèmes de casse
                $essai = $this->normalizeEssaiKeys($essai);

                // Contenu réaliste du PV
                $contenu = $this->generatePVContent($essai);

                // Statut PV réaliste (principalement Finalisé, parfois Envoyé)
                $statut_pv = rand(0, 3) === 0 ? 'Envoyé' : 'Finalisé';
                $this->validateStatut('pv', $statut_pv);

                // Créer le PV
                if (PV::createWithEssai($numero_pv, $essai_id, $contenu, $statut_pv, $this->user_id)) {
                    $pv_count++;
                }
            }
        }

        return $pv_count;
    }

    /**
     * Normaliser les clés d'un essai pour éviter les problèmes de casse
     */
    private function normalizeEssaiKeys($essai)
    {
        $normalized = $essai;

        // Normaliser la clé type/type
        if (isset($essai['type']) && !isset($essai['type'])) {
            $normalized['type'] = $essai['type'];
        } elseif (isset($essai['type']) && !isset($essai['type'])) {
            $normalized['type'] = $essai['type'];
        }

        return $normalized;
    }

    /**
     * Générer le contenu d'un PV réaliste
     */
    private function generatePVContent($essai)
    {
        // Vérification défensive des clés requises
        $required_keys = ['affaire_numero', 'client', 'type', 'date_essai', 'parametre_theorique'];
        foreach ($required_keys as $key) {
            if (!isset($essai[$key])) {
                throw new Exception("Clé manquante '$key' dans les données de l'essai pour la génération du PV");
            }
        }

        $parametres = json_decode($essai['parametre_theorique'], true);

        $contenu = "PROCÈS-VERBAL D'ESSAI HYDRAULIQUE\n\n";
        $contenu .= "1. IDENTIFICATION DE L'ESSAI\n";
        $contenu .= "Affaire: " . $essai['affaire_numero'] . "\n";
        $contenu .= "Client: " . $essai['client'] . "\n";
        $contenu .= "Type d'essai: " . $essai['type'] . "\n";
        $contenu .= "Date d'essai: " . date('d/m/Y', strtotime($essai['date_essai'])) . "\n\n";

        $contenu .= "2. PARAMÈTRES D'ESSAI\n";
        if ($parametres) {
            foreach ($parametres as $param => $valeur) {
                $contenu .= ucfirst(str_replace('_', ' ', $param)) . ": " . $valeur . "\n";
            }
        }
        $contenu .= "\n";

        $contenu .= "3. RÉSULTATS\n";
        $contenu .= "L'essai a été réalisé conformément aux procédures internes.\n";
        $contenu .= "Les paramètres mesurés sont conformes aux spécifications.\n";
        $contenu .= "Aucune anomalie détectée durant l'essai.\n\n";

        $contenu .= "4. COURBES DE MESURE\n";
        $contenu .= "Les courbes de pression CPA et CPB ont été enregistrées.\n";
        $contenu .= "Les données sont disponibles dans le système de gestion.\n\n";

        $contenu .= "5. CONCLUSION\n";
        $contenu .= "L'équipement testé répond aux exigences techniques.\n";
        $contenu .= "Validation accordée pour la mise en service.\n\n";

        $contenu .= "Opérateur: Système Synthétique\n";
        $contenu .= "Contrôleur: " . ($_SESSION['user']['username'] ?? 'Admin') . "\n";
        $contenu .= "Date de validation: " . date('d/m/Y H:i');

        return $contenu;
    }

    /**
     * Générer des calculs de rendement synthétiques
     */
    private function generateRendements($essais_ids)
    {
        $rendements_count = 0;

        foreach ($essais_ids as $essai_id) {
            // Vérifier si l'essai a des courbes CPA et CPB
            $courbes_count = Courbe::countByTypeForEssai($essai_id, ['CPA', 'CPB']);

            if ($courbes_count >= 2 && rand(0, 3) > 0) { // 75% de chance de calculer le rendement
                try {
                    $result = Rendement::calculerRendement($essai_id, $this->user_id);
                    if ($result['success']) {
                        // Marquer le rendement comme synthétique
                        Rendement::markAsSynthetic($result['rendement_id']);
                        $rendements_count++;
                    }
                } catch (Exception $e) {
                    // Ignorer les erreurs de calcul de rendement pour les données synthétiques
                    continue;
                }
            }
        }

        return $rendements_count;
    }

    /**
     * Générer un jeu de données avec variance cohérente personnalisée
     *
     * @param string $size Taille du dataset ('small', 'medium', 'large')
     * @param int $variance_percentage Pourcentage de variance entre points consécutifs (1-50)
     * @return array Résultats de la génération
     */
    public function generateDatasetWithVariance($size, $variance_percentage = 10)
    {
        // Configurer la variance
        $old_variance = $this->variance_percentage;
        $this->setVariancePercentage($variance_percentage);

        try {
            // Générer le dataset avec la variance configurée
            $results = $this->generateDataset($size);
            $results['variance_percentage'] = $variance_percentage;
            return $results;
        } finally {
            // Restaurer la variance précédente
            $this->variance_percentage = $old_variance;
        }
    }

    /**
     * Générer un jeu de données avec une configuration personnalisée
     */
    public function generateCustomDataset($config)
    {
        $results = [
            'affaires_created' => 0,
            'essais_created' => 0,
            'courbes_created' => 0,
            'pv_created' => 0,
            'rendements_created' => 0,
            'duration_seconds' => 0
        ];

        $start_time = microtime(true);

        try {
            $this->db->beginTransaction();

            // 1. Générer les affaires
            $affaires_ids = $this->generateAffaires($config['affaires']);
            $results['affaires_created'] = count($affaires_ids);

            // 2. Générer les essais
            $essais_ids = $this->generateEssais($affaires_ids, $config['essais_per_affaire']);
            $results['essais_created'] = count($essais_ids);

            // 3. Générer les courbes
            $courbes_count = $this->generateCourbes($essais_ids);
            $results['courbes_created'] = $courbes_count;

            // 4. Générer les PV
            $pv_count = $this->generatePV($essais_ids);
            $results['pv_created'] = $pv_count;

            // 5. Calculer les rendements
            $rendements_count = $this->generateRendements($essais_ids);
            $results['rendements_created'] = $rendements_count;

            $this->db->commit();

        } catch (Exception $e) {
            $this->db->rollBack();
            throw new Exception("Erreur lors de la génération personnalisée: " . $e->getMessage());
        }

        $results['duration_seconds'] = round(microtime(true) - $start_time, 2);
        return $results;
    }

    /**
     * Nettoyer toutes les données synthétiques
     */
    public function cleanupSyntheticData()
    {
        $results = [];

        try {
            $this->db->beginTransaction();

            // Supprimer dans l'ordre inverse des dépendances
            $results['rendements_deleted'] = Rendement::deleteSynthetic();
            $results['courbes_deleted'] = Courbe::deleteSynthetic();
            $results['pv_deleted'] = PV::deleteSynthetic();
            $results['essais_deleted'] = Essai::deleteSynthetic();
            $results['affaires_deleted'] = Affaire::deleteSynthetic();

            $this->db->commit();

        } catch (Exception $e) {
            $this->db->rollBack();
            throw new Exception("Erreur lors du nettoyage: " . $e->getMessage());
        }

        return $results;
    }

    /**
     * Nettoyer toutes les données (DANGER!)
     */
    public function cleanupAllData()
    {
        $results = [];

        try {
            $this->db->beginTransaction();

            // Supprimer dans l'ordre inverse des dépendances
            $results['rendements_deleted'] = Rendement::deleteAll();
            $results['courbes_deleted'] = Courbe::deleteAll();
            $results['pv_deleted'] = PV::deleteAll();
            $results['essais_deleted'] = Essai::deleteAll();
            $results['affaires_deleted'] = Affaire::deleteAll();

            $this->db->commit();

        } catch (Exception $e) {
            $this->db->rollBack();
            throw new Exception("Erreur lors du nettoyage complet: " . $e->getMessage());
        }

        return $results;
    }

    /**
     * Obtenir un statut aléatoire valide pour une table
     */
    private function getRandomStatut($table)
    {
        if (!isset($this->valid_statuts[$table])) {
            throw new Exception("Table '$table' non reconnue pour obtenir un statut aléatoire");
        }

        return $this->valid_statuts[$table][array_rand($this->valid_statuts[$table])];
    }
}
