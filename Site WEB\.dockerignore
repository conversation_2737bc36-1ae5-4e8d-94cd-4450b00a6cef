# Ignore development and temporary files
.git
.gitignore
*.md
README*
CHANGELOG*

# Ignore backup files (will be created at runtime)
backups/*
!backups/.gitkeep

# Ignore generated PDFs (will be created at runtime)
pdf_exports/*
!pdf_exports/.gitkeep

# Ignore temporary files
temp/*
!temp/.gitkeep

# Ignore vendor if it exists (will be installed via Composer)
vendor/*

# Ignore IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Ignore OS files
.DS_Store
Thumbs.db

# Ignore log files
*.log

# Ignore test files (but include tests directory for production debugging)
# tests/
# Uncomment above line to exclude tests in production

# Ignore Docker files
Dockerfile
Dockerfile.dev
.dockerignore
docker-compose.yml
docker-compose.dev.yml

# Ignore development files
.env
.env.dev
.env.example
dev-start.sh
dev-start.bat
README_DEV.md
