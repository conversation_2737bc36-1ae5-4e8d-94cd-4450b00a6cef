@echo off
REM Script de démarrage pour l'environnement de développement FluidMotion (Windows)
REM Usage: dev-start.bat [command]

setlocal enabledelayedexpansion

REM Définir les couleurs (limitées sur Windows)
set "GREEN=[92m"
set "YELLOW=[93m"
set "RED=[91m"
set "BLUE=[94m"
set "NC=[0m"

REM Fonction de logging (simulée avec des labels)
goto :main

:log
echo %GREEN%[%date% %time%] %~1%NC%
goto :eof

:warn
echo %YELLOW%[%date% %time%] WARNING: %~1%NC%
goto :eof

:error
echo %RED%[%date% %time%] ERROR: %~1%NC%
goto :eof

:info
echo %BLUE%[%date% %time%] INFO: %~1%NC%
goto :eof

:check_docker
REM Vérifier que Docker est installé et en cours d'exécution
docker --version >nul 2>&1
if errorlevel 1 (
    call :error "Docker n'est pas installé. Veuillez installer Docker Desktop."
    exit /b 1
)

docker info >nul 2>&1
if errorlevel 1 (
    call :error "Docker n'est pas en cours d'exécution. Veuillez démarrer Docker Desktop."
    exit /b 1
)

docker-compose --version >nul 2>&1
if errorlevel 1 (
    call :error "Docker Compose n'est pas installé."
    exit /b 1
)
goto :eof

:check_config
REM Vérifier les fichiers de configuration
if not exist ".env.dev" (
    call :warn "Fichier .env.dev non trouvé. Création d'un fichier par défaut..."
    if exist ".env.example" copy ".env.example" ".env.dev" >nul 2>&1
)

if not exist "docker-compose.dev.yml" (
    call :error "Fichier docker-compose.dev.yml non trouvé."
    exit /b 1
)

if not exist "Site WEB\Dockerfile.dev" (
    call :error "Fichier Site WEB\Dockerfile.dev non trouvé."
    exit /b 1
)
goto :eof

:setup_permissions
call :log "Configuration des permissions..."

REM Créer les répertoires nécessaires s'ils n'existent pas
if not exist "Site WEB\backups" mkdir "Site WEB\backups"
if not exist "Site WEB\pdf_exports" mkdir "Site WEB\pdf_exports"
if not exist "Site WEB\temp\mpdf" mkdir "Site WEB\temp\mpdf"
if not exist "Site WEB\vendor" mkdir "Site WEB\vendor"
goto :eof

:start_dev
call :log "Démarrage de l'environnement de développement FluidMotion..."

call :check_docker
if errorlevel 1 exit /b 1

call :check_config
if errorlevel 1 exit /b 1

call :setup_permissions

REM Copier le fichier d'environnement de développement
copy ".env.dev" ".env" >nul 2>&1

REM Construire et démarrer les conteneurs
call :log "Construction des images Docker..."
docker-compose -f docker-compose.dev.yml build

call :log "Démarrage des conteneurs..."
docker-compose -f docker-compose.dev.yml up -d

REM Attendre que les services soient prêts
call :log "Attente du démarrage des services..."
timeout /t 10 /nobreak >nul

REM Vérifier l'état des conteneurs
docker-compose -f docker-compose.dev.yml ps

REM Afficher les informations de connexion
echo.
call :info "=== ENVIRONNEMENT DE DÉVELOPPEMENT DÉMARRÉ ==="
call :info "Application Web: http://localhost:8080"
call :info "PhpMyAdmin: http://localhost:8081"
call :info "Documentation: http://localhost:3000"
echo.
call :info "Logs en temps réel: docker-compose -f docker-compose.dev.yml logs -f"
call :info "Arrêter: dev-start.bat stop"
call :info "Redémarrer: dev-start.bat restart"
call :info "================================================"
goto :eof

:stop_dev
call :log "Arrêt de l'environnement de développement..."
docker-compose -f docker-compose.dev.yml down
call :log "Environnement de développement arrêté."
goto :eof

:restart_dev
call :log "Redémarrage de l'environnement de développement..."
call :stop_dev
call :start_dev
goto :eof

:show_logs
docker-compose -f docker-compose.dev.yml logs -f
goto :eof

:clean_dev
call :warn "Nettoyage de l'environnement de développement..."
docker-compose -f docker-compose.dev.yml down -v --remove-orphans
docker system prune -f
call :log "Nettoyage terminé."
goto :eof

:shell_web
call :log "Connexion au conteneur web..."
docker-compose -f docker-compose.dev.yml exec web bash
goto :eof

:show_help
echo Script de gestion de l'environnement de développement FluidMotion
echo.
echo Usage: %~nx0 [COMMAND]
echo.
echo Commands:
echo   start     Démarrer l'environnement de développement (défaut)
echo   stop      Arrêter l'environnement de développement
echo   restart   Redémarrer l'environnement de développement
echo   logs      Afficher les logs en temps réel
echo   clean     Nettoyer complètement l'environnement
echo   shell     Ouvrir un shell dans le conteneur web
echo   help      Afficher cette aide
echo.
echo Exemples:
echo   %~nx0                # Démarrer l'environnement
echo   %~nx0 start          # Démarrer l'environnement
echo   %~nx0 logs           # Voir les logs
echo   %~nx0 shell          # Ouvrir un shell dans le conteneur
goto :eof

:main
REM Fonction principale
set "command=%~1"
if "%command%"=="" set "command=start"

if "%command%"=="start" (
    call :start_dev
) else if "%command%"=="stop" (
    call :stop_dev
) else if "%command%"=="restart" (
    call :restart_dev
) else if "%command%"=="logs" (
    call :show_logs
) else if "%command%"=="clean" (
    call :clean_dev
) else if "%command%"=="shell" (
    call :shell_web
) else if "%command%"=="help" (
    call :show_help
) else if "%command%"=="--help" (
    call :show_help
) else if "%command%"=="-h" (
    call :show_help
) else (
    call :error "Commande inconnue: %command%"
    call :show_help
    exit /b 1
)

endlocal

pause