<?php
/**
 * Générateur de Données Synthétiques - FluidMotion Labs
 * Page cachée pour générer des données de test réalistes
 * Accès: /data-generator.php (contrôleurs uniquement)
 */

session_start();

// Vérifier que l'utilisateur est connecté et est un contrôleur
if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'controleur') {
    die('Accès refusé. Cette page nécessite un utilisateur contrôleur connecté.');
}

require_once(__DIR__ . '/lib/data_generator.php');

// Traitement des actions
$message = '';
$error = '';
$progress = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $action = $_POST['action'] ?? '';
        $generator = new DataGenerator();

        switch ($action) {
            case 'generate_small':
                $variance = isset($_POST['variance']) ? intval($_POST['variance']) : null;
                if ($variance && $variance >= 1 && $variance <= 50) {
                    $result = $generator->generateDatasetWithVariance('small', $variance);
                    $message = "Petit jeu de données généré avec variance cohérente ±{$variance}% !";
                } else {
                    $result = $generator->generateDataset('small');
                    $message = "Petit jeu de données généré avec succès !";
                }
                $progress = $result;
                break;

            case 'generate_medium':
                $variance = isset($_POST['variance']) ? intval($_POST['variance']) : null;
                if ($variance && $variance >= 1 && $variance <= 50) {
                    $result = $generator->generateDatasetWithVariance('medium', $variance);
                    $message = "Jeu de données moyen généré avec variance cohérente ±{$variance}% !";
                } else {
                    $result = $generator->generateDataset('medium');
                    $message = "Jeu de données moyen généré avec succès !";
                }
                $progress = $result;
                break;

            case 'generate_large':
                $variance = isset($_POST['variance']) ? intval($_POST['variance']) : null;
                if ($variance && $variance >= 1 && $variance <= 50) {
                    $result = $generator->generateDatasetWithVariance('large', $variance);
                    $message = "Grand jeu de données généré avec variance cohérente ±{$variance}% !";
                } else {
                    $result = $generator->generateDataset('large');
                    $message = "Grand jeu de données généré avec succès !";
                }
                $progress = $result;
                break;

            case 'generate_custom':
                $nb_affaires = intval($_POST['nb_affaires'] ?? 0);
                $min_essais = intval($_POST['min_essais'] ?? 1);
                $max_essais = intval($_POST['max_essais'] ?? 3);
                $variance = isset($_POST['variance']) ? intval($_POST['variance']) : null;

                if ($nb_affaires <= 0 || $nb_affaires > 1000) {
                    $error = "Le nombre d'affaires doit être entre 1 et 1000.";
                } elseif ($min_essais <= 0 || $max_essais <= 0 || $min_essais > $max_essais) {
                    $error = "Les valeurs d'essais doivent être valides (min ≤ max, > 0).";
                } elseif ($max_essais > 20) {
                    $error = "Le nombre maximum d'essais par affaire ne peut pas dépasser 20.";
                } elseif ($variance && ($variance < 1 || $variance > 50)) {
                    $error = "La variance doit être entre 1% et 50%.";
                } else {
                    $config = [
                        'affaires' => $nb_affaires,
                        'essais_per_affaire' => [$min_essais, $max_essais]
                    ];

                    if ($variance && $variance >= 1 && $variance <= 50) {
                        // Utiliser la génération avec variance pour les datasets personnalisés
                        $old_variance = $generator->setVariancePercentage($variance);
                        $result = $generator->generateCustomDataset($config);
                        $result['variance_percentage'] = $variance;
                        $message = "Jeu de données personnalisé généré avec variance cohérente ±{$variance}% !";
                    } else {
                        $result = $generator->generateCustomDataset($config);
                        $message = "Jeu de données personnalisé généré avec succès !";
                    }
                    $progress = $result;
                }
                break;

            case 'cleanup_synthetic':
                $result = $generator->cleanupSyntheticData();
                $message = "Données synthétiques supprimées avec succès !";
                $progress = $result;
                break;

            case 'cleanup_all':
                if (isset($_POST['confirm_cleanup']) && $_POST['confirm_cleanup'] === 'yes') {
                    $result = $generator->cleanupAllData();
                    $message = "Toutes les données ont été supprimées !";
                    $progress = $result;
                } else {
                    $error = "Confirmation requise pour supprimer toutes les données.";
                }
                break;

            default:
                $error = "Action non reconnue.";
        }
    } catch (Exception $e) {
        $error = "Erreur lors de la génération : " . $e->getMessage();
    }
}

ob_start();
?>

<div class="p-6">
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold dark:text-white">🔧 Générateur de Données Synthétiques</h1>
            <p class="text-gray-600 dark:text-gray-400 mt-2">Génération de données de test réalistes pour FluidMotion
                Labs</p>
        </div>
        <div class="text-right">
            <p class="text-sm text-gray-500 dark:text-gray-400">
                Utilisateur: <?php echo htmlspecialchars($_SESSION['user']['username'] ?? 'Inconnu'); ?></p>
            <p class="text-sm text-gray-500 dark:text-gray-400">
                Rôle: <?php echo htmlspecialchars($_SESSION['user']['role'] ?? 'Inconnu'); ?></p>
        </div>
    </div>

    <!-- Messages -->
    <?php if ($message): ?>
        <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg mb-6">
            <div class="flex">
                <svg class="w-5 h-5 text-green-400 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <div>
                    <h3 class="text-sm font-medium text-green-800 dark:text-green-400">Succès</h3>
                    <p class="text-sm text-green-700 dark:text-green-300 mt-1"><?php echo htmlspecialchars($message); ?></p>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg mb-6">
            <div class="flex">
                <svg class="w-5 h-5 text-red-400 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
                <div>
                    <h3 class="text-sm font-medium text-red-800 dark:text-red-400">Erreur</h3>
                    <p class="text-sm text-red-700 dark:text-red-300 mt-1"><?php echo htmlspecialchars($error); ?></p>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Résultats de génération -->
    <?php if (!empty($progress)): ?>
        <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg mb-6">
            <h3 class="text-lg font-semibold text-blue-800 dark:text-blue-400 mb-3">📊 Résultats de la génération</h3>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <?php foreach ($progress as $key => $value): ?>
                    <div class="bg-white dark:bg-gray-800 p-3 rounded">
                        <span class="font-medium text-gray-900 dark:text-white"><?php echo ucfirst(str_replace('_', ' ', $key)); ?>:</span>
                        <span class="text-blue-600 dark:text-blue-400 ml-2"><?php echo $value; ?></span>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    <?php endif; ?>

    <!-- Options de génération -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- Génération de données -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 class="text-xl font-bold dark:text-white mb-4">📈 Génération de Données</h2>
            <p class="text-gray-600 dark:text-gray-400 mb-4">
                Générez des jeux de données synthétiques cohérents pour les tests et démonstrations.
            </p>

            <div class="space-y-4">
                <!-- Petit jeu de données -->
                <form method="POST" class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                    <input type="hidden" name="action" value="generate_small">
                    <div class="flex justify-between items-center mb-2">
                        <h3 class="font-semibold text-gray-900 dark:text-white">Petit Dataset</h3>
                        <span class="text-sm text-gray-500 dark:text-gray-400">~2 minutes</span>
                    </div>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                        10 affaires, 30 essais, courbes et PV correspondants
                    </p>

                    <!-- Contrôles de variance -->
                    <div class="mb-3 p-2 bg-blue-50 dark:bg-blue-900/20 rounded">
                        <label class="flex items-center mb-2">
                            <input type="checkbox" id="enable_variance_small"
                                   class="mr-2 text-blue-600 focus:ring-blue-500">
                            <span class="text-sm font-medium text-blue-800 dark:text-blue-300">🔬 Génération cohérente (variance séquentielle)</span>
                        </label>
                        <div id="variance_controls_small" class="hidden">
                            <div class="flex items-center space-x-2 mb-2">
                                <label class="text-xs text-blue-700 dark:text-blue-300">Variance:</label>
                                <button type="button" onclick="setVariance('small', 5)"
                                        class="px-2 py-1 text-xs bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-200 rounded">
                                    ±5%
                                </button>
                                <button type="button" onclick="setVariance('small', 10)"
                                        class="px-2 py-1 text-xs bg-blue-200 dark:bg-blue-700 text-blue-800 dark:text-blue-200 rounded">
                                    ±10%
                                </button>
                                <button type="button" onclick="setVariance('small', 15)"
                                        class="px-2 py-1 text-xs bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-200 rounded">
                                    ±15%
                                </button>
                            </div>
                            <input type="number" name="variance" id="variance_small" min="1" max="50" value="10"
                                   class="w-full px-2 py-1 text-xs border border-blue-300 dark:border-blue-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white">
                            <p class="text-xs text-blue-600 dark:text-blue-400 mt-1">Chaque point varie de ±X% du
                                précédent</p>
                        </div>
                    </div>

                    <button type="submit"
                            class="w-full px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors">
                        Générer Petit Dataset
                    </button>
                </form>

                <!-- Jeu de données moyen -->
                <form method="POST" class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                    <input type="hidden" name="action" value="generate_medium">
                    <div class="flex justify-between items-center mb-2">
                        <h3 class="font-semibold text-gray-900 dark:text-white">Dataset Moyen</h3>
                        <span class="text-sm text-gray-500 dark:text-gray-400">~5 minutes</span>
                    </div>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                        50 affaires, 150 essais, courbes et PV correspondants
                    </p>

                    <!-- Contrôles de variance -->
                    <div class="mb-3 p-2 bg-blue-50 dark:bg-blue-900/20 rounded">
                        <label class="flex items-center mb-2">
                            <input type="checkbox" id="enable_variance_medium"
                                   class="mr-2 text-blue-600 focus:ring-blue-500">
                            <span class="text-sm font-medium text-blue-800 dark:text-blue-300">🔬 Génération cohérente (variance séquentielle)</span>
                        </label>
                        <div id="variance_controls_medium" class="hidden">
                            <div class="flex items-center space-x-2 mb-2">
                                <label class="text-xs text-blue-700 dark:text-blue-300">Variance:</label>
                                <button type="button" onclick="setVariance('medium', 5)"
                                        class="px-2 py-1 text-xs bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-200 rounded">
                                    ±5%
                                </button>
                                <button type="button" onclick="setVariance('medium', 10)"
                                        class="px-2 py-1 text-xs bg-blue-200 dark:bg-blue-700 text-blue-800 dark:text-blue-200 rounded">
                                    ±10%
                                </button>
                                <button type="button" onclick="setVariance('medium', 15)"
                                        class="px-2 py-1 text-xs bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-200 rounded">
                                    ±15%
                                </button>
                            </div>
                            <input type="number" name="variance" id="variance_medium" min="1" max="50" value="10"
                                   class="w-full px-2 py-1 text-xs border border-blue-300 dark:border-blue-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white">
                            <p class="text-xs text-blue-600 dark:text-blue-400 mt-1">Chaque point varie de ±X% du
                                précédent</p>
                        </div>
                    </div>

                    <button type="submit"
                            class="w-full px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                        Générer Dataset Moyen
                    </button>
                </form>

                <!-- Grand jeu de données -->
                <form method="POST" class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                    <input type="hidden" name="action" value="generate_large">
                    <div class="flex justify-between items-center mb-2">
                        <h3 class="font-semibold text-gray-900 dark:text-white">Grand Dataset</h3>
                        <span class="text-sm text-gray-500 dark:text-gray-400">~15 minutes</span>
                    </div>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                        200 affaires, 600 essais, courbes et PV correspondants
                    </p>

                    <!-- Contrôles de variance -->
                    <div class="mb-3 p-2 bg-blue-50 dark:bg-blue-900/20 rounded">
                        <label class="flex items-center mb-2">
                            <input type="checkbox" id="enable_variance_large"
                                   class="mr-2 text-blue-600 focus:ring-blue-500">
                            <span class="text-sm font-medium text-blue-800 dark:text-blue-300">🔬 Génération cohérente (variance séquentielle)</span>
                        </label>
                        <div id="variance_controls_large" class="hidden">
                            <div class="flex items-center space-x-2 mb-2">
                                <label class="text-xs text-blue-700 dark:text-blue-300">Variance:</label>
                                <button type="button" onclick="setVariance('large', 5)"
                                        class="px-2 py-1 text-xs bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-200 rounded">
                                    ±5%
                                </button>
                                <button type="button" onclick="setVariance('large', 10)"
                                        class="px-2 py-1 text-xs bg-blue-200 dark:bg-blue-700 text-blue-800 dark:text-blue-200 rounded">
                                    ±10%
                                </button>
                                <button type="button" onclick="setVariance('large', 15)"
                                        class="px-2 py-1 text-xs bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-200 rounded">
                                    ±15%
                                </button>
                            </div>
                            <input type="number" name="variance" id="variance_large" min="1" max="50" value="10"
                                   class="w-full px-2 py-1 text-xs border border-blue-300 dark:border-blue-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white">
                            <p class="text-xs text-blue-600 dark:text-blue-400 mt-1">Chaque point varie de ±X% du
                                précédent</p>
                        </div>
                    </div>

                    <button type="submit"
                            class="w-full px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors"
                            onclick="return confirm('La génération d\'un grand dataset peut prendre du temps. Continuer ?')">
                        Générer Grand Dataset
                    </button>
                </form>

                <!-- Génération personnalisée -->
                <form method="POST"
                      class="border border-indigo-200 dark:border-indigo-700 rounded-lg p-4 bg-indigo-50 dark:bg-indigo-900/20">
                    <input type="hidden" name="action" value="generate_custom">
                    <div class="flex justify-between items-center mb-2">
                        <h3 class="font-semibold text-indigo-900 dark:text-indigo-300">Dataset Personnalisé</h3>
                        <span class="text-sm text-indigo-600 dark:text-indigo-400">Variable</span>
                    </div>
                    <p class="text-sm text-indigo-700 dark:text-indigo-300 mb-3">
                        Spécifiez exactement les quantités souhaitées
                    </p>

                    <div class="space-y-3 mb-4">
                        <div>
                            <label for="nb_affaires"
                                   class="block text-sm font-medium text-indigo-900 dark:text-indigo-300 mb-1">
                                Nombre d'affaires (1-1000)
                            </label>
                            <input type="number" id="nb_affaires" name="nb_affaires"
                                   min="1" max="1000" value="25" required
                                   class="w-full px-3 py-2 border border-indigo-300 dark:border-indigo-600 rounded-md text-sm
                                          bg-white dark:bg-gray-800 text-gray-900 dark:text-white
                                          focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                        </div>

                        <div class="grid grid-cols-2 gap-2">
                            <div>
                                <label for="min_essais"
                                       class="block text-sm font-medium text-indigo-900 dark:text-indigo-300 mb-1">
                                    Min essais/affaire
                                </label>
                                <input type="number" id="min_essais" name="min_essais"
                                       min="1" max="20" value="2" required
                                       class="w-full px-3 py-2 border border-indigo-300 dark:border-indigo-600 rounded-md text-sm
                                              bg-white dark:bg-gray-800 text-gray-900 dark:text-white
                                              focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                            </div>
                            <div>
                                <label for="max_essais"
                                       class="block text-sm font-medium text-indigo-900 dark:text-indigo-300 mb-1">
                                    Max essais/affaire
                                </label>
                                <input type="number" id="max_essais" name="max_essais"
                                       min="1" max="20" value="4" required
                                       class="w-full px-3 py-2 border border-indigo-300 dark:border-indigo-600 rounded-md text-sm
                                              bg-white dark:bg-gray-800 text-gray-900 dark:text-white
                                              focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                            </div>
                        </div>

                        <div class="text-xs text-indigo-600 dark:text-indigo-400 bg-indigo-100 dark:bg-indigo-900/30 p-2 rounded">
                            <strong>Estimation:</strong> <span id="estimation">~75 essais au total</span>
                        </div>
                    </div>

                    <!-- Contrôles de variance pour génération personnalisée -->
                    <div class="mb-3 p-2 bg-blue-50 dark:bg-blue-900/20 rounded">
                        <label class="flex items-center mb-2">
                            <input type="checkbox" id="enable_variance_custom"
                                   class="mr-2 text-blue-600 focus:ring-blue-500">
                            <span class="text-sm font-medium text-blue-800 dark:text-blue-300">🔬 Génération cohérente (variance séquentielle)</span>
                        </label>
                        <div id="variance_controls_custom" class="hidden">
                            <div class="flex items-center space-x-2 mb-2">
                                <label class="text-xs text-blue-700 dark:text-blue-300">Variance:</label>
                                <button type="button" onclick="setVariance('custom', 5)"
                                        class="px-2 py-1 text-xs bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-200 rounded">
                                    ±5%
                                </button>
                                <button type="button" onclick="setVariance('custom', 10)"
                                        class="px-2 py-1 text-xs bg-blue-200 dark:bg-blue-700 text-blue-800 dark:text-blue-200 rounded">
                                    ±10%
                                </button>
                                <button type="button" onclick="setVariance('custom', 15)"
                                        class="px-2 py-1 text-xs bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-200 rounded">
                                    ±15%
                                </button>
                            </div>
                            <input type="number" name="variance" id="variance_custom" min="1" max="50" value="10"
                                   class="w-full px-2 py-1 text-xs border border-blue-300 dark:border-blue-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white">
                            <p class="text-xs text-blue-600 dark:text-blue-400 mt-1">Chaque point varie de ±X% du
                                précédent</p>
                        </div>
                    </div>

                    <button type="submit"
                            class="w-full px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700 transition-colors">
                        Générer Dataset Personnalisé
                    </button>
                </form>
            </div>
        </div>

        <!-- Nettoyage des données -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 class="text-xl font-bold dark:text-white mb-4">🗑️ Nettoyage des Données</h2>
            <p class="text-gray-600 dark:text-gray-400 mb-4">
                Supprimez les données synthétiques ou réinitialisez complètement la base de données.
            </p>

            <div class="space-y-4">
                <!-- Nettoyage données synthétiques -->
                <form method="POST"
                      class="border border-yellow-200 dark:border-yellow-700 rounded-lg p-4 bg-yellow-50 dark:bg-yellow-900/20">
                    <input type="hidden" name="action" value="cleanup_synthetic">
                    <h3 class="font-semibold text-yellow-800 dark:text-yellow-400 mb-2">Supprimer Données
                        Synthétiques</h3>
                    <p class="text-sm text-yellow-700 dark:text-yellow-300 mb-3">
                        Supprime uniquement les données générées par cet outil (marquées comme synthétiques).
                    </p>
                    <button type="submit"
                            class="w-full px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700 transition-colors"
                            onclick="return confirm('Supprimer toutes les données synthétiques ?')">
                        Nettoyer Données Synthétiques
                    </button>
                </form>

                <!-- Nettoyage complet -->
                <form method="POST"
                      class="border border-red-200 dark:border-red-700 rounded-lg p-4 bg-red-50 dark:bg-red-900/20">
                    <input type="hidden" name="action" value="cleanup_all">
                    <h3 class="font-semibold text-red-800 dark:text-red-400 mb-2">⚠️ Réinitialisation Complète</h3>
                    <p class="text-sm text-red-700 dark:text-red-300 mb-3">
                        <strong>DANGER:</strong> Supprime TOUTES les données (synthétiques ET réelles).
                    </p>
                    <div class="mb-3">
                        <label class="flex items-center">
                            <input type="checkbox" name="confirm_cleanup" value="yes"
                                   class="mr-2 text-red-600 focus:ring-red-500">
                            <span class="text-sm text-red-700 dark:text-red-300">
                                Je confirme vouloir supprimer TOUTES les données
                            </span>
                        </label>
                    </div>
                    <button type="submit"
                            class="w-full px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
                            onclick="return confirm('ATTENTION: Cette action supprimera TOUTES les données de façon irréversible. Êtes-vous absolument certain ?')">
                        Réinitialiser Complètement
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Nouvelle fonctionnalité : Génération cohérente avec distributions normales -->
    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-6 mb-6">
        <h2 class="text-xl font-bold text-blue-900 dark:text-blue-100 mb-4">🆕 Génération de Données Avancée</h2>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
                <h3 class="font-semibold text-blue-800 dark:text-blue-200 mb-2">Distributions Normales</h3>
                <p class="text-sm text-blue-700 dark:text-blue-300 mb-3">
                    Génération basée sur des distributions gaussiennes avec moyenne et écart-type pour chaque paramètre hydraulique.
                </p>
                <ul class="text-sm text-blue-600 dark:text-blue-400 space-y-1">
                    <li>• <strong>Pression</strong> : 6±1.5 bar (1-15 bar)</li>
                    <li>• <strong>Débit</strong> : 10±2 L/min (2-20 L/min)</li>
                    <li>• <strong>Température</strong> : 40±15°C (5-90°C)</li>
                    <li>• <strong>Puissance</strong> : 75±25 W (10-200 W)</li>
                </ul>
            </div>

            <div>
                <h3 class="font-semibold text-blue-800 dark:text-blue-200 mb-2">Corrélations Physiques</h3>
                <p class="text-sm text-blue-700 dark:text-blue-300 mb-3">
                    Relations réalistes entre paramètres basées sur les lois physiques des systèmes hydrauliques.
                </p>
                <ul class="text-sm text-blue-600 dark:text-blue-400 space-y-1">
                    <li>• <strong>Viscosité-Température</strong> : Viscosité ↓ quand T° ↑</li>
                    <li>• <strong>Puissance-Débit-Pression</strong> : P ≈ k × Q × H</li>
                    <li>• <strong>Facteur de corrélation</strong> : 0.8 pour viscosité</li>
                    <li>• <strong>Efficacité hydraulique</strong> : k = 0.18</li>
                </ul>
            </div>

            <div>
                <h3 class="font-semibold text-blue-800 dark:text-blue-200 mb-2">Variance Séquentielle</h3>
                <p class="text-sm text-blue-700 dark:text-blue-300 mb-3">
                    Chaque point de mesure varie de ±X% par rapport au point précédent, simulant des conditions d'essais réalistes.
                </p>
                <ul class="text-sm text-blue-600 dark:text-blue-400 space-y-1">
                    <li>• <strong>±5%</strong> : Tests de précision, étalonnage</li>
                    <li>• <strong>±10%</strong> : Validation industrielle standard</li>
                    <li>• <strong>±15%</strong> : Tests de robustesse, conditions difficiles</li>
                </ul>
            </div>
        </div>

        <div class="mt-4 p-3 bg-blue-100 dark:bg-blue-800/30 rounded">
            <p class="text-sm text-blue-800 dark:text-blue-200">
                <strong>💡 Nouveau :</strong> La génération utilise maintenant des distributions normales et des corrélations physiques
                pour créer des données encore plus réalistes qui respectent les lois de la physique hydraulique.
            </p>
        </div>
    </div>

    <!-- Modèle mathématique et physique -->
    <div class="bg-gradient-to-r from-green-50 to-teal-50 dark:from-green-900/20 dark:to-teal-900/20 rounded-lg p-6 mb-6">
        <h2 class="text-xl font-bold text-green-900 dark:text-green-100 mb-4">🧮 Modèle Mathématique et Physique</h2>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h3 class="font-semibold text-green-800 dark:text-green-200 mb-2">Distributions Normales</h3>
                <div class="text-sm text-green-700 dark:text-green-300 space-y-2">
                    <p><strong>Algorithme Box-Muller</strong> pour générer des valeurs gaussiennes :</p>
                    <div class="bg-green-100 dark:bg-green-800/30 p-2 rounded font-mono text-xs">
                        X = μ + σ × √(-2×ln(U)) × sin(2π×V)<br>
                        où U,V ~ Uniforme[0,1]
                    </div>
                    <p>Chaque paramètre suit une loi normale N(μ, σ²) avec contraintes min/max.</p>
                </div>
            </div>

            <div>
                <h3 class="font-semibold text-green-800 dark:text-green-200 mb-2">Corrélations Physiques</h3>
                <div class="text-sm text-green-700 dark:text-green-300 space-y-2">
                    <p><strong>Viscosité-Température :</strong></p>
                    <div class="bg-green-100 dark:bg-green-800/30 p-2 rounded font-mono text-xs">
                        ν = ν₀ - 0.8 × (T - T₀)
                    </div>
                    <p><strong>Puissance hydraulique :</strong></p>
                    <div class="bg-green-100 dark:bg-green-800/30 p-2 rounded font-mono text-xs">
                        P = 0.18 × Q × H + bruit_gaussien
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-4 p-3 bg-green-100 dark:bg-green-800/30 rounded">
            <p class="text-sm text-green-800 dark:text-green-200">
                <strong>🔬 Avantage scientifique :</strong> Cette approche génère des données qui respectent les lois physiques
                des systèmes hydrauliques, permettant des tests plus réalistes et une meilleure validation des algorithmes.
            </p>
        </div>
    </div>

    <!-- Informations sur les données générées -->
    <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
        <h2 class="text-xl font-bold dark:text-white mb-4">ℹ️ Informations sur les Données Générées</h2>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
                <h3 class="font-semibold text-gray-900 dark:text-white mb-2">Types de Données</h3>
                <ul class="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                    <li>• <strong>Affaires:</strong> Clients industriels réalistes, numéros de projets cohérents</li>
                    <li>• <strong>Essais:</strong> Tests de vérins hydrauliques (simple/double effet, télescopique,
                        rotatif)
                    </li>
                    <li>• <strong>Courbes:</strong> Données CPA/CPB avec pressions 1-15 bar, variance séquentielle
                        ±10%
                    </li>
                    <li>• <strong>PV:</strong> Rapports d'essais avec contenu technique approprié</li>
                    <li>• <strong>Rendements:</strong> Calculs d'efficacité basés sur les courbes réelles</li>
                    <li>• <strong>🆕 Distributions normales:</strong> Paramètres générés selon des lois gaussiennes</li>
                    <li>• <strong>🆕 Corrélations physiques:</strong> Relations réalistes entre paramètres hydrauliques</li>
                </ul>
            </div>

            <div>
                <h3 class="font-semibold text-gray-900 dark:text-white mb-2">Options de Génération</h3>
                <ul class="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                    <li>• <strong>Petit:</strong> 10 affaires, ~30 essais (2 min)</li>
                    <li>• <strong>Moyen:</strong> 50 affaires, ~150 essais (5 min)</li>
                    <li>• <strong>Grand:</strong> 200 affaires, ~600 essais (15 min)</li>
                    <li>• <strong>Personnalisé:</strong> Quantités sur mesure (variable)</li>
                    <li>• <strong>Limites:</strong> Max 1000 affaires, 20 essais/affaire</li>
                </ul>
            </div>

            <div>
                <h3 class="font-semibold text-gray-900 dark:text-white mb-2">Caractéristiques</h3>
                <ul class="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                    <li>• <strong>Cohérence:</strong> Relations logiques entre toutes les entités</li>
                    <li>• <strong>Réalisme:</strong> Valeurs hydrauliques dans les plages industrielles</li>
                    <li>• <strong>Chronologie:</strong> Dates respectant l'ordre temporel</li>
                    <li>• <strong>Variété:</strong> Différents types de clients et d'essais</li>
                    <li>• <strong>Traçabilité:</strong> Données marquées comme synthétiques</li>
                    <li>• <strong>🆕 Distributions gaussiennes:</strong> Génération selon lois normales (moyenne ± écart-type)</li>
                    <li>• <strong>🆕 Corrélations physiques:</strong> Viscosité-température, puissance-débit-pression</li>
                    <li>• <strong>🆕 Variance séquentielle:</strong> Points consécutifs avec variation ±1% à ±50%</li>
                    <li>• <strong>🆕 Contraintes réalistes:</strong> Limites min/max respectant les systèmes hydrauliques</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Actions rapides -->
    <div class="mt-6 flex justify-between items-center">
        <div class="flex space-x-4">
            <a href="/tests.php"
               class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors">
                ← Retour aux Tests
            </a>
            <a href="/index.php"
               class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                Tableau de Bord
            </a>
        </div>

        <div class="text-sm text-gray-500 dark:text-gray-400">
            Dernière génération: <span id="last-generation">Jamais</span>
        </div>
    </div>

    <!-- Note de migration -->
    <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg mt-6">
        <div class="flex">
            <svg class="w-5 h-5 text-blue-400 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <div>
                <h3 class="text-sm font-medium text-blue-800 dark:text-blue-400">Migration de Base de Données</h3>
                <p class="text-sm text-blue-700 dark:text-blue-300 mt-1">
                    Si vous rencontrez des erreurs lors de la génération, exécutez d'abord le script de migration :
                    <a href="/migrate_synthetic_columns.php"
                       class="underline font-medium">migrate_synthetic_columns.php</a>
                    pour mettre à jour le schéma de base de données.
                </p>
            </div>
        </div>
    </div>

    <!-- Note de sécurité -->
    <div class="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg mt-4">
        <div class="flex">
            <svg class="w-5 h-5 text-yellow-400 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 24 24">
                <path fill-rule="evenodd"
                      d="M8.485 2.495c.673-1.167 2.357-1.167 3.03 0l6.28 10.875c.673 1.167-.17 2.625-1.516 2.625H3.72c-1.347 0-2.189-1.458-1.515-2.625L8.485 2.495ZM12 9a.75.75 0 0 1 .75.75v3.5a.75.75 0 0 1-1.5 0v-3.5A.75.75 0 0 1 12 9Zm0 8a1 1 0 1 0 0-2 1 1 0 0 0 0 2Z"
                      clip-rule="evenodd"/>
            </svg>
            <div>
                <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-400">Avertissement de Sécurité</h3>
                <p class="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
                    Cette page est réservée aux contrôleurs et ne doit jamais être accessible en production.
                    Les données générées sont marquées comme synthétiques mais utilisez cette fonctionnalité
                    uniquement dans des environnements de développement ou de test.
                </p>
            </div>
        </div>
    </div>
</div>

<script>
    // Mettre à jour l'heure de dernière génération si une génération a eu lieu
    <?php if (!empty($progress)): ?>
    document.getElementById('last-generation').textContent = new Date().toLocaleString('fr-FR');
    <?php endif; ?>

    // Calcul de l'estimation en temps réel pour la génération personnalisée
    function updateEstimation() {
        const nbAffaires = parseInt(document.getElementById('nb_affaires').value) || 0;
        const minEssais = parseInt(document.getElementById('min_essais').value) || 1;
        const maxEssais = parseInt(document.getElementById('max_essais').value) || 1;

        // Validation des valeurs
        if (minEssais > maxEssais) {
            document.getElementById('max_essais').value = minEssais;
        }

        // Calcul de l'estimation
        const avgEssais = (minEssais + maxEssais) / 2;
        const totalEssais = Math.round(nbAffaires * avgEssais);
        const estimatedTime = Math.ceil(totalEssais / 10); // ~10 essais par minute

        // Mise à jour de l'affichage
        let timeText = '';
        if (estimatedTime < 1) {
            timeText = '< 1 minute';
        } else if (estimatedTime < 60) {
            timeText = `~${estimatedTime} minute${estimatedTime > 1 ? 's' : ''}`;
        } else {
            const hours = Math.floor(estimatedTime / 60);
            const minutes = estimatedTime % 60;
            timeText = `~${hours}h${minutes > 0 ? minutes + 'm' : ''}`;
        }

        document.getElementById('estimation').textContent =
            `~${totalEssais} essais au total (${timeText})`;
    }

    // Ajouter les événements pour la mise à jour en temps réel
    document.addEventListener('DOMContentLoaded', function () {
        const inputs = ['nb_affaires', 'min_essais', 'max_essais'];
        inputs.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('input', updateEstimation);
                element.addEventListener('change', updateEstimation);
            }
        });

        // Calcul initial
        updateEstimation();
    });
    Résultante

    // Fonctions pour la gestion de la variance
    function setVariance(formType, value) {
        const input = document.getElementById('variance_' + formType);
        if (input) {
            input.value = value;
            // Mettre à jour l'apparence des boutons
            const buttons = document.querySelectorAll(`#variance_controls_${formType} button`);
            buttons.forEach(btn => {
                btn.className = btn.className.replace('bg-blue-200 dark:bg-blue-700', 'bg-blue-100 dark:bg-blue-800');
            });
            event.target.className = event.target.className.replace('bg-blue-100 dark:bg-blue-800', 'bg-blue-200 dark:bg-blue-700');
        }
    }

    function toggleVarianceControls(formType) {
        const checkbox = document.getElementById('enable_variance_' + formType);
        const controls = document.getElementById('variance_controls_' + formType);
        const varianceInput = document.getElementById('variance_' + formType);

        if (checkbox && controls && varianceInput) {
            if (checkbox.checked) {
                controls.classList.remove('hidden');
                varianceInput.disabled = false;
            } else {
                controls.classList.add('hidden');
                varianceInput.disabled = true;
                varianceInput.removeAttribute('name'); // Ne pas envoyer la valeur si désactivé
            }
        }
    }

    // Ajouter une confirmation supplémentaire pour les actions destructives
    document.addEventListener('DOMContentLoaded', function () {
        // Initialiser les contrôles de variance
        ['small', 'medium', 'large', 'custom'].forEach(formType => {
            const checkbox = document.getElementById('enable_variance_' + formType);
            if (checkbox) {
                checkbox.addEventListener('change', () => toggleVarianceControls(formType));
                // Initialiser l'état
                toggleVarianceControls(formType);
            }
        });

        const dangerousForms = document.querySelectorAll('form[method="POST"]');
        dangerousForms.forEach(form => {
            const action = form.querySelector('input[name="action"]').value;
            if (action.includes('cleanup') || action.includes('large')) {
                form.addEventListener('submit', function (e) {
                    const button = form.querySelector('button[type="submit"]');
                    button.disabled = true;
                    button.textContent = 'Traitement en cours...';

                    // Réactiver le bouton après 30 secondes pour éviter les blocages
                    setTimeout(() => {
                        button.disabled = false;
                        button.textContent = button.getAttribute('data-original-text') || 'Réessayer';
                    }, 30000);
                });
            }
        });
    });
</script>

<?php
$pageContent = ob_get_clean();
include 'layout.php';
?>
