<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once(__DIR__ . '/../config/database.php');
require_once(__DIR__ . '/../lib/user.php');
require_once(__DIR__ . '/../lib/affaires.php');
require_once(__DIR__ . '/../lib/essais.php');
require_once(__DIR__ . '/../lib/courbes.php');
require_once(__DIR__ . '/../lib/pv.php');

function sendJSON($data, $code = 200)
{
    http_response_code($code);
    echo json_encode($data);
    exit;
}

function getRequestData()
{
    return json_decode(file_get_contents('php://input'), true);
}
