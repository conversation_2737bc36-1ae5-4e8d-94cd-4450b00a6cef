/**
 * Any CSS included here will be global. The classic template
 * bundles Infima by default. Infima is a CSS framework designed to
 * work well for content-centric websites.
 */

/* You can override the default Infima variables here. */
:root {
  --ifm-color-primary: #2e8555;
  --ifm-color-primary-dark: #29784c;
  --ifm-color-primary-darker: #277148;
  --ifm-color-primary-darkest: #205d3b;
  --ifm-color-primary-light: #33925d;
  --ifm-color-primary-lighter: #359962;
  --ifm-color-primary-lightest: #3cad6e;
  --ifm-code-font-size: 95%;
  --docusaurus-highlighted-code-line-bg: rgba(0, 0, 0, 0.1);
}

/* For readability concerns, you should choose a lighter palette in dark mode. */
[data-theme='dark'] {
  --ifm-color-primary: #25c2a0;
  --ifm-color-primary-dark: #21af90;
  --ifm-color-primary-darker: #1fa588;
  --ifm-color-primary-darkest: #1a8870;
  --ifm-color-primary-light: #29d5b0;
  --ifm-color-primary-lighter: #32d8b4;
  --ifm-color-primary-lightest: #4fddbf;
  --docusaurus-highlighted-code-line-bg: rgba(0, 0, 0, 0.3);
}

/* Custom styles for FluidMotion Labs documentation */
.hero__title {
  font-size: 3rem;
}

.hero__subtitle {
  font-size: 1.5rem;
  margin-bottom: 2rem;
}

/* Role-specific styling */
.role-controleur {
  border-left: 4px solid #ff6b6b;
  padding-left: 1rem;
  background-color: rgba(255, 107, 107, 0.1);
  margin: 1rem 0;
}

.role-operateur {
  border-left: 4px solid #4ecdc4;
  padding-left: 1rem;
  background-color: rgba(78, 205, 196, 0.1);
  margin: 1rem 0;
}

.role-both {
  border-left: 4px solid #45b7d1;
  padding-left: 1rem;
  background-color: rgba(69, 183, 209, 0.1);
  margin: 1rem 0;
}

/* Warning and info boxes */
.warning-box {
  border: 2px solid #ff9800;
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem 0;
  background-color: rgba(255, 152, 0, 0.1);
}

.info-box {
  border: 2px solid #2196f3;
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem 0;
  background-color: rgba(33, 150, 243, 0.1);
}

.success-box {
  border: 2px solid #4caf50;
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem 0;
  background-color: rgba(76, 175, 80, 0.1);
}

/* Step-by-step guides */
.step-number {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  background-color: var(--ifm-color-primary);
  color: white;
  border-radius: 50%;
  text-align: center;
  line-height: 2rem;
  margin-right: 0.5rem;
  font-weight: bold;
}

.step-content {
  margin-left: 2.5rem;
  margin-bottom: 1.5rem;
}

/* Keyboard shortcut styling */
.keyboard-key {
  display: inline-block;
  padding: 0.2rem 0.5rem;
  background-color: #f1f1f1;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-family: monospace;
  font-size: 0.9rem;
  margin: 0 0.2rem;
}

[data-theme='dark'] .keyboard-key {
  background-color: #333;
  border-color: #555;
  color: #fff;
}

/* Feature highlight boxes */
.feature-highlight {
  border: 2px solid var(--ifm-color-primary);
  border-radius: 8px;
  padding: 1.5rem;
  margin: 1.5rem 0;
  background-color: rgba(46, 133, 85, 0.05);
}

.feature-highlight h3 {
  color: var(--ifm-color-primary);
  margin-top: 0;
}

/* Status indicators */
.status-en-cours {
  background-color: #2196f3;
  color: white;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
}

.status-termine {
  background-color: #4caf50;
  color: white;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
}

.status-annule {
  background-color: #f44336;
  color: white;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
}

.status-en-attente {
  background-color: #ff9800;
  color: white;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
}
