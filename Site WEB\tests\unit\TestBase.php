<?php
require_once(__DIR__ . '/../../config/database.php');

abstract class TestBase
{
    protected $test_results = [];
    protected $assertions_count = 0;
    protected $failures_count = 0;

    public function setUp()
    {
        // Configuration de base pour les tests
        $this->test_results = [];
        $this->assertions_count = 0;
        $this->failures_count = 0;
    }

    public function tearDown()
    {
        // Nettoyage après les tests
    }

    public function getResults()
    {
        return [
            'assertions' => $this->assertions_count,
            'failures' => $this->failures_count,
            'success_rate' => $this->assertions_count > 0 ?
                round((($this->assertions_count - $this->failures_count) / $this->assertions_count) * 100, 2) : 0,
            'details' => $this->test_results
        ];
    }

    abstract public function runAllTests();

    protected function assertTrue($condition, $message = '')
    {
        $this->assertions_count++;
        if (!$condition) {
            $this->failures_count++;
            $this->test_results[] = [
                'type' => 'failure',
                'message' => $message ?: 'Assertion failed: expected true',
                'trace' => debug_backtrace()
            ];
            return false;
        }
        return true;
    }

    protected function assertFalse($condition, $message = '')
    {
        $this->assertions_count++;
        if ($condition) {
            $this->failures_count++;
            $this->test_results[] = [
                'type' => 'failure',
                'message' => $message ?: 'Assertion failed: expected false',
                'trace' => debug_backtrace()
            ];
            return false;
        }
        return true;
    }

    protected function assertEquals($expected, $actual, $message = '')
    {
        $this->assertions_count++;
        if ($expected !== $actual) {
            $this->failures_count++;
            $this->test_results[] = [
                'type' => 'failure',
                'message' => $message ?: "Assertion failed: expected '$expected', got '$actual'",
                'trace' => debug_backtrace()
            ];
            return false;
        }
        return true;
    }

    protected function assertNotEquals($expected, $actual, $message = '')
    {
        $this->assertions_count++;
        if ($expected === $actual) {
            $this->failures_count++;
            $this->test_results[] = [
                'type' => 'failure',
                'message' => $message ?: "Assertion failed: expected not '$expected'",
                'trace' => debug_backtrace()
            ];
            return false;
        }
        return true;
    }

    protected function assertNull($value, $message = '')
    {
        $this->assertions_count++;
        if ($value !== null) {
            $this->failures_count++;
            $this->test_results[] = [
                'type' => 'failure',
                'message' => $message ?: 'Assertion failed: expected null',
                'trace' => debug_backtrace()
            ];
            return false;
        }
        return true;
    }

    protected function assertNotNull($value, $message = '')
    {
        $this->assertions_count++;
        if ($value === null) {
            $this->failures_count++;
            $this->test_results[] = [
                'type' => 'failure',
                'message' => $message ?: 'Assertion failed: expected not null',
                'trace' => debug_backtrace()
            ];
            return false;
        }
        return true;
    }

    protected function assertIsArray($value, $message = '')
    {
        $this->assertions_count++;
        if (!is_array($value)) {
            $this->failures_count++;
            $this->test_results[] = [
                'type' => 'failure',
                'message' => $message ?: 'Assertion failed: expected array',
                'trace' => debug_backtrace()
            ];
            return false;
        }
        return true;
    }

    protected function assertGreaterThan($expected, $actual, $message = '')
    {
        $this->assertions_count++;
        if ($actual <= $expected) {
            $this->failures_count++;
            $this->test_results[] = [
                'type' => 'failure',
                'message' => $message ?: "Assertion failed: expected $actual > $expected",
                'trace' => debug_backtrace()
            ];
            return false;
        }
        return true;
    }

    protected function assertLessThanOrEqual($expected, $actual, $message = '')
    {
        $this->assertions_count++;
        if ($actual > $expected) {
            $this->failures_count++;
            $this->test_results[] = [
                'type' => 'failure',
                'message' => $message ?: "Assertion failed: expected $actual <= $expected",
                'trace' => debug_backtrace()
            ];
            return false;
        }
        return true;
    }

    protected function assertContains($needle, $haystack, $message = '')
    {
        $this->assertions_count++;
        if (is_array($haystack)) {
            $found = in_array($needle, $haystack);
        } else {
            $found = strpos($haystack, $needle) !== false;
        }

        if (!$found) {
            $this->failures_count++;
            $this->test_results[] = [
                'type' => 'failure',
                'message' => $message ?: "Assertion failed: haystack does not contain '$needle'",
                'trace' => debug_backtrace()
            ];
            return false;
        }
        return true;
    }

    protected function runTest($testMethod)
    {
        try {
            $start_time = microtime(true);
            $this->$testMethod();
            $end_time = microtime(true);
            $duration = round(($end_time - $start_time) * 1000, 2);

            echo "<div class='test-result success'>";
            echo "<span class='test-name'>✅ $testMethod</span>";
            echo "<span class='test-duration'>({$duration}ms)</span>";
            echo "</div>";

        } catch (Exception $e) {
            $this->failures_count++;
            echo "<div class='test-result failure'>";
            echo "<span class='test-name'>❌ $testMethod</span>";
            echo "<span class='test-error'>Error: " . $e->getMessage() . "</span>";
            echo "</div>";
        }
    }
}
