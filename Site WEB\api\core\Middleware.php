<?php
require_once __DIR__ . '/../auth.php';

abstract class Middleware
{
    protected $next;

    public function __construct(callable $next)
    {
        $this->next = $next;
    }

    abstract public function handle();
}

class AuthMiddleware extends Middleware
{
    private $roles = [];

    public function __construct(callable $next, array $roles = [])
    {
        parent::__construct($next);
        $this->roles = $roles;
    }

    public function handle()
    {
        try {
            $user = Auth::validateToken();

            // Vérification des rôles si spécifiés
            if (!empty($this->roles) && !in_array($user['role'], $this->roles)) {
                $this->error('Permission refusée', 403);
            }

            return call_user_func($this->next, $user);
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
    }

    private function error($message, $code = 401)
    {
        http_response_code($code);
        echo json_encode(['error' => $message]);
        exit;
    }
}
