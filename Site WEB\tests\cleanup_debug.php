<?php
/**
 * Script de nettoyage pour supprimer le script de debug
 * À utiliser après avoir résolu les problèmes de session
 */

session_start();

// Vérifier que l'utilisateur est connecté et est un contrôleur
if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'controleur') {
    die('Accès refusé. Ce script nécessite un utilisateur contrôleur connecté.');
}

echo "<h1>Nettoyage des Scripts de Debug</h1>";

$debugFile = __DIR__ . '/debug_session.php';
$cleanupFile = __FILE__;

if (file_exists($debugFile)) {
    if (unlink($debugFile)) {
        echo "<p style='color: green;'>✅ Script debug_session.php supprimé avec succès.</p>";
    } else {
        echo "<p style='color: red;'>❌ Erreur lors de la suppression de debug_session.php.</p>";
    }
} else {
    echo "<p style='color: orange;'>⚠️ Le script debug_session.php n'existe pas.</p>";
}

echo "<h2>Auto-suppression</h2>";
echo "<p>Ce script va maintenant se supprimer automatiquement...</p>";

// Auto-suppression de ce script
if (unlink($cleanupFile)) {
    echo "<p style='color: green;'>✅ Script de nettoyage supprimé automatiquement.</p>";
} else {
    echo "<p style='color: red;'>❌ Erreur lors de l'auto-suppression. Veuillez supprimer manuellement cleanup_debug.php.</p>";
}

echo "<p><a href='/tests.php'>← Retour aux Tests</a></p>";
?>
