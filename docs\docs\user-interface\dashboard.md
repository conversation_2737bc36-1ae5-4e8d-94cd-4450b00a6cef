# Tableau de Bord

## Vue d'ensemble

Le tableau de bord est la page d'accueil de FluidMotion Labs. Il offre une vue synthétique de l'activité du laboratoire avec des statistiques en temps réel, des indicateurs de performance et un aperçu des tâches en cours.

## Structure du Tableau de Bord

### Zone Supérieure - Indicateurs Principaux

#### Métriques de Performance (Ligne 1)

<div className="feature-highlight">

**Affaires Terminées**
- Nombre total d'affaires avec statut "Terminé"
- Évolution par rapport à la semaine précédente
- Indicateur visuel de tendance (↗️ ↘️)
- Couleur verte pour progression positive

**Essais Terminés**
- Nombre total d'essais avec statut "Terminé"
- Évolution hebdomadaire en pourcentage
- Indicateur de tendance coloré
- Suivi de la productivité des tests

</div>

#### Métriques d'Activité (Ligne 2)

<div className="feature-highlight">

**Affaires en Cours**
- Nombre d'affaires avec statut "En cours"
- Charge de travail actuelle
- Indicateur de capacité

**Essais en Cours**
- Nombre d'essais avec statut "En cours"
- Tests en cours d'exécution
- Planification des ressources

</div>

### Zone Centrale - Analyses Détaillées

#### Statistiques Rapides

<div className="info-box">

**Taux de Réussite des Essais**
- Pourcentage d'essais terminés avec succès
- Barre de progression visuelle
- Indicateur de qualité des processus

**Durée Moyenne des Affaires**
- Temps moyen entre création et dernier essai
- Calculé en jours
- Indicateur d'efficacité opérationnelle

</div>

#### Activité Récente

Liste des 5 dernières activités du système :
- **Nouvelles affaires** créées
- **Nouveaux essais** planifiés
- Horodatage précis (date et heure)
- Icônes différenciées par type d'activité

#### À Faire

<div className="warning-box">

**Alertes et Rappels**
- Affaires en cours depuis plus de 30 jours
- Essais en attente de traitement
- Indicateurs d'attention nécessaire

</div>

### Zone Inférieure - Aperçu des Données

#### Tableau des Dernières Affaires

Affichage des 10 dernières affaires avec :
- **Numéro** d'affaire (identifiant unique)
- **Client** (nom de l'entreprise)
- **Description** (tronquée si trop longue)
- **Date de création**
- **Statut** avec code couleur
- **Créateur** (nom d'utilisateur)

#### Lien vers Vue Complète
- Bouton "Voir toutes les affaires"
- Redirection vers le module Affaires
- Accès rapide aux données complètes

## Indicateurs Visuels

### Codes Couleur des Statuts

#### Affaires
- <span className="status-en-cours">En cours</span> : Bleu - Travail en progression
- <span className="status-termine">Terminé</span> : Vert - Affaire complétée
- <span className="status-annule">Annulé</span> : Rouge - Affaire abandonnée

#### Essais
- <span className="status-en-attente">En attente</span> : Orange - Planifié mais non démarré
- <span className="status-en-cours">En cours</span> : Bleu - Test en exécution
- <span className="status-termine">Terminé</span> : Vert - Test complété
- <span className="status-annule">Annulé</span> : Rouge - Test abandonné

### Indicateurs de Tendance

#### Flèches de Progression
- **↗️ Vert** : Augmentation positive
- **↘️ Rouge** : Diminution
- **→ Gris** : Stabilité (pas de changement)

#### Pourcentages
- Affichage avec 1 décimale maximum
- Suppression des zéros inutiles
- Format compact et lisible

## Calculs et Métriques

### Taux de Réussite des Essais

```
Taux = (Essais avec résultat "Succès" / Total essais terminés) × 100
```

### Durée Moyenne des Affaires

```
Durée = Moyenne des (Date dernier essai - Date création affaire)
```

Pour les affaires sans essai : durée depuis la création jusqu'à maintenant.

### Évolution Hebdomadaire

```
Évolution = ((Semaine actuelle - Semaine précédente) / Semaine précédente) × 100
```

## Actualisation des Données

### Fréquence de Mise à Jour
- **Automatique** : À chaque chargement de page
- **Temps réel** : Reflet de l'état actuel de la base de données
- **Cohérence** : Synchronisation avec tous les modules

### Données Dynamiques
- Statistiques recalculées à chaque visite
- Activité récente mise à jour en continu
- Alertes actualisées selon les nouveaux seuils

## Utilisation Optimale

### Routine Quotidienne

<div className="step-number">1</div>
<div className="step-content">

**Consultation Matinale**
- Vérifiez les indicateurs de performance
- Consultez les alertes "À faire"
- Planifiez les priorités de la journée

</div>

<div className="step-number">2</div>
<div className="step-content">

**Suivi en Cours de Journée**
- Surveillez l'évolution des métriques
- Vérifiez l'activité récente
- Identifiez les goulots d'étranglement

</div>

<div className="step-number">3</div>
<div className="step-content">

**Bilan de Fin de Journée**
- Analysez les tendances hebdomadaires
- Évaluez la productivité
- Préparez le travail du lendemain

</div>

### Interprétation des Métriques

#### Indicateurs Positifs
- **Taux de réussite élevé** (&gt;90%) : Processus maîtrisés
- **Durée moyenne stable** : Efficacité constante
- **Tendances à la hausse** : Croissance d'activité

#### Signaux d'Attention
- **Affaires anciennes** (&gt;30 jours) : Risque de retard
- **Essais en attente** nombreux : Goulot d'étranglement
- **Taux de réussite en baisse** : Problème qualité

## Personnalisation

### Thème d'Affichage
- **Mode clair** : Optimal pour environnement lumineux
- **Mode sombre** : Confort visuel en faible luminosité
- **Basculement** instantané via l'icône thème

### Responsive Design
- **Écrans larges** : Affichage en grille 2×2 pour les métriques
- **Écrans moyens** : Adaptation automatique des colonnes
- **Écrans petits** : Empilement vertical des éléments

## Navigation depuis le Tableau de Bord

### Accès Rapide
- **Clic sur métriques** : Accès aux modules correspondants
- **Liens dans activité récente** : Navigation contextuelle
- **Bouton "Voir toutes les affaires"** : Module Affaires complet

### Raccourcis Utiles
- <kbd>Alt</kbd> + <kbd>A</kbd> : Accès direct aux Affaires
- <kbd>Alt</kbd> + <kbd>E</kbd> : Accès direct aux Essais
- <kbd>Alt</kbd> + <kbd>P</kbd> : Accès direct aux PV

---

:::tip Utilisation Efficace
Consultez le tableau de bord en début de journée pour avoir une vue d'ensemble de l'activité et identifier les priorités.
:::

:::info Métriques Personnalisées
Les seuils d'alerte (30 jours pour les affaires anciennes) peuvent être ajustés selon les besoins de votre laboratoire.
:::
