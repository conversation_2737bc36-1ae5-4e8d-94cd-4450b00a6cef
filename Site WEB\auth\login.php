<?php
session_start();
require_once(__DIR__ . '/../lib/user.php');

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';

    $user = User::authenticate($username, $password);
    if ($user) {
        $_SESSION['user'] = $user;

        if (!empty($_POST['remember'])) {
            setcookie('authentication', $username, time() + (30 * 24 * 60 * 60), '/');
        } else {
            setcookie('authentication', '', time() - 3600, '/');
        }
        header('Location: /index.php');
        exit;
    } else {
        $error = "Identifiants invalides";
    }
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connexion - FluidMotion Labs</title>
    <link rel="icon" href="/img/logo.png" type="image/png">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.3.0/flowbite.min.css" rel="stylesheet"/>
    <style>
        body {
            background-repeat: no-repeat;
            background-position: center center;
            background-size: cover;
            background-attachment: fixed;
        }

        .login-container {
            background-color: rgba(255, 255, 255, 0.98);
        }

        .dark .login-container {
            background-color: rgba(31, 41, 55, 0.98);
        }
    </style>
</head>
<body class="bg-gray-50 dark:bg-gray-900">
<div class="flex flex-col items-center justify-center px-6 mx-auto h-screen">
    <div class="login-container w-full max-w-xl p-6 space-y-8 sm:p-8 rounded-lg shadow-lg">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white text-center">
            FluidMotion Labs
        </h2>
        <h3 class="text-xl text-gray-700 dark:text-gray-300 text-center mb-6">
            Connectez-vous à votre compte
        </h3>
        <?php if (isset($error)): ?>
            <div class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400"
                 role="alert">
                <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>
        <form class="mt-8 space-y-6" action="login.php" method="POST">
            <div>
                <label for="username" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Votre nom
                    d'utilisateur</label>
                <input type="text" name="username" id="username"
                       class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                       placeholder="Nom d'utilisateur" required>
            </div>
            <div>
                <label for="password" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Votre mot de
                    passe</label>
                <input type="password" name="password" id="password" placeholder="••••••••"
                       class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                       required>
            </div>
            <div class="flex items-center justify-between">
                <div class="flex items-start">
                    <div class="flex items-center h-5">
                        <input id="remember" aria-describedby="remember" name="remember" type="checkbox"
                               class="w-4 h-4 border-gray-300 rounded bg-gray-50 focus:ring-3 focus:ring-primary-300 dark:focus:ring-primary-600 dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600">
                    </div>
                    <div class="ml-3 text-sm">
                        <label for="remember" class="font-medium text-gray-900 dark:text-white">Se souvenir de
                            moi</label>
                    </div>
                </div>
                <button type="submit"
                        class="px-5 py-2 text-base font-medium text-center text-white bg-black rounded-lg hover:bg-gray-800 focus:ring-4 focus:ring-gray-300 transition-all duration-200 shadow-lg dark:bg-gray-800 dark:hover:bg-gray-700 dark:focus:ring-gray-700">
                    Se connecter
                </button>
            </div>
        </form>
    </div>
</div>
<script src="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.3.0/flowbite.min.js"></script>
</body>
</html>