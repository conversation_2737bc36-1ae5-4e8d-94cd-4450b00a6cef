# FluidMotion - Guide de Développement

Ce guide explique comment configurer et utiliser l'environnement de développement pour le système de gestion d'essais hydrauliques FluidMotion.

## 🚀 Démarrage Rapide

### Prérequis

- **Docker Desktop** installé et en cours d'exécution
- **Git** pour cloner le repository
- **VS Code** (recommandé) avec les extensions Docker et PHP

### Démarrage de l'environnement

#### Linux/macOS
```bash
# Cloner le repository
git clone <repository-url>
cd Projet-BTS

# Démarrer l'environnement de développement
./dev-start.sh
```

#### Windows
```cmd
# Cloner le repository
git clone <repository-url>
cd Projet-BTS

# Démarrer l'environnement de développement
dev-start.bat
```

## 🔧 Configuration de Développement

### Structure des Fichiers

```
Projet-BTS/
├── Site WEB/                 # Code source de l'application (monté dynamiquement)
│   ├── Dockerfile.dev        # Dockerfile pour le développement
│   ├── *.php                 # Fichiers PHP de l'application
│   └── ...
├── docker-compose.dev.yml    # Configuration Docker pour le développement
├── .env.dev                  # Variables d'environnement de développement
├── dev-start.sh             # Script de démarrage (Linux/macOS)
├── dev-start.bat            # Script de démarrage (Windows)
└── README_DEV.md            # Ce fichier
```

### Montage Dynamique

Le répertoire `Site WEB` est monté dynamiquement dans le conteneur Docker, ce qui signifie que :

- ✅ **Hot-reload** : Les modifications de fichiers sont immédiatement visibles
- ✅ **Pas de rebuild** : Aucune reconstruction d'image nécessaire
- ✅ **Développement rapide** : Cycle de développement accéléré

### Services Disponibles

| Service | URL | Description |
|---------|-----|-------------|
| **Application Web** | http://localhost:8080 | Interface principale FluidMotion |
| **PhpMyAdmin** | http://localhost:8081 | Administration de base de données |
| **Documentation** | http://localhost:3000 | Documentation Docusaurus |

## 🛠️ Commandes de Développement

### Scripts de Gestion

```bash
# Démarrer l'environnement
./dev-start.sh start

# Arrêter l'environnement
./dev-start.sh stop

# Redémarrer l'environnement
./dev-start.sh restart

# Voir les logs en temps réel
./dev-start.sh logs

# Ouvrir un shell dans le conteneur web
./dev-start.sh shell

# Nettoyer complètement l'environnement
./dev-start.sh clean
```

### Commandes Docker Directes

```bash
# Voir l'état des conteneurs
docker-compose -f docker-compose.dev.yml ps

# Voir les logs
docker-compose -f docker-compose.dev.yml logs -f web

# Exécuter une commande dans le conteneur web
docker-compose -f docker-compose.dev.yml exec web php -v

# Installer des dépendances Composer
docker-compose -f docker-compose.dev.yml exec web composer install
```

## 🐛 Débogage avec Xdebug

### Configuration VS Code

1. Installer l'extension **PHP Debug**
2. Créer `.vscode/launch.json` :

```json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Listen for Xdebug",
            "type": "php",
            "request": "launch",
            "port": 9003,
            "pathMappings": {
                "/var/www/html": "${workspaceFolder}/Site WEB"
            }
        }
    ]
}
```

3. Placer des points d'arrêt dans le code PHP
4. Démarrer le débogage (F5)

### Configuration Xdebug

- **Port** : 9003
- **Mode** : develop,debug,coverage
- **Client Host** : host.docker.internal
- **IDE Key** : VSCODE

## 📁 Gestion des Permissions

### Linux/macOS

Les permissions sont gérées automatiquement via les variables `UID` et `GID` dans `.env.dev`.

### Windows

Les permissions sont gérées par Docker Desktop automatiquement.

### Répertoires Importants

- `Site WEB/backups/` : Sauvegardes de base de données
- `Site WEB/pdf_exports/` : Exports PDF générés
- `Site WEB/temp/` : Fichiers temporaires
- `Site WEB/vendor/` : Dépendances Composer

## 🔄 Workflow de Développement

### 1. Modification du Code

1. Modifier les fichiers dans `Site WEB/`
2. Les changements sont immédiatement visibles à http://localhost:8080
3. Aucun redémarrage nécessaire

### 2. Base de Données

- **PhpMyAdmin** : http://localhost:8081
- **Credentials** : voir `.env.dev`
- **Sauvegardes** : Automatiques dans `Site WEB/backups/`

### 3. Tests

```bash
# Exécuter les tests
./dev-start.sh shell
cd /var/www/html
php tests/test_runner.php
```

### 4. Logs

```bash
# Logs de l'application
./dev-start.sh logs

# Logs PHP spécifiques
docker-compose -f docker-compose.dev.yml exec web tail -f /var/log/php_errors.log
```

## 🚨 Dépannage

### Problèmes Courants

#### Port déjà utilisé
```bash
# Changer les ports dans .env.dev
WEB_PORT=8081
PHPMYADMIN_PORT=8082
```

#### Permissions de fichiers
```bash
# Réinitialiser les permissions
./dev-start.sh clean
./dev-start.sh start
```

#### Base de données inaccessible
```bash
# Vérifier l'état des conteneurs
docker-compose -f docker-compose.dev.yml ps

# Redémarrer la base de données
docker-compose -f docker-compose.dev.yml restart db
```

#### Xdebug ne fonctionne pas
1. Vérifier que VS Code écoute sur le port 9003
2. Vérifier les path mappings
3. Redémarrer le conteneur web

### Nettoyage Complet

```bash
# Arrêter et supprimer tout
./dev-start.sh clean

# Supprimer les images Docker
docker rmi $(docker images -q ghcr.io/laprovidenceamiens/ciel2_bancdetest-dev)

# Redémarrer proprement
./dev-start.sh start
```

## 📝 Variables d'Environnement

### Fichier `.env.dev`

Les principales variables configurables :

```bash
# Application
APP_ENV=development
APP_DEBUG=true
APP_URL=http://localhost:8080

# Base de données
DB_NAME=verins_db
DB_USER=verins_user
DB_PASSWORD=dev_password_123

# Ports
WEB_PORT=8080
PHPMYADMIN_PORT=8081

# PHP
PHP_MEMORY_LIMIT=512M
PHP_UPLOAD_MAX_FILESIZE=100M

# Xdebug
XDEBUG_MODE=develop,debug
XDEBUG_CLIENT_PORT=9003
```

## 🔒 Sécurité en Développement

⚠️ **ATTENTION** : Les configurations de développement ne sont **PAS** sécurisées pour la production :

- Mots de passe par défaut
- Xdebug activé
- Erreurs PHP affichées
- Permissions de fichiers flexibles
- CORS ouvert

**Ne jamais utiliser ces configurations en production !**

## 📚 Ressources Supplémentaires

- [Documentation Docker](https://docs.docker.com/)
- [Guide PHP avec Docker](https://docs.docker.com/samples/php/)
- [Configuration Xdebug](https://xdebug.org/docs/remote)
- [VS Code PHP Debug](https://marketplace.visualstudio.com/items?itemName=xdebug.php-debug)
