/**
 * CSS files with the .module.css suffix will be treated as CSS modules
 * and scoped locally.
 */

.heroBanner {
  padding: 4rem 0;
  text-align: center;
  position: relative;
  overflow: hidden;
}

@media screen and (max-width: 996px) {
  .heroBanner {
    padding: 2rem;
  }
}

.buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-top: 2rem;
}

@media screen and (max-width: 768px) {
  .buttons {
    flex-direction: column;
    gap: 0.5rem;
  }
}

/* Hero section styling */
.heroBanner h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.heroBanner p {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

/* Button styling */
.buttons .button {
  font-size: 1.1rem;
  padding: 0.75rem 2rem;
  border-radius: 8px;
  text-decoration: none;
  transition: all 0.3s ease;
}

.buttons .button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Responsive adjustments */
@media screen and (max-width: 996px) {
  .heroBanner h1 {
    font-size: 2.5rem;
  }
  
  .heroBanner p {
    font-size: 1.1rem;
  }
}

@media screen and (max-width: 768px) {
  .heroBanner h1 {
    font-size: 2rem;
  }
  
  .heroBanner p {
    font-size: 1rem;
    padding: 0 1rem;
  }
  
  .buttons .button {
    width: 100%;
    max-width: 300px;
  }
}
