<?php
/**
 * Test rapide pour vérifier la disponibilité de mPDF
 */

session_start();

// Vérifier que l'utilisateur est connecté et est un contrôleur
if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'controleur') {
    die('Accès refusé. Ce script nécessite un utilisateur contrôleur connecté.');
}

echo "<h1>Test Rapide mPDF</h1>";

echo "<h2>1. Vérification de l'autoloader Composer</h2>";
$autoloaderPath = __DIR__ . '/../vendor/autoload.php';
if (file_exists($autoloaderPath)) {
    echo "✅ Autoloader trouvé: <code>$autoloaderPath</code><br>";
    require_once($autoloaderPath);
    echo "✅ Autoloader chargé avec succès<br>";
} else {
    echo "❌ Autoloader non trouvé: <code>$autoloaderPath</code><br>";
}

echo "<h2>2. Vérification de la classe mPDF</h2>";
if (class_exists('\Mpdf\Mpdf')) {
    echo "✅ Classe \\Mpdf\\Mpdf disponible<br>";

    // Essayer de créer une instance
    try {
        $tempDir = __DIR__ . '/../temp/mpdf';
        if (!is_dir($tempDir)) {
            mkdir($tempDir, 0755, true);
        }

        $mpdf = new \Mpdf\Mpdf([
            'tempDir' => $tempDir,
            'mode' => 'utf-8',
            'format' => 'A4'
        ]);
        echo "✅ Instance mPDF créée avec succès<br>";

        // Obtenir la version
        $reflection = new ReflectionClass($mpdf);
        $version = $reflection->getConstant('VERSION') ?? 'Version inconnue';
        echo "✅ Version mPDF: $version<br>";

    } catch (Exception $e) {
        echo "❌ Erreur lors de la création de l'instance mPDF: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ Classe \\Mpdf\\Mpdf non disponible<br>";
}

echo "<h2>3. Vérification des extensions PHP requises</h2>";
$requiredExtensions = ['gd', 'mbstring', 'curl', 'xml'];
foreach ($requiredExtensions as $ext) {
    if (extension_loaded($ext)) {
        echo "✅ Extension $ext: Chargée<br>";
    } else {
        echo "❌ Extension $ext: Non chargée<br>";
    }
}

echo "<h2>4. Vérification du dossier vendor</h2>";
$vendorDir = __DIR__ . '/../vendor';
if (is_dir($vendorDir)) {
    echo "✅ Dossier vendor existe: <code>$vendorDir</code><br>";

    $mpdfDir = $vendorDir . '/mpdf/mpdf';
    if (is_dir($mpdfDir)) {
        echo "✅ Dossier mPDF existe: <code>$mpdfDir</code><br>";
    } else {
        echo "❌ Dossier mPDF non trouvé: <code>$mpdfDir</code><br>";
    }

    $composerLock = __DIR__ . '/../composer.lock';
    if (file_exists($composerLock)) {
        echo "✅ Fichier composer.lock existe<br>";

        $lockContent = file_get_contents($composerLock);
        if (strpos($lockContent, 'mpdf/mpdf') !== false) {
            echo "✅ mPDF trouvé dans composer.lock<br>";
        } else {
            echo "❌ mPDF non trouvé dans composer.lock<br>";
        }
    } else {
        echo "⚠️ Fichier composer.lock non trouvé<br>";
    }
} else {
    echo "❌ Dossier vendor non trouvé: <code>$vendorDir</code><br>";
}

echo "<h2>5. Test de chargement direct</h2>";
// Test de chargement sans autoloader
$mpdfMainFile = __DIR__ . '/../vendor/mpdf/mpdf/src/Mpdf.php';
if (file_exists($mpdfMainFile)) {
    echo "✅ Fichier principal mPDF trouvé: <code>$mpdfMainFile</code><br>";
} else {
    echo "❌ Fichier principal mPDF non trouvé: <code>$mpdfMainFile</code><br>";
}

echo "<h2>6. Informations de débogage</h2>";
echo "<ul>";
echo "<li>Répertoire actuel: " . __DIR__ . "</li>";
echo "<li>Répertoire parent: " . dirname(__DIR__) . "</li>";
echo "<li>Include path: " . get_include_path() . "</li>";
echo "<li>Classes chargées contenant 'Mpdf': ";
$loadedClasses = get_declared_classes();
$mpdfClasses = array_filter($loadedClasses, function ($class) {
    return strpos($class, 'Mpdf') !== false;
});
echo empty($mpdfClasses) ? 'Aucune' : implode(', ', $mpdfClasses);
echo "</li>";
echo "</ul>";

echo "<h2>Conclusion</h2>";
if (class_exists('\Mpdf\Mpdf')) {
    echo "<p style='color: green; font-weight: bold;'>✅ mPDF est correctement installé et accessible !</p>";
    echo "<p><a href='/tests/test_mpdf.php'>Procéder au test complet mPDF</a></p>";
} else {
    echo "<p style='color: red; font-weight: bold;'>❌ mPDF n'est pas accessible. Vérifiez l'installation.</p>";
    echo "<p><a href='/tests/install_mpdf.php'>Installer mPDF</a></p>";
}

echo "<p><a href='/tests.php'>← Retour aux Tests</a></p>";
?>

<style>
    body {
        font-family: Arial, sans-serif;
        margin: 20px;
    }

    h1 {
        color: #2563eb;
    }

    h2 {
        color: #1e40af;
        border-bottom: 1px solid #e5e7eb;
        padding-bottom: 5px;
    }

    code {
        background-color: #f3f4f6;
        padding: 2px 4px;
        border-radius: 3px;
    }

    ul {
        margin-left: 20px;
    }

    a {
        color: #2563eb;
        text-decoration: none;
    }

    a:hover {
        text-decoration: underline;
    }
</style>
