<?php
require_once(__DIR__ . '/../config/database.php');

class Backup
{
    /**
     * Créer une sauvegarde complète de la base de données
     * @param string $backupPath Chemin où sauvegarder le fichier
     * @return array Résultat de l'opération avec status et message
     */
    public static function createBackup($backupPath = null)
    {
        try {
            // Générer un nom de fichier unique si non fourni
            if (!$backupPath) {
                $timestamp = date('Y-m-d_H-i-s');
                $backupPath = __DIR__ . "/../backups/backup_verins_db_{$timestamp}.sql";
            }

            // Créer le dossier de sauvegarde s'il n'existe pas
            $backupDir = dirname($backupPath);
            if (!is_dir($backupDir)) {
                if (!mkdir($backupDir, 0755, true)) {
                    return [
                        'success' => false,
                        'message' => 'Impossible de créer le dossier de sauvegarde'
                    ];
                }
            }

            // Vérifier l'espace disque disponible
            $spaceCheck = self::checkDiskSpace($backupDir);
            if (!$spaceCheck['success']) {
                return $spaceCheck;
            }

            // Obtenir les informations de connexion à la base de données
            $dbConfig = self::getDatabaseConfig();

            // Construire la commande mysqldump
            $command = sprintf(
                'mysqldump --host=%s --user=%s --password=%s --single-transaction --routines --triggers %s > %s',
                escapeshellarg($dbConfig['host']),
                escapeshellarg($dbConfig['user']),
                escapeshellarg($dbConfig['password']),
                escapeshellarg($dbConfig['database']),
                escapeshellarg($backupPath)
            );

            // Exécuter la commande
            $output = [];
            $returnCode = 0;
            exec($command . ' 2>&1', $output, $returnCode);

            if ($returnCode !== 0) {
                return [
                    'success' => false,
                    'message' => 'Erreur lors de la création de la sauvegarde: ' . implode("\n", $output)
                ];
            }

            // Vérifier que le fichier a été créé et n'est pas vide
            if (!file_exists($backupPath) || filesize($backupPath) === 0) {
                return [
                    'success' => false,
                    'message' => 'Le fichier de sauvegarde n\'a pas été créé correctement'
                ];
            }

            return [
                'success' => true,
                'message' => 'Sauvegarde créée avec succès',
                'file_path' => $backupPath,
                'file_size' => filesize($backupPath)
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Erreur lors de la sauvegarde: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Vérifier l'espace disque disponible
     * @param string $path Chemin à vérifier
     * @param int $minSpaceGB Espace minimum requis en GB
     * @return array Résultat de la vérification
     */
    public static function checkDiskSpace($path, $minSpaceGB = 1)
    {
        try {
            $freeBytes = disk_free_space($path);
            $totalBytes = disk_total_space($path);

            if ($freeBytes === false || $totalBytes === false) {
                return [
                    'success' => false,
                    'message' => 'Impossible de vérifier l\'espace disque'
                ];
            }

            $minSpaceBytes = $minSpaceGB * 1024 * 1024 * 1024;

            if ($freeBytes < $minSpaceBytes) {
                return [
                    'success' => false,
                    'message' => sprintf(
                        'Espace disque insuffisant. Requis: %s GB, Disponible: %s GB',
                        $minSpaceGB,
                        round($freeBytes / (1024 * 1024 * 1024), 2)
                    )
                ];
            }

            return [
                'success' => true,
                'free_space_gb' => round($freeBytes / (1024 * 1024 * 1024), 2),
                'total_space_gb' => round($totalBytes / (1024 * 1024 * 1024), 2)
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Erreur lors de la vérification de l\'espace disque: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Obtenir la configuration de la base de données
     * @return array Configuration de la base de données
     */
    private static function getDatabaseConfig()
    {
        // Utiliser reflection pour accéder aux propriétés privées de Database
        $db = Database::getInstance();
        $reflection = new ReflectionClass($db);

        $hostProperty = $reflection->getProperty('db_host');
        $hostProperty->setAccessible(true);
        $host = $hostProperty->getValue($db);

        $nameProperty = $reflection->getProperty('db_name');
        $nameProperty->setAccessible(true);
        $database = $nameProperty->getValue($db);

        $userProperty = $reflection->getProperty('db_user');
        $userProperty->setAccessible(true);
        $user = $userProperty->getValue($db);

        $passProperty = $reflection->getProperty('db_pass');
        $passProperty->setAccessible(true);
        $password = $passProperty->getValue($db);

        return [
            'host' => $host,
            'database' => $database,
            'user' => $user,
            'password' => $password
        ];
    }

    /**
     * Restaurer la base de données à partir d'un fichier de sauvegarde
     * @param string $backupFile Chemin vers le fichier de sauvegarde
     * @param bool $confirmed Confirmation de l'utilisateur
     * @return array Résultat de l'opération
     */
    public static function restoreBackup($backupFile, $confirmed = false)
    {
        try {
            if (!$confirmed) {
                return [
                    'success' => false,
                    'message' => 'La restauration nécessite une confirmation explicite'
                ];
            }

            // Vérifier que le fichier existe
            if (!file_exists($backupFile)) {
                return [
                    'success' => false,
                    'message' => 'Le fichier de sauvegarde n\'existe pas'
                ];
            }

            // Vérifier l'espace disque
            $spaceCheck = self::checkDiskSpace(dirname($backupFile));
            if (!$spaceCheck['success']) {
                return $spaceCheck;
            }

            // Obtenir les informations de connexion
            $dbConfig = self::getDatabaseConfig();

            // Construire la commande mysql pour la restauration
            $command = sprintf(
                'mysql --host=%s --user=%s --password=%s %s < %s',
                escapeshellarg($dbConfig['host']),
                escapeshellarg($dbConfig['user']),
                escapeshellarg($dbConfig['password']),
                escapeshellarg($dbConfig['database']),
                escapeshellarg($backupFile)
            );

            // Exécuter la commande
            $output = [];
            $returnCode = 0;
            exec($command . ' 2>&1', $output, $returnCode);

            if ($returnCode !== 0) {
                return [
                    'success' => false,
                    'message' => 'Erreur lors de la restauration: ' . implode("\n", $output)
                ];
            }

            return [
                'success' => true,
                'message' => 'Base de données restaurée avec succès'
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Erreur lors de la restauration: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Initialiser une base de données vierge (F21)
     * @param bool $confirmed Confirmation de l'utilisateur
     * @return array Résultat de l'opération
     */
    public static function initializeCleanDatabase($confirmed = false)
    {
        try {
            if (!$confirmed) {
                return [
                    'success' => false,
                    'message' => 'L\'initialisation nécessite une confirmation explicite'
                ];
            }

            // Lire le schéma de base
            $schemaFile = __DIR__ . '/../config/schema.sql';
            if (!file_exists($schemaFile)) {
                return [
                    'success' => false,
                    'message' => 'Fichier de schéma introuvable'
                ];
            }

            $schema = file_get_contents($schemaFile);
            if ($schema === false) {
                return [
                    'success' => false,
                    'message' => 'Impossible de lire le fichier de schéma'
                ];
            }

            // Obtenir les informations de connexion
            $dbConfig = self::getDatabaseConfig();

            // Construire la commande pour exécuter le schéma
            $tempFile = tempnam(sys_get_temp_dir(), 'schema_');
            file_put_contents($tempFile, $schema);

            $command = sprintf(
                'mysql --host=%s --user=%s --password=%s < %s',
                escapeshellarg($dbConfig['host']),
                escapeshellarg($dbConfig['user']),
                escapeshellarg($dbConfig['password']),
                escapeshellarg($tempFile)
            );

            // Exécuter la commande
            $output = [];
            $returnCode = 0;
            exec($command . ' 2>&1', $output, $returnCode);

            // Nettoyer le fichier temporaire
            unlink($tempFile);

            if ($returnCode !== 0) {
                return [
                    'success' => false,
                    'message' => 'Erreur lors de l\'initialisation: ' . implode("\n", $output)
                ];
            }

            return [
                'success' => true,
                'message' => 'Base de données initialisée avec succès'
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Erreur lors de l\'initialisation: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Lister les fichiers de sauvegarde disponibles
     * @return array Liste des sauvegardes
     */
    public static function listBackups()
    {
        try {
            $backupDir = __DIR__ . '/../backups';
            if (!is_dir($backupDir)) {
                return [];
            }

            $backups = [];
            $files = glob($backupDir . '/backup_verins_db_*.sql');

            foreach ($files as $file) {
                $backups[] = [
                    'filename' => basename($file),
                    'path' => $file,
                    'size' => filesize($file),
                    'created' => filemtime($file),
                    'created_formatted' => date('Y-m-d H:i:s', filemtime($file))
                ];
            }

            // Trier par date de création (plus récent en premier)
            usort($backups, function ($a, $b) {
                return $b['created'] - $a['created'];
            });

            return $backups;

        } catch (Exception $e) {
            return [];
        }
    }

    private static function getDb()
    {
        return Database::getInstance()->getConnection();
    }
}
