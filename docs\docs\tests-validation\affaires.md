---
sidebar_position: 3
title: Tests de Gestion des Affaires
description: Tests de validation du module de gestion des affaires
keywords: [affaires, CRUD, recherche, tags, historique]
---

# Tests de Gestion des Affaires

Cette section présente les tests de validation du module de gestion des affaires, fonctionnalité centrale de FluidMotion Labs.

## 🎯 Objectifs des Tests

- Valider les opérations CRUD (Créer, Lire, Modifier, Supprimer) sur les affaires
- Vérifier la gestion des tags et la catégorisation
- Contrôler l'historique des modifications et la traçabilité
- Tester les fonctionnalités de recherche et de filtrage

## 📊 Vue d'Ensemble

| **Module** | **Gestion des Affaires** |
|------------|--------------------------|
| **Nombre de tests** | **5 tests** |
| **Criticité** | **Critique** |
| **Couverture** | **100%** |
| **Profils concernés** | **Contrôleur + Opérateur** |

## 🧪 Tests Détaillés

### AFF-001 : Création d'Affaire Complète

| **Aspect** | **Détail** |
|------------|------------|
| **Objectif** | Valider la création d'une nouvelle affaire avec tous les champs |
| **Préconditions** | - Utilisateur connecté (contrôleur ou opérateur)<br />- Accès au module Affaires |
| **Étapes de Test** | 1. Naviguer vers "Gestion des Affaires"<br />2. Cliquer sur "Nouvelle Affaire"<br />3. Remplir : Numéro "AFF-TEST-001", Client "Société Test SARL"<br />4. Description "Essais hydrauliques pour vérins industriels"<br />5. Tags "hydraulique, vérin, test"<br />6. Cliquer sur "Créer l'affaire" |
| **Résultats Attendus** | - Affaire créée avec succès<br />- Message de confirmation affiché<br />- Affaire visible dans la liste avec statut "En cours"<br />- Tags correctement associés |
| **Critères de Réussite** | ✅ Affaire créée et visible |

### AFF-002 : Modification d'Affaire avec Historique

| **Aspect** | **Détail** |
|------------|------------|
| **Objectif** | Valider la modification d'affaire et la traçabilité |
| **Préconditions** | - Affaire AFF-TEST-001 existante<br />- Utilisateur connecté |
| **Étapes de Test** | 1. Sélectionner l'affaire AFF-TEST-001<br />2. Cliquer sur "Modifier"<br />3. Modifier la description : "...Phase 2"<br />4. Ajouter un tag : "phase2"<br />5. Sauvegarder les modifications<br />6. Consulter l'historique des modifications |
| **Résultats Attendus** | - Modifications sauvegardées<br />- Historique créé avec détail des changements<br />- Horodatage et utilisateur enregistrés |
| **Critères de Réussite** | ✅ Modification tracée dans l'historique |

### AFF-003 : Validation Numéro Unique

| **Aspect** | **Détail** |
|------------|------------|
| **Objectif** | Valider l'unicité des numéros d'affaire |
| **Préconditions** | - Affaire AFF-TEST-001 existante<br />- Utilisateur connecté |
| **Étapes de Test** | 1. Naviguer vers "Nouvelle Affaire"<br />2. Remplir le formulaire avec le numéro "AFF-TEST-001" (déjà existant)<br />3. Compléter les autres champs<br />4. Tenter de créer l'affaire |
| **Résultats Attendus** | - Message d'erreur indiquant que le numéro existe déjà<br />- Création de l'affaire refusée<br />- Formulaire reste affiché pour correction |
| **Critères de Réussite** | ✅ Contrainte d'unicité respectée |

### AFF-004 : Suppression d'Affaire avec Vérification

| **Aspect** | **Détail** |
|------------|------------|
| **Objectif** | Valider la suppression d'affaire avec vérification des dépendances |
| **Préconditions** | - Affaire avec essais associés<br />- Utilisateur connecté |
| **Étapes de Test** | 1. Sélectionner une affaire avec essais<br />2. Cliquer sur "Supprimer"<br />3. Vérifier le message d'avertissement<br />4. Confirmer ou annuler la suppression |
| **Résultats Attendus** | - Message d'avertissement sur les dépendances<br />- Suppression en cascade si confirmée<br />- Données liées supprimées correctement |
| **Critères de Réussite** | ✅ Suppression sécurisée avec vérification |

### AFF-005 : Recherche et Filtrage des Affaires

| **Aspect** | **Détail** |
|------------|------------|
| **Objectif** | Valider la recherche et le filtrage des affaires |
| **Préconditions** | - Plusieurs affaires avec différents statuts et tags |
| **Étapes de Test** | 1. Utiliser la barre de recherche<br />2. Filtrer par statut<br />3. Filtrer par tags<br />4. Combiner plusieurs filtres<br />5. Vérifier la pagination |
| **Résultats Attendus** | - Résultats de recherche pertinents<br />- Filtres appliqués correctement<br />- Pagination fonctionnelle<br />- Compteurs mis à jour |
| **Critères de Réussite** | ✅ Recherche et filtrage opérationnels |

## 🔧 Données de Test

### Affaires de Test Recommandées

| **Numéro** | **Client** | **Description** | **Tags** | **Statut** |
|------------|------------|-----------------|----------|------------|
| **AFF-TEST-001** | Société Test SARL | Essais hydrauliques pour vérins industriels | hydraulique, vérin, test | En cours |
| **AFF-TEST-002** | Industrie ABC | Tests de performance pompes | hydraulique, pompe, performance | Terminé |
| **AFF-TEST-003** | Client XYZ | Validation système complet | système, validation, complet | En attente |

### Tags de Test

| **Tag** | **Description** | **Utilisation** |
|---------|-----------------|-----------------|
| **hydraulique** | Systèmes hydrauliques | Catégorie principale |
| **vérin** | Vérins hydrauliques | Type d'équipement |
| **pompe** | Pompes hydrauliques | Type d'équipement |
| **test** | Phase de test | Statut du projet |
| **performance** | Tests de performance | Type de test |
| **validation** | Phase de validation | Statut du projet |

## 🎭 Tests par Profil

### 👨‍💼 Tests Contrôleur
- **AFF-001** : Création d'affaire complète ✅
- **AFF-002** : Modification avec historique ✅
- **AFF-003** : Validation contraintes ✅
- **AFF-004** : Suppression sécurisée ✅
- **AFF-005** : Recherche et filtrage ✅

### 👨‍🔧 Tests Opérateur
- **AFF-001** : Création d'affaire complète ✅
- **AFF-002** : Modification avec historique ✅
- **AFF-003** : Validation contraintes ✅
- **AFF-005** : Recherche et filtrage ✅

:::info Permissions Opérateur
Les opérateurs peuvent avoir des restrictions sur la suppression d'affaires selon la configuration système.
:::

## 🚨 Points de Vigilance

### Intégrité des Données
- Vérifier que les numéros d'affaire sont uniques
- S'assurer que les relations avec les essais sont préservées
- Contrôler que l'historique est complet et cohérent

### Performance
- La création d'affaire doit s'effectuer en moins de 2 secondes
- La recherche doit retourner des résultats en moins de 1 seconde
- La pagination doit être fluide même avec de nombreuses affaires

### Ergonomie
- Les formulaires doivent avoir une validation en temps réel
- Les messages d'erreur doivent être explicites
- L'auto-complétion des tags doit être fonctionnelle

## 📋 Checklist de Validation

### Avant les Tests
- [ ] Utilisateur connecté avec les bonnes permissions
- [ ] Module Affaires accessible
- [ ] Données de test préparées
- [ ] Tags de test configurés

### Pendant les Tests
- [ ] Vérifier les messages de confirmation
- [ ] Contrôler l'affichage des listes
- [ ] Tester les filtres et la recherche
- [ ] Valider l'historique des modifications

### Après les Tests
- [ ] Nettoyer les affaires de test
- [ ] Vérifier l'intégrité de la base
- [ ] Documenter les anomalies
- [ ] Valider les performances

## 🔗 Liens Connexes

- [**Tests des Essais**](./essais) - Module dépendant des affaires
- [**Tests des PV**](./pv) - Génération de rapports d'affaires
- [**Tests de l'Interface**](./interface) - Ergonomie des formulaires
- [**Tests de Performance**](./performance) - Optimisation des requêtes

---

:::tip Conseil
Créez toujours des affaires de test avec des données réalistes pour valider le comportement en conditions réelles d'utilisation.
:::

:::warning Attention
Lors des tests de suppression, assurez-vous de travailler sur des données de test uniquement pour éviter la perte d'informations importantes.
:::

:::info Navigation
**Précédent** : [Tests d'Authentification](./authentification)  
**Suivant** : [Tests de Gestion des Essais](./essais)
:::
