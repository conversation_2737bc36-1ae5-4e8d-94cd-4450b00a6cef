<?php
require_once(__DIR__ . '/../lib/objdiff.php');

class ObjDiff
{
    /**
     * Génère un tableau des différences entre deux objets
     * @param mixed $obj1 Premier objet à comparer
     * @param mixed $obj2 Deuxième objet à comparer
     * @param int $recordId ID de l'enregistrement (affaire_id, essai_id ou pv_id)
     * @param int $userId ID de l'utilisateur qui fait la modification
     * @return array Tableau des changements au format JSON
     */
    public static function compareForHistory($obj1, $obj2, int $recordId, int $userId): array
    {
        $changes = [];

        // Convertir les objets en tableaux pour faciliter la comparaison
        $array1 = (array)$obj1;
        $array2 = (array)$obj2;

        // Vérifier les propriétés modifiées et supprimées
        foreach ($array1 as $key => $value) {
            if (!isset($array2[$key])) {
                // Propriété supprimée
                $changes[$key] = [
                    'old' => self::formatValue($value),
                    'new' => null
                ];
            } elseif ($array2[$key] !== $value) {
                // Propriété modifiée
                $changes[$key] = [
                    'old' => self::formatValue($value),
                    'new' => self::formatValue($array2[$key])
                ];
            }
        }

        // Vérifier les nouvelles propriétés
        foreach ($array2 as $key => $value) {
            if (!isset($array1[$key])) {
                $changes[$key] = [
                    'old' => null,
                    'new' => self::formatValue($value)
                ];
            }
        }

        return $changes;
    }

    /**
     * Formate une valeur pour l'affichage
     * @param mixed $value Valeur à formater
     * @return string Valeur formatée
     */
    private static function formatValue($value): ?string
    {
        if (is_null($value)) {
            return null;
        } elseif (is_bool($value)) {
            return $value ? '1' : '0';
        } elseif (is_array($value)) {
            return json_encode($value, JSON_UNESCAPED_UNICODE);
        } elseif (is_object($value)) {
            if (method_exists($value, '__toString')) {
                return (string)$value;
            }
            return json_encode($value, JSON_UNESCAPED_UNICODE);
        }
        return (string)$value;
    }

    /**
     * Insère les changements dans la table historique appropriée
     * @param array $changes Tableau des changements généré par compareForHistory()
     * @param string $type Type d'enregistrement ('affaires', 'essais', 'pv')
     * @param int $recordId ID de l'enregistrement
     * @param int $userId ID de l'utilisateur
     * @return bool Succès de l'insertion
     */
    public static function saveToHistory(array $changes, string $type, int $recordId, int $userId): bool
    {
        if (empty($changes)) {
            return true;
        }

        $db = self::getDb();
        $table = $type . '_historique';
        $idField = rtrim($type, 's') . '_id';

        $sql = "INSERT INTO $table ($idField, changements, modifie_par) 
                VALUES (?, ?, ?)";

        $stmt = $db->prepare($sql);

        try {
            $changements = json_encode($changes, JSON_UNESCAPED_UNICODE);

            return $stmt->execute([
                $recordId,
                $changements,
                $userId
            ]);
        } catch (Exception $e) {
            error_log("Erreur lors de l'enregistrement de l'historique: " . $e->getMessage());
            return false;
        }
    }

    private static function getDb()
    {
        return Database::getInstance()->getConnection();
    }

    /**
     * Récupère l'historique des changements pour un enregistrement donné
     * @param int $recordId ID de l'enregistrement
     * @param string $type Type d'enregistrement ('affaires', 'essais', 'pv')
     * @return array Historique des changements
     */
    public static function getHistory(int $recordId, string $type): array
    {
        $db = self::getDb();
        $table = $type . '_historique';
        $idField = rtrim($type, 's') . '_id';

        $sql = "SELECT * FROM $table WHERE $idField = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$recordId]);

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
