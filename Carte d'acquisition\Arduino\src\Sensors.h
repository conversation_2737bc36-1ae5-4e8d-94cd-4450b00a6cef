#ifndef SENSORS_H
#define SENSORS_H

#include <Arduino.h>

class Sensors {
public:
    // Initialiser les capteurs
    static void init();
    
    // Lire la valeur brute du capteur de pression
    static int readPressureSensor();
    
    // Lire la valeur brute du capteur de débit
    static int readFlowSensor();
    
    // Obtenir la pression en pourcentage (0-100%)
    static float getPressurePercentage();
    
    // Obtenir le débit en pourcentage (0-100%)
    static float getFlowPercentage();
};

#endif // SENSORS_H
