<?php
session_start();
require_once(__DIR__ . '/lib/affaires.php');
require_once(__DIR__ . '/lib/essais.php');
require_once(__DIR__ . '/lib/pv.php');

if (!isset($_SESSION['user'])) {
    header('Location: /auth/login.php');
    exit;
}

$affaires = Affaire::getAll();
$essais = Essai::getAll();

$affairesTermines = array_filter($affaires, function ($affaire) {
    return $affaire['statut'] == 'Terminé';
});

$essaisTermines = array_filter($essais, function ($essai) {
    return $essai['statut'] == 'Terminé';
});

$affairesEnCours = array_filter($affaires, function ($affaire) {
    return $affaire['statut'] == 'En cours';
});

$essaisEnCours = array_filter($essais, function ($essai) {
    return $essai['statut'] == 'En cours';
});

$affairesByWeek = [];
foreach ($affairesTermines as $affaire) {
    $week = date('W', strtotime($affaire['date_creation']));
    if (!isset($affairesByWeek[$week])) {
        $affairesByWeek[$week] = [];
    }
    $affairesByWeek[$week][] = $affaire;
}

$essaisByWeek = [];
foreach ($essaisTermines as $essai) {
    $week = date('W', strtotime($essai['date_essai']));
    if (!isset($essaisByWeek[$week])) {
        $essaisByWeek[$week] = [];
    }
    $essaisByWeek[$week][] = $essai;
}

$previousAffairesCount = count($affairesByWeek[date('W', strtotime('-1 week'))] ?? []);
$currentAffairesCount = count($affairesByWeek[date('W')] ?? []);
$affairesPercentageChange = $previousAffairesCount > 0 ? (($currentAffairesCount - $previousAffairesCount) / $previousAffairesCount) * 100 : 0;

$previousEssaiCount = count($essaisByWeek[date('W', strtotime('-1 week'))] ?? []);
$currentEssaiCount = count($essaisByWeek[date('W')] ?? []);
$essaiPercentageChange = $previousEssaiCount > 0 ? (($currentEssaiCount - $previousEssaiCount) / $previousEssaiCount) * 100 : 0;

ob_start();
?>

    <div class="px-4 pt-6">
        <div class="grid w-full grid-cols-1 gap-4 mt-4 xl:grid-cols-2 2xl:grid-cols-2">
            <div class="items-center justify-between p-4 bg-white border border-gray-200 rounded-lg shadow-sm sm:flex dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                <div class="w-full">
                    <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">Affaires terminées</h3>
                    <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white">
                    <?php echo count($affairesTermines); ?>
                </span>
                    <p class="flex items-center text-base font-normal text-gray-500 dark:text-gray-400">
                    <span class="flex items-center mr-1.5 text-sm <?php echo $affairesPercentageChange > 0 ? 'text-green-500 dark:text-green-400' : ($affairesPercentageChange < 0 ? 'text-red-500 dark:text-red-400' : 'text-gray-500 dark:text-gray-400'); ?>">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"
                             aria-hidden="true">
                            <path clip-rule="evenodd" fill-rule="evenodd"
                                  d="M10 17a.75.75 0 01-.75-.75V5.612L5.29 9.77a.75.75 0 01-1.08-1.04l5.25-5.5a.75.75 0 011.08 0l5.25 5.5a.75.75 0 11-1.08 1.04l-3.96-4.158V16.25A.75.75 0 0110 17z"
                                <?php echo $affairesPercentageChange > 0 ? '' : ($affairesPercentageChange < 0 ? 'transform="rotate(180)"' : ''); ?>></path>
                        </svg>
                        <?php echo rtrim(rtrim(number_format($affairesPercentageChange, 2, '.', ''), '0'), '.') . '%'; ?>
                    </span>
                        Depuis la semaine dernière
                    </p>
                </div>
            </div>
            <div class="items-center justify-between p-4 bg-white border border-gray-200 rounded-lg shadow-sm sm:flex dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                <div class="w-full">
                    <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">Essais terminés</h3>
                    <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white">
                    <?php echo count($essaisTermines); ?>
                </span>
                    <p class="flex items-center text-base font-normal text-gray-500 dark:text-gray-400">
                    <span class="flex items-center mr-1.5 text-sm <?php echo $essaiPercentageChange > 0 ? 'text-green-500 dark:text-green-400' : ($essaiPercentageChange < 0 ? 'text-red-500 dark:text-red-400' : 'text-gray-500 dark:text-gray-400'); ?>">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"
                             aria-hidden="true">
                            <path clip-rule="evenodd" fill-rule="evenodd"
                                  d="M10 17a.75.75 0 01-.75-.75V5.612L5.29 9.77a.75.75 0 01-1.08-1.04l5.25-5.5a.75.75 0 011.08 0l5.25 5.5a.75.75 0 11-1.08 1.04l-3.96-4.158V16.25A.75.75 0 0110 17z"
                                <?php echo $essaiPercentageChange > 0 ? '' : ($essaiPercentageChange < 0 ? 'transform="rotate(180)"' : ''); ?>></path>
                        </svg>
                        <?php echo rtrim(rtrim(number_format($essaiPercentageChange, 2, '.', ''), '0'), '.') . '%'; ?>
                    </span>
                        Depuis la semaine dernière
                    </p>
                </div>
            </div>
        </div>

        <div class="grid w-full grid-cols-1 gap-4 mt-4 xl:grid-cols-2 2xl:grid-cols-2">
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">Affaires en cours</h3>
                <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white">
                <?php echo count($affairesEnCours); ?>
            </span>
            </div>
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">Essais en cours</h3>
                <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white">
                <?php echo count($essaisEnCours); ?>
            </span>
            </div>
        </div>

        <div class="grid w-full grid-cols-1 gap-4 mt-4 xl:grid-cols-3 2xl:grid-cols-3">
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Statistiques rapides</h3>
                </div>
                <div class="space-y-3">
                    <div>
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Taux de réussite des essais</p>
                        <div class="flex items-center mt-2">
                            <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                                <?php
                                $tauxReussite = count($essaisTermines) > 0 ?
                                    (count(array_filter($essaisTermines, function ($e) {
                                            return $e['resultat'] === 'Succès';
                                        })) / count($essaisTermines)) * 100 : 0;
                                ?>
                                <div class="bg-blue-600 h-2.5 rounded-full"
                                     style="width: <?php echo $tauxReussite; ?>%"></div>
                            </div>
                            <span class="text-sm font-medium text-gray-500 dark:text-gray-400 ml-2">
                            <?php echo number_format($tauxReussite, 1); ?>%
                        </span>
                        </div>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Durée moyenne des affaires</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white">
                            <?php
                            // Calculer la durée basée sur le dernier essai de chaque affaire terminée
                            $dureesMoyennes = array_map(function ($a) use ($essais) {
                                // Trouver le dernier essai de cette affaire
                                $essaisAffaire = array_filter($essais, function ($e) use ($a) {
                                    return $e['affaire_id'] == $a['id'];
                                });

                                if (!empty($essaisAffaire)) {
                                    // Trier par date d'essai pour obtenir le plus récent
                                    usort($essaisAffaire, function ($a, $b) {
                                        return strtotime($b['date_essai']) - strtotime($a['date_essai']);
                                    });

                                    $dernierEssai = $essaisAffaire[0];
                                    $debut = new DateTime($a['date_creation']);
                                    $fin = new DateTime($dernierEssai['date_essai']);
                                    return $fin->diff($debut)->days;
                                }

                                // Si pas d'essai, calculer depuis la création jusqu'à maintenant
                                $debut = new DateTime($a['date_creation']);
                                $maintenant = new DateTime();
                                return $debut->diff($maintenant)->days;
                            }, $affairesTermines);

                            $dureesMoyennes = array_filter($dureesMoyennes, function ($d) {
                                return $d !== null;
                            });
                            echo count($dureesMoyennes) > 0 ? round(array_sum($dureesMoyennes) / count($dureesMoyennes)) : 0;
                            ?> jours
                        </p>
                    </div>
                </div>
            </div>

            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Activité récente</h3>
                </div>
                <div class="space-y-3">
                    <?php
                    $activites = array_merge(
                        array_map(function ($a) {
                            return [
                                'type' => 'affaire',
                                'date' => $a['date_creation'],
                                'texte' => "Nouvelle affaire : " . $a['numero']
                            ];
                        }, array_slice($affaires, 0, 3)),
                        array_map(function ($e) {
                            return [
                                'type' => 'essai',
                                'date' => $e['date_essai'],
                                'texte' => "Nouvel essai : " . $e['numero_affaire'] . " - " . $e['type']
                            ];
                        }, array_slice($essais, 0, 3))
                    );
                    usort($activites, function ($a, $b) {
                        return strtotime($b['date']) - strtotime($a['date']);
                    });
                    foreach (array_slice($activites, 0, 5) as $activite): ?>
                        <div class="flex items-center">
                            <div class="<?php echo $activite['type'] === 'affaire' ? 'bg-blue-100' : 'bg-green-100'; ?> p-2 rounded-lg">
                                <svg class="w-4 h-4 <?php echo $activite['type'] === 'affaire' ? 'text-blue-600' : 'text-green-600'; ?>"
                                     fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"></path>
                                    <path fill-rule="evenodd"
                                          d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z"
                                          clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-gray-900 dark:text-white">
                                    <?php echo htmlspecialchars($activite['texte']); ?>
                                </p>
                                <p class="text-xs text-gray-500 dark:text-gray-400">
                                    <?php echo (new DateTime($activite['date']))->format('d/m/Y H:i'); ?>
                                </p>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">À faire</h3>
                </div>
                <div class="space-y-3">
                    <?php
                    $affairesUrgentes = array_filter($affairesEnCours, function ($a) {
                        return strtotime($a['date_creation']) < strtotime('-30 days');
                    });
                    $essaisEnAttente = array_filter($essaisEnCours, function ($e) {
                        return $e['statut'] === 'En attente';
                    });
                    ?>
                    <div class="flex items-center">
                    <span class="text-sm font-medium text-gray-900 dark:text-white">
                        <?php echo count($affairesUrgentes); ?> affaire(s) en cours depuis plus de 30 jours
                    </span>
                    </div>
                    <div class="flex items-center">
                    <span class="text-sm font-medium text-gray-900 dark:text-white">
                        <?php echo count($essaisEnAttente); ?> essai(s) en attente de traitement
                    </span>
                    </div>
                </div>
            </div>
        </div>

        <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800 my-4">

            <div class="items-center justify-between lg:flex">
                <div class="mb-4 lg:mb-0">
                    <h3 class="mb-2 text-xl font-bold text-gray-900 dark:text-white">Affaires</h3>
                    <span class="text-base font-normal text-gray-500 dark:text-gray-400">Voici les dernières affaires</span>
                </div>
            </div>

            <div class="flex flex-col mt-6">
                <div class="overflow-x-auto rounded-lg">
                    <div class="inline-block min-w-full align-middle">
                        <div class="overflow-hidden shadow sm:rounded-lg">
                            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
                                <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th scope="col"
                                        class="p-4 text-xs font-medium tracking-wider text-left text-gray-500 uppercase dark:text-white">
                                        Numéro
                                    </th>
                                    <th scope="col"
                                        class="p-4 text-xs font-medium tracking-wider text-left text-gray-500 uppercase dark:text-white">
                                        Client
                                    </th>
                                    <th scope="col"
                                        class="p-4 text-xs font-medium tracking-wider text-left text-gray-500 uppercase dark:text-white">
                                        Description
                                    </th>
                                    <th scope="col"
                                        class="p-4 text-xs font-medium tracking-wider text-left text-gray-500 uppercase dark:text-white">
                                        Date de création
                                    </th>
                                    <th scope="col"
                                        class="p-4 text-xs font-medium tracking-wider text-left text-gray-500 uppercase dark:text-white">
                                        Statut
                                    </th>
                                    <th scope="col"
                                        class="p-4 text-xs font-medium tracking-wider text-left text-gray-500 uppercase dark:text-white">
                                        Créé par
                                    </th>
                                </tr>
                                </thead>
                                <?php foreach (array_slice($affaires, 0, 10) as $affaire): ?>
                                    <tbody class="bg-white dark:bg-gray-800">
                                    <td class="p-4 text-sm font-normal text-gray-900 whitespace-nowrap dark:text-white">
                                        <?php echo htmlspecialchars($affaire['numero']); ?>
                                    </td>
                                    <td class="p-4 text-sm font-normal text-gray-500 whitespace-nowrap dark:text-gray-400">
                                        <?php echo htmlspecialchars($affaire['client']); ?>
                                    </td>
                                    <td class="p-4 text-sm font-normal text-gray-500 whitespace-nowrap dark:text-gray-400">
                                        <?php
                                        $description = htmlspecialchars($affaire['description'] ?? '');
                                        if (mb_strlen($description) > 35) {
                                            $truncated = mb_substr($description, 0, 35);
                                            $lastSpace = mb_strrpos($truncated, ' ');
                                            if ($lastSpace !== false) {
                                                $truncated = mb_substr($truncated, 0, $lastSpace);
                                            }
                                            echo $truncated . '...';
                                        } else {
                                            echo $description;
                                        }
                                        ?>
                                    </td>
                                    <td class="p-4 text-sm font-normal text-gray-500 whitespace-nowrap dark:text-gray-400">
                                        <?php echo htmlspecialchars($affaire['date_creation']); ?>
                                    </td>
                                    <td class="p-4 whitespace-nowrap">
                                        <span class="text-xs font-medium mr-2 px-2.5 py-0.5 rounded-md border
                                            <?php
                                        if ($affaire['statut'] == 'Annulé') {
                                            echo 'bg-red-100 text-red-800 dark:bg-gray-700 dark:text-red-400 border-red-100 dark:border-red-500';
                                        } elseif ($affaire['statut'] == 'Terminé') {
                                            echo 'bg-green-100 text-green-800 dark:bg-gray-700 dark:text-green-400 border-green-100 dark:border-green-500';
                                        } elseif ($affaire['statut'] == 'En cours') {
                                            echo 'bg-blue-100 text-blue-800 dark:bg-gray-700 dark:text-blue-400 border-blue-100 dark:border-blue-500';
                                        }
                                        ?>">
                                            <?php echo htmlspecialchars($affaire['statut']); ?>
                                        </span>
                                    </td>
                                    <td class="p-4 text-sm font-normal text-gray-500 whitespace-nowrap dark:text-gray-400">
                                        <?php echo htmlspecialchars($affaire['created_by_username']); ?>
                                    </td>
                                    </tr>
                                    </tbody>
                                <?php endforeach; ?>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="flex items-center justify-between pt-3 sm:pt-6">
                <div class="flex-shrink-0 ml-auto">
                    <a href="affaires.php"
                       class="inline-flex items-center p-2 text-xs font-medium uppercase rounded-lg text-primary-700 sm:text-sm hover:bg-gray-100 dark:text-primary-500 dark:hover:bg-gray-700">
                        Voir toutes les affaires
                        <svg class="w-4 h-4 ml-1 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                             xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </div>

<?php
$pageContent = ob_get_clean();
include 'layout.php';
?>