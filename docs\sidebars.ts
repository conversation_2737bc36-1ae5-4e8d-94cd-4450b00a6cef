import type {SidebarsConfig} from '@docusaurus/plugin-content-docs';

// This runs in Node.js - Don't use client-side code here (browser APIs, JSX...)

/**
 * Creating a sidebar enables you to:
 - create an ordered group of docs
 - render a sidebar for each doc of that group
 - provide next/previous navigation

 The sidebars can be generated from the filesystem, or explicitly defined here.

 Create as many sidebars as you want.
 */
const sidebars: SidebarsConfig = {
  tutorialSidebar: [
    'intro',
    {
      type: 'category',
      label: 'Guide de Démarrage',
      items: [
        'getting-started/introduction',
        'getting-started/user-roles',
        'getting-started/first-login',
        'getting-started/navigation',
      ],
    },
    {
      type: 'category',
      label: 'Interface Utilisateur',
      items: [
        'user-interface/dashboard',
        'user-interface/navigation-menu',
        'user-interface/theme-switching',
        'user-interface/keyboard-shortcuts',
      ],
    },
    {
      type: 'category',
      label: 'Modules Principaux',
      items: [
        'modules/affaires',
        'modules/essais',
        'modules/pv',
        'modules/detail-views',
      ],
    },
    {
      type: 'category',
      label: 'Fonctionnalités Avancées (Contrôleur)',
      items: [
        'advanced/backup-restore',
        'advanced/system-diagnostics',
        'advanced/data-generator',
      ],
    },
    {
      type: 'category',
      label: 'Workflows et Bonnes Pratiques',
      items: [
        'workflows/complete-workflow',
        'workflows/test-execution',
        'workflows/report-generation',
        'workflows/best-practices',
      ],
    },
    {
      type: 'category',
      label: 'Tests et Validation',
      items: [
        'tests-validation/index',
        {
          type: 'category',
          label: 'Tests Fonctionnels',
          items: [
            'tests-validation/authentification',
            'tests-validation/affaires',
            'tests-validation/essais',
            'tests-validation/courbes',
            'tests-validation/rendements',
            'tests-validation/pv',
          ],
        },
        {
          type: 'category',
          label: 'Tests Techniques',
          items: [
            'tests-validation/sauvegarde',
            'tests-validation/permissions',
            'tests-validation/api',
            'tests-validation/classes-metier',
            'tests-validation/performance',
          ],
        },
        {
          type: 'category',
          label: 'Tests Système',
          items: [
            'tests-validation/systeme',
            'tests-validation/interface',
            'tests-validation/charge',
            'tests-validation/erreurs',
          ],
        },
        {
          type: 'category',
          label: 'Tests Spécialisés',
          items: [
            'tests-validation/generateur',
            'tests-validation/validation',
            'tests-validation/raspberry-pi',
          ],
        },
        {
          type: 'category',
          label: 'Documentation',
          items: [
            'tests-validation/tracabilite',
            'tests-validation/guide-execution',
          ],
        },
      ],
    },
    {
      type: 'category',
      label: 'Dépannage',
      items: [
        'troubleshooting/common-issues',
        'troubleshooting/error-messages',
        'troubleshooting/faq',
      ],
    },
  ],
};

export default sidebars;
