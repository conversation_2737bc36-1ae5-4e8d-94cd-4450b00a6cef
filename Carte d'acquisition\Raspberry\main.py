from serial_comm import SerialComm
import time
import platform

def main():
    # Détermine automatiquement le port série en fonction du système d'exploitation
    if platform.system() == 'Windows':
        port = 'COM3'  # Port par défaut sous Windows
    else:
        port = '/dev/ttyACM0'  # Port par défaut sous Linux/Raspberry Pi
    
    print(f"Tentative de connexion au port {port}...")
    
    # Initialize serial communication
    comm = SerialComm(port, baudrate=9600)
    
    try:
        while True:
            # Get all sensor data
            data = comm.get_all_sensors()
            if data:
                print(f"Time: {data['timestamp']}")
                print(f"Pressure: {data['pressure']}% ({data['pressure_pascal']} Pa)")
                print(f"Flow: {data['flow']}% ({data['flow_lpm']} L/min)")
                print("-" * 40)
            
            # Wait before next reading
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\nClosing connection...")
    finally:
        comm.close()

if __name__ == "__main__":
    main()
