---
sidebar_position: 5
title: Tests de Gestion des Courbes
description: Tests de validation du module de gestion des courbes de mesure
keywords: [courbes, mesures, CPA, CPB, optimisation]
---

# Tests de Gestion des Courbes

Cette section présente les tests de validation du module de gestion des courbes de mesure, essentielles pour l'analyse des données hydrauliques.

## 🎯 Objectifs des Tests

- Valider la saisie des données de courbes CPA et CPB
- Vérifier l'optimisation des courbes avec nombreux points
- Contrôler la suppression sécurisée des courbes
- Tester l'affichage graphique et la performance

## 📊 Vue d'Ensemble

| **Module** | **Gestion des Courbes** |
|------------|-------------------------|
| **Nombre de tests** | **3 tests** |
| **Criticité** | **Critique** |
| **Couverture** | **100%** |
| **Profils concernés** | **Contrôleur + Opérateur** |

## 🧪 Tests Détaillés

### CRB-001 : <PERSON>sie de Courbes de Mesure

| **Aspect** | **Détail** |
|------------|------------|
| **Objectif** | Valider la saisie des données de courbes |
| **Préconditions** | - Essai en statut "En cours" |
| **Étapes de Test** | 1. Accéder à l'essai<br />2. Ajouter une courbe CPA avec données de mesure<br />3. Ajouter une courbe CPB avec données de mesure<br />4. Vérifier l'enregistrement des données<br />5. Contrôler l'affichage graphique |
| **Résultats Attendus** | - Courbes CPA et CPB enregistrées<br />- Données JSON correctement formatées<br />- Affichage graphique fonctionnel<br />- Horodatage correct |
| **Critères de Réussite** | ✅ Courbes saisies et affichées |

### CRB-002 : Optimisation des Données de Courbes

| **Aspect** | **Détail** |
|------------|------------|
| **Objectif** | Valider l'optimisation automatique des courbes |
| **Préconditions** | - Courbes avec nombreux points de données (> 1000) |
| **Étapes de Test** | 1. Charger une courbe avec > 1000 points<br />2. Vérifier l'optimisation automatique<br />3. Contrôler la qualité de l'affichage<br />4. Vérifier les performances de chargement |
| **Résultats Attendus** | - Optimisation automatique activée<br />- Qualité graphique préservée<br />- Temps de chargement acceptable<br />- Données originales conservées |
| **Critères de Réussite** | ✅ Optimisation des courbes fonctionnelle |

### CRB-003 : Suppression de Courbes

| **Aspect** | **Détail** |
|------------|------------|
| **Objectif** | Valider la suppression sécurisée de courbes |
| **Préconditions** | - Courbes existantes liées à un essai |
| **Étapes de Test** | 1. Sélectionner une courbe<br />2. Cliquer sur "Supprimer"<br />3. Confirmer la suppression<br />4. Vérifier la mise à jour de l'affichage |
| **Résultats Attendus** | - Courbe supprimée de la base<br />- Affichage mis à jour<br />- Autres courbes non affectées<br />- Historique de suppression tracé |
| **Critères de Réussite** | ✅ Suppression de courbes sécurisée |

## 📈 Types de Courbes

### 🔧 Courbes Techniques

| **Type** | **Description** | **Usage** |
|----------|-----------------|-----------|
| **CPA** | Chambre Piston A (côté tige) | Mesure pression côté tige |
| **CPB** | Chambre Piston B (côté fond) | Mesure pression côté fond |
| **Résultante** | Courbe calculée (CPA - CPB) | Analyse différentielle |

### 📊 Formats de Données

```json
{
  "type": "CPA",
  "points": [
    {"x": 0, "y": 0, "timestamp": "2024-12-15T10:00:00Z"},
    {"x": 1, "y": 50, "timestamp": "2024-12-15T10:00:01Z"},
    {"x": 2, "y": 100, "timestamp": "2024-12-15T10:00:02Z"}
  ],
  "metadata": {
    "unit": "bars",
    "frequency": "1Hz",
    "total_points": 3
  }
}
```

## 🎭 Tests par Profil

### 👨‍💼 Tests Contrôleur
- **CRB-001** : Saisie de courbes complète ✅
- **CRB-002** : Optimisation avancée ✅
- **CRB-003** : Suppression avec historique ✅

### 👨‍🔧 Tests Opérateur
- **CRB-001** : Saisie de courbes complète ✅
- **CRB-002** : Optimisation avancée ✅
- **CRB-003** : Suppression avec restrictions ⚠️

:::info Permissions Opérateur
Les opérateurs peuvent avoir des restrictions sur la suppression de courbes selon la configuration système.
:::

## 🚨 Points de Vigilance

### Performance
- Optimisation automatique pour > 1000 points
- Temps de chargement < 3 secondes
- Affichage fluide même avec gros volumes

### Intégrité
- Vérification format JSON des données
- Validation des timestamps
- Cohérence des unités de mesure

### Sécurité
- Confirmation avant suppression
- Traçabilité des modifications
- Sauvegarde automatique

## 📋 Checklist de Validation

### Avant les Tests
- [ ] Essai en cours disponible
- [ ] Données de test préparées
- [ ] Interface graphique fonctionnelle

### Pendant les Tests
- [ ] Saisie des courbes CPA/CPB
- [ ] Vérification affichage graphique
- [ ] Test optimisation performance
- [ ] Validation suppression sécurisée

### Après les Tests
- [ ] Nettoyage des courbes de test
- [ ] Vérification intégrité base
- [ ] Documentation des résultats

## 🔗 Liens Connexes

- [**Tests des Essais**](./essais) - Module parent des courbes
- [**Tests des Rendements**](./rendements) - Calculs basés sur les courbes
- [**Tests de Performance**](./performance) - Optimisation des courbes
- [**Guide d'Exécution**](./guide-execution) - Méthodologie de test

---

:::tip Conseil
Utilisez des données de test réalistes avec différents volumes pour valider l'optimisation automatique des courbes.
:::

:::warning Attention
La suppression de courbes est irréversible. Vérifiez toujours que vous travaillez sur des données de test.
:::

:::info Navigation
**Précédent** : [Tests de Gestion des Essais](./essais)  
**Suivant** : [Tests de Calcul des Rendements](./rendements)
:::
