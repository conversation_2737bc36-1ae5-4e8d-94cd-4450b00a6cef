# Générateur de Données

<div className="role-controleur">
<strong>🔒 Fonctionnalité Contrôleur Uniquement</strong><br/>
Cette section est réservée aux utilisateurs ayant le rôle "Contrôleur". Les opérateurs n'ont pas accès à ces fonctionnalités.
</div>

## Vue d'ensemble

Le **Générateur de Données** permet aux contrôleurs de créer rapidement des jeux de données de test pour la formation, les démonstrations ou les tests de performance. Il génère des données cohérentes et réalistes dans tous les modules du système avec une **nouvelle fonctionnalité de variance séquentielle contrôlée** qui simule des conditions d'essais hydrauliques réalistes.

### 🆕 Nouvelles Fonctionnalités

#### Génération de Données Cohérentes
- **Variance séquentielle** : Chaque point de mesure varie de ±10% par rapport au point précédent
- **Patterns hydrauliques réalistes** : Simulation de montée en pression, corrélation débit-pression, évolution thermique
- **Limites de sécurité** : Valeurs maintenues dans les plages industrielles réalistes
- **Configuration personnalisable** : Variance ajustable de 1% à 50% selon le type d'essai

## Accès au Module

### Navigation
- **Menu utilisateur** : "🌐 Générateur de Données"
- **Raccourci clavier** : <kbd>Alt</kbd> + <kbd>G</kbd> (si configuré)
- **URL directe** : `/admin/data-generator.php`

### Vérifications de Sécurité
- **Contrôle de rôle** : Vérification "Contrôleur" obligatoire
- **Confirmation** : Double validation pour les actions destructives
- **Logs** : Traçabilité de toutes les générations

## Interface du Générateur

### Tableau de Bord Principal

#### État Actuel des Données

<div className="info-box">

**Statistiques Actuelles**
- **Affaires** : Nombre total d'enregistrements
- **Essais** : Nombre total de tests
- **PV** : Nombre de procès-verbaux
- **Utilisateurs** : Comptes actifs (hors système)

</div>

#### Options de Génération

| Type de Données | Description | Quantité Recommandée |
|------------------|-------------|---------------------|
| **Affaires** | Dossiers clients fictifs | 10-100 |
| **Essais** | Tests hydrauliques simulés | 50-500 |
| **PV** | Rapports de test | 20-200 |
| **Utilisateurs** | Comptes de démonstration | 5-20 |

## Génération d'Affaires

### Configuration de la Génération

<div className="step-number">1</div>
<div className="step-content">

**Paramètres d'Affaires**
- **Nombre** : Quantité d'affaires à créer (1-1000)
- **Période** : Répartition sur X mois
- **Types de clients** : PME, Grandes entreprises, Particuliers
- **Statuts** : Répartition En cours/Terminé/Annulé

</div>

### Données Générées

#### Informations Client
```
Exemples de clients générés :
- Hydraulique Industries SA
- Systèmes Fluides SARL
- Mécanique Précision SAS
- Composants Haute Pression
- Solutions Hydrauliques Pro
```

#### Numérotation Automatique
- **Format** : AFF-YYYY-XXX (année + numéro séquentiel)
- **Cohérence** : Respect de la chronologie
- **Unicité** : Pas de doublons garantie

#### Descriptions Réalistes

<div className="feature-highlight">

**Types de Projets Générés**
- Test de vérins hydrauliques 350 bar
- Validation de pompes haute pression
- Contrôle de distributeurs proportionnels
- Essais de résistance à la fatigue
- Certification de composants aéronautiques

</div>

### Tags Automatiques
- **Priorité** : urgent, normal, faible
- **Secteur** : aéronautique, automobile, industriel
- **Type** : maintenance, développement, certification

## 🔬 Génération de Données Cohérentes

### Principe de la Variance Séquentielle

<div className="feature-highlight">

**Innovation : Cohérence entre Points de Mesure**

Le système génère maintenant des séquences de mesures où chaque point reste dans une variance de ±10% du point précédent, simulant les conditions réelles d'essais hydrauliques.

</div>

#### Avantages Opérationnels
- **Réalisme accru** : Les données ressemblent aux vraies mesures d'essais
- **Cohérence temporelle** : Évite les variations brutales irréalistes
- **Formation efficace** : Les opérateurs s'entraînent sur des données réalistes
- **Validation d'algorithmes** : Tests plus fiables des systèmes d'analyse

### Patterns Hydrauliques Simulés

#### 1. Montée en Pression Progressive
```
Phase initiale (0-20% de l'essai) :
- Augmentation graduelle de la pression
- Simulation de la mise en pression du système
- Évite les pics de pression irréalistes
```

#### 2. Corrélation Pression-Débit
```
Comportement physique simulé :
- Pression élevée → Débit légèrement réduit
- Simulation de la résistance hydraulique
- Respect des lois physiques des fluides
```

#### 3. Évolution Thermique
```
Échauffement progressif :
- Température initiale : 45-75°C
- Augmentation graduelle : +5°C maximum
- Simulation de l'échauffement du fluide
```

### Configuration de la Variance

#### Niveaux de Variance Recommandés

| Type d'Essai | Variance | Usage Opérationnel |
|--------------|----------|-------------------|
| **Tests de Précision** | ±5% | Contrôle qualité, étalonnage |
| **Tests Standard** | ±10% | Validation industrielle courante |
| **Tests de Robustesse** | ±15-20% | Conditions difficiles, stress test |

#### Limites de Sécurité Automatiques

<div className="info-box">

**Plages Industrielles Respectées**
- **Pression** : 0.1 à 350 bar (systèmes hydrauliques industriels)
- **Débit** : 1 à 150 L/min (gamme des équipements courants)
- **Température** : 20 à 90°C (conditions d'exploitation normales)

</div>

## Génération d'Essais

### Configuration Avancée

<div className="step-number">1</div>
<div className="step-content">

**Paramètres d'Essais**
- **Nombre par affaire** : 1-10 essais par affaire
- **Types d'essais** : Pression, débit, température, fatigue
- **Répartition temporelle** : Sur la durée de l'affaire
- **Taux de réussite** : Pourcentage d'essais réussis

</div>

### Types d'Essais Générés

#### Essais de Pression
```
Type : Test de pression hydraulique
Paramètres théoriques : Pression max 350 bar, maintien 5 min
Mode opératoire : Montée progressive, paliers de 50 bar
Résultats : Pression atteinte 348 bar, étanchéité OK
```

#### Essais de Débit
```
Type : Mesure de débit nominal
Paramètres théoriques : Débit 45 L/min à 200 bar
Mode opératoire : Mesure à différentes pressions
Résultats : Débit mesuré 44.2 L/min, conforme
```

#### Essais de Fatigue
```
Type : Test de résistance cyclique
Paramètres théoriques : 1 million de cycles à 250 bar
Mode opératoire : Cycles automatisés 24h/24
Résultats : 1.2M cycles atteints, aucune défaillance
```

### Statuts et Progression

#### Répartition des Statuts
- **En attente** : 20% des essais
- **En cours** : 15% des essais
- **Terminé** : 60% des essais
- **Annulé** : 5% des essais

## Génération de PV

### Configuration des Rapports

<div className="step-number">1</div>
<div className="step-content">

**Paramètres de PV**
- **Couverture** : Pourcentage d'affaires avec PV
- **Types de rapports** : Conformité, non-conformité, partiel
- **Statuts** : Répartition Brouillon/Finalisé/Envoyé
- **Qualité** : Niveau de détail du contenu

</div>

### Contenu Généré

#### Structure Standard

<div className="success-box">

**Sections Automatiques**
1. **Contexte** : Référence affaire et objectifs
2. **Conditions d'essai** : Environnement et équipements
3. **Résultats** : Mesures et observations
4. **Conclusions** : Conformité et recommandations

</div>

#### Exemple de Contenu Généré
```
PROCÈS-VERBAL D'ESSAI PV-2024-001

1. CONTEXTE
Affaire : AFF-2024-015 - Hydraulique Industries SA
Objet : Validation vérin hydraulique 350 bar
Norme : ISO 4413 - Systèmes de transmission hydraulique

2. CONDITIONS D'ESSAI
Date : 15/01/2024
Température : 22°C ± 2°C
Fluide : HLP 46 selon ISO 11158
Équipement : Banc d'essai certifié COFRAC

3. RÉSULTATS
Pression maximale : 348 bar (spéc. 350 bar)
Débit nominal : 44.2 L/min (spéc. 45 L/min)
Étanchéité : Aucune fuite détectée
Cycles de fatigue : 1.2M cycles réalisés

4. CONCLUSIONS
Conforme aux spécifications client
Recommandation : Validation pour mise en service
Validité : 12 mois à compter de la date d'essai
```

## Génération d'Utilisateurs

### Types de Comptes

#### Comptes de Démonstration

<div className="role-operateur">

**Opérateurs Générés**
- operateur_demo1 / password123
- operateur_demo2 / password123
- operateur_test / password123

</div>

<div className="role-controleur">

**Contrôleurs Générés**
- controleur_demo1 / password123
- controleur_test / password123

</div>

### Sécurité des Comptes

<div className="warning-box">

**⚠️ Comptes de Test Uniquement**
- Mots de passe par défaut faibles
- À modifier immédiatement en production
- Suppression recommandée après formation
- Aucun accès aux données sensibles

</div>

## Options Avancées

### Génération Cohérente

#### Relations Logiques
- **Chronologie** : Respect de l'ordre temporel
- **Dépendances** : Essais liés aux affaires appropriées
- **Cohérence** : PV basés sur des essais existants
- **Réalisme** : Données techniquement plausibles

#### Paramètres de Qualité

| Niveau | Description | Utilisation |
|--------|-------------|-------------|
| **Basique** | Données minimales | Tests rapides |
| **Standard** | Données complètes | Démonstrations |
| **Avancé** | Données détaillées | Formation approfondie |

### Nettoyage et Réinitialisation

#### Suppression Sélective

<div className="step-number">1</div>
<div className="step-content">

**Options de Nettoyage**
- **Données générées uniquement** : Préservation des vraies données
- **Période spécifique** : Suppression par date
- **Type de données** : Affaires, essais ou PV seulement
- **Confirmation multiple** : Sécurité anti-erreur

</div>

#### Réinitialisation Complète

<div className="warning-box">

**🚨 ATTENTION : Action Destructive**
- Suppression de TOUTES les données
- Retour à l'état initial du système
- Perte définitive des informations
- Réservé aux environnements de test

</div>

## Cas d'Usage

### Formation Utilisateurs

#### Scénarios Pédagogiques
- **Découverte** : 10 affaires, 30 essais, 15 PV
- **Pratique** : 50 affaires, 150 essais, 75 PV
- **Maîtrise** : 100 affaires, 300 essais, 150 PV

### Tests de Performance

#### Charge de Données
- **Test léger** : 100 affaires, 500 essais
- **Test moyen** : 1000 affaires, 5000 essais
- **Test lourd** : 10000 affaires, 50000 essais

### Démonstrations Client

#### Présentation Réaliste
- **Secteur spécifique** : Données adaptées au client
- **Cas d'usage** : Scénarios pertinents
- **Résultats variés** : Conformes et non-conformes

## Bonnes Pratiques

### Utilisation Responsable

<div className="success-box">

**Recommandations**
1. **Environnement de test** : Jamais en production avec vraies données
2. **Nettoyage régulier** : Suppression après utilisation
3. **Documentation** : Traçabilité des générations
4. **Sécurité** : Modification des mots de passe par défaut

</div>

### Planification

#### Avant Génération
- **Sauvegarde** : Backup des données existantes
- **Espace disque** : Vérification de l'espace disponible
- **Performance** : Génération en dehors des heures d'activité
- **Communication** : Information des utilisateurs

---

:::tip Formation Efficace
Générez des données progressivement : commencez par un petit jeu pour la découverte, puis augmentez pour la pratique.
:::

:::warning Sécurité
Ne jamais utiliser le générateur sur un système de production contenant de vraies données client.
:::
