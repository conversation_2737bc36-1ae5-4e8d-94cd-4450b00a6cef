<?php
/**
 * Page cachée de tests - FluidMotion Labs
 * Accès: /tests.php
 *
 * Cette page présente tous les tests disponibles pour vérifier
 * le bon fonctionnement du système.
 */

session_start();

// Vérifier que l'utilisateur est connecté et est un contrôleur
if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'controleur') {
    die('Accès refusé. Cette page nécessite un utilisateur contrôleur connecté.');
}

// Charger l'autoloader Composer si disponible
if (file_exists(__DIR__ . '/vendor/autoload.php')) {
    require_once(__DIR__ . '/vendor/autoload.php');
}

// Fonction pour détecter l'environnement
function detectEnvironment()
{
    // Méthode 1: Variables d'environnement Docker
    if (getenv('DOCKER_CONTAINER') !== false || getenv('HOSTNAME') !== false) {
        $hostname = getenv('HOSTNAME');
        if ($hostname && (strpos($hostname, 'docker') !== false || strlen($hostname) === 12)) {
            return 'Docker';
        }
    }

    // Méthode 2: Vérifier les variables d'environnement spécifiques à notre setup
    if (getenv('DB_HOST') === 'db' || getenv('APACHE_RUN_USER') === 'www-data') {
        return 'Docker';
    }

    // Méthode 3: Vérifier le fichier /.dockerenv (fallback)
    if (file_exists('/.dockerenv')) {
        return 'Docker';
    }

    // Méthode 4: Vérifier les processus (si proc est disponible)
    if (is_readable('/proc/1/cgroup')) {
        $cgroup = file_get_contents('/proc/1/cgroup');
        if (strpos($cgroup, 'docker') !== false || strpos($cgroup, 'containerd') !== false) {
            return 'Docker';
        }
    }

    return 'Standard';
}

// Fonction pour vérifier le statut d'un test
function getTestStatus($testFile)
{
    if (file_exists(__DIR__ . '/tests/' . $testFile)) {
        return ['status' => 'available', 'message' => 'Disponible'];
    } else {
        return ['status' => 'missing', 'message' => 'Fichier manquant'];
    }
}

// Fonction pour vérifier les prérequis d'un test
function checkPrerequisites($test)
{
    switch ($test) {
        case 'mpdf':
            if (class_exists('\Mpdf\Mpdf')) {
                return ['status' => 'ok', 'message' => 'mPDF installé'];
            } else {
                return ['status' => 'warning', 'message' => 'mPDF non installé'];
            }

        case 'docker':
            $env = detectEnvironment();
            if ($env === 'Docker') {
                return ['status' => 'ok', 'message' => 'Environnement Docker détecté'];
            } else {
                return ['status' => 'info', 'message' => 'Environnement Standard'];
            }

        case 'backup':
            if (function_exists('exec')) {
                return ['status' => 'ok', 'message' => 'Fonction exec() disponible'];
            } else {
                return ['status' => 'error', 'message' => 'Fonction exec() désactivée'];
            }

        case 'permissions':
            $dirs = [__DIR__ . '/backups', __DIR__ . '/pdf_exports', __DIR__ . '/temp'];
            $allWritable = true;
            foreach ($dirs as $dir) {
                if (!is_dir($dir) || !is_writable($dir)) {
                    $allWritable = false;
                    break;
                }
            }
            if ($allWritable) {
                return ['status' => 'ok', 'message' => 'Tous les dossiers accessibles'];
            } else {
                return ['status' => 'warning', 'message' => 'Problèmes de permissions détectés'];
            }

        case 'debug':
            if (isset($_SESSION['user'])) {
                return ['status' => 'ok', 'message' => 'Session utilisateur active'];
            } else {
                return ['status' => 'error', 'message' => 'Aucune session utilisateur'];
            }

        case 'validation':
            // Vérifier que les classes de calcul sont disponibles
            if (class_exists('Rendement') || file_exists(__DIR__ . '/lib/rendement.php')) {
                return ['status' => 'ok', 'message' => 'Classes de calcul disponibles'];
            } else {
                return ['status' => 'error', 'message' => 'Classes de calcul manquantes'];
            }

        default:
            return ['status' => 'info', 'message' => 'Aucun prérequis spécifique'];
    }
}

// Définition des tests disponibles
$tests = [
    'core' => [
        'title' => 'Tests Fondamentaux',
        'description' => 'Tests des fonctionnalités de base du système',
        'tests' => [
            [
                'name' => 'Suite de Tests Unitaires',
                'file' => 'test_runner.php',
                'description' => 'Exécute tous les tests unitaires (Affaires, Essais, Rendement) avec rapport détaillé',
                'type' => 'debug',
                'priority' => 'high',
                'estimated_time' => '2-3 minutes'
            ],
            [
                'name' => 'Vérification des Permissions',
                'file' => 'check_permissions.php',
                'description' => 'Vérifie et corrige les permissions des dossiers critiques',
                'type' => 'permissions',
                'priority' => 'high',
                'estimated_time' => '1-2 minutes'
            ],
            [
                'name' => 'Debug Session Utilisateur',
                'file' => 'debug_session.php',
                'description' => 'Affiche la structure de la session utilisateur pour le débogage',
                'type' => 'debug',
                'priority' => 'low',
                'estimated_time' => '30 secondes'
            ],
            [
                'name' => 'Détection d\'Environnement',
                'file' => 'environment_detection.php',
                'description' => 'Diagnostic détaillé de la détection d\'environnement (Docker vs Standard)',
                'type' => 'debug',
                'priority' => 'medium',
                'estimated_time' => '1 minute'
            ]
        ]
    ],
    'pdf' => [
        'title' => 'Tests PDF et mPDF',
        'description' => 'Tests de génération PDF et configuration mPDF',
        'tests' => [
            [
                'name' => 'Installation mPDF',
                'file' => 'install_mpdf.php',
                'description' => 'Installe mPDF via Composer ou méthode alternative',
                'type' => 'mpdf',
                'priority' => 'high',
                'estimated_time' => '2-5 minutes'
            ],
            [
                'name' => 'Vérification Rapide mPDF',
                'file' => 'quick_mpdf_check.php',
                'description' => 'Diagnostic rapide de l\'installation et configuration mPDF',
                'type' => 'mpdf',
                'priority' => 'high',
                'estimated_time' => '30 secondes'
            ],
            [
                'name' => 'Test mPDF',
                'file' => 'test_mpdf.php',
                'description' => 'Teste la génération PDF avec mPDF et crée un PDF de démonstration',
                'type' => 'mpdf',
                'priority' => 'medium',
                'estimated_time' => '1 minute'
            ]
        ]
    ],
    'validation' => [
        'title' => 'Tests de Validation des Calculs',
        'description' => 'Validation complète de l\'exactitude mathématique et physique des calculs',
        'tests' => [
            [
                'name' => 'Validation Complète des Calculs',
                'file' => 'validation_calculs.php',
                'description' => 'Vérifie l\'exactitude de tous les calculs : rendements, conversions d\'unités, cohérence physique',
                'type' => 'validation',
                'priority' => 'high',
                'estimated_time' => '1-2 minutes'
            ]
        ]
    ],
    'docker' => [
        'title' => 'Tests Docker',
        'description' => 'Tests spécifiques à l\'environnement Docker',
        'tests' => [
            [
                'name' => 'Test Docker Backup',
                'file' => 'test_docker_backup.php',
                'description' => 'Teste les fonctionnalités de backup et PDF dans l\'environnement Docker',
                'type' => 'docker',
                'priority' => 'medium',
                'estimated_time' => '2-3 minutes'
            ]
        ]
    ]
];

ob_start();
?>

<div class="p-6">
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold dark:text-white">🧪 Suite de Tests - FluidMotion Labs</h1>
            <p class="text-gray-600 dark:text-gray-400 mt-2">Tests de vérification et diagnostic du système</p>
        </div>
        <div class="text-right">
            <p class="text-sm text-gray-500 dark:text-gray-400">
                Utilisateur: <?php echo htmlspecialchars($_SESSION['user']['username'] ?? 'Inconnu'); ?></p>
            <p class="text-sm text-gray-500 dark:text-gray-400">
                Rôle: <?php echo htmlspecialchars($_SESSION['user']['role'] ?? 'Inconnu'); ?></p>
        </div>
    </div>

    <!-- Informations système -->
    <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg mb-6">
        <h2 class="text-lg font-semibold text-blue-800 dark:text-blue-400 mb-2">ℹ️ Informations Système</h2>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
                <span class="font-medium">PHP:</span> <?php echo PHP_VERSION; ?>
            </div>
            <div>
                <span class="font-medium">OS:</span> <?php echo PHP_OS; ?>
            </div>
            <div>
                <span class="font-medium">Environnement:</span>
                <?php echo detectEnvironment(); ?>
            </div>
            <div>
                <span class="font-medium">Serveur:</span> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Inconnu'; ?>
            </div>
            <div>
                <span class="font-medium">mPDF:</span>
                <?php echo class_exists('\Mpdf\Mpdf') ? '✅ Installé' : '❌ Non installé'; ?>
            </div>
            <div>
                <span class="font-medium">Composer:</span>
                <?php echo file_exists(__DIR__ . '/vendor/autoload.php') ? '✅ Disponible' : '❌ Non disponible'; ?>
            </div>
        </div>
    </div>

    <!-- Tests par catégorie -->
    <?php foreach ($tests as $categoryKey => $category): ?>
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow mb-6">
            <div class="p-6">
                <h2 class="text-2xl font-bold dark:text-white mb-2"><?php echo $category['title']; ?></h2>
                <p class="text-gray-600 dark:text-gray-400 mb-4"><?php echo $category['description']; ?></p>

                <div class="space-y-4">
                    <?php foreach ($category['tests'] as $test): ?>
                        <?php
                        $fileStatus = getTestStatus($test['file']);
                        $prerequisites = checkPrerequisites($test['type']);
                        ?>

                        <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                            <div class="flex justify-between items-start mb-3">
                                <div class="flex-1">
                                    <h3 class="text-lg font-semibold dark:text-white"><?php echo $test['name']; ?></h3>
                                    <p class="text-gray-600 dark:text-gray-400 text-sm mt-1"><?php echo $test['description']; ?></p>
                                </div>

                                <div class="flex space-x-2 ml-4">
                                    <!-- Badge de priorité -->
                                    <span class="px-2 py-1 text-xs font-medium rounded-full
                                        <?php
                                    switch ($test['priority']) {
                                        case 'high':
                                            echo 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
                                            break;
                                        case 'medium':
                                            echo 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
                                            break;
                                        default:
                                            echo 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
                                    }
                                    ?>">
                                        <?php echo ucfirst($test['priority']); ?>
                                    </span>

                                    <!-- Badge de statut -->
                                    <span class="px-2 py-1 text-xs font-medium rounded-full
                                        <?php
                                    switch ($fileStatus['status']) {
                                        case 'available':
                                            echo 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
                                            break;
                                        case 'missing':
                                            echo 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
                                            break;
                                        default:
                                            echo 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
                                    }
                                    ?>">
                                        <?php echo $fileStatus['message']; ?>
                                    </span>
                                </div>
                            </div>

                            <div class="flex justify-between items-center">
                                <div class="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                                    <span>⏱️ <?php echo $test['estimated_time']; ?></span>
                                    <span class="
                                        <?php
                                    switch ($prerequisites['status']) {
                                        case 'ok':
                                            echo 'text-green-600 dark:text-green-400';
                                            break;
                                        case 'warning':
                                            echo 'text-yellow-600 dark:text-yellow-400';
                                            break;
                                        case 'error':
                                            echo 'text-red-600 dark:text-red-400';
                                            break;
                                        default:
                                            echo 'text-blue-600 dark:text-blue-400';
                                    }
                                    ?>">
                                        <?php echo $prerequisites['message']; ?>
                                    </span>
                                </div>

                                <?php if ($fileStatus['status'] === 'available'): ?>
                                    <a href="/tests/<?php echo $test['file']; ?>"
                                       class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                  d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                        </svg>
                                        Exécuter le Test
                                    </a>
                                <?php else: ?>
                                    <button disabled
                                            class="inline-flex items-center px-4 py-2 bg-gray-300 text-gray-500 text-sm font-medium rounded-lg cursor-not-allowed">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                  d="M6 18L18 6M6 6l12 12"></path>
                                        </svg>
                                        Non Disponible
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    <?php endforeach; ?>

    <!-- Actions rapides -->
    <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
        <h2 class="text-xl font-bold dark:text-white mb-4">🚀 Actions Rapides</h2>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <a href="/data-generator.php"
               class="flex items-center p-4 bg-white dark:bg-gray-700 rounded-lg shadow hover:shadow-md transition-shadow">
                <div class="w-10 h-10 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center mr-3">
                    <svg class="w-5 h-5 text-purple-600 dark:text-purple-400" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                </div>
                <div>
                    <h3 class="font-medium dark:text-white">Générateur de Données</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">Créer des données de test</p>
                </div>
            </a>
            <a href="/backup.php"
               class="flex items-center p-4 bg-white dark:bg-gray-700 rounded-lg shadow hover:shadow-md transition-shadow">
                <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center mr-3">
                    <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 24 24">
                        <path fill-rule="evenodd"
                              d="M12 3a1 1 0 0 1 .78.375l4 5A1 1 0 0 1 16 10h-3v9a1 1 0 1 1-2 0v-9H8a1 1 0 0 1-.78-1.625l4-5A1 1 0 0 1 12 3Z"
                              clip-rule="evenodd"/>
                    </svg>
                </div>
                <div>
                    <h3 class="font-medium dark:text-white">Gestion des Sauvegardes</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">Créer et gérer les backups</p>
                </div>
            </a>

            <a href="/pv.php"
               class="flex items-center p-4 bg-white dark:bg-gray-700 rounded-lg shadow hover:shadow-md transition-shadow">
                <div class="w-10 h-10 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center mr-3">
                    <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="currentColor" viewBox="0 0 24 24">
                        <path fill-rule="evenodd"
                              d="M9 2.221V7H4.221a2 2 0 0 1 .365-.5L8.5 2.586A2 2 0 0 1 9 2.22ZM11 2v5a2 2 0 0 1-2 2H4v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2h-7Z"
                              clip-rule="evenodd"/>
                    </svg>
                </div>
                <div>
                    <h3 class="font-medium dark:text-white">Génération PDF</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">Tester les PV et PDFs</p>
                </div>
            </a>

            <a href="/tests/validation_calculs.php"
               class="flex items-center p-4 bg-white dark:bg-gray-700 rounded-lg shadow hover:shadow-md transition-shadow">
                <div class="w-10 h-10 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center mr-3">
                    <svg class="w-5 h-5 text-orange-600 dark:text-orange-400" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                </div>
                <div>
                    <h3 class="font-medium dark:text-white">Validation Calculs</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">Vérifier l'exactitude mathématique</p>
                </div>
            </a>

            <a href="/index.php"
               class="flex items-center p-4 bg-white dark:bg-gray-700 rounded-lg shadow hover:shadow-md transition-shadow">
                <div class="w-10 h-10 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center mr-3">
                    <svg class="w-5 h-5 text-purple-600 dark:text-purple-400" fill="currentColor" viewBox="0 0 24 24">
                        <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
                    </svg>
                </div>
                <div>
                    <h3 class="font-medium dark:text-white">Retour à l'Application</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">Interface principale</p>
                </div>
            </a>
        </div>
    </div>

    <!-- Note de sécurité -->
    <div class="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg mt-6">
        <div class="flex">
            <svg class="w-5 h-5 text-yellow-400 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 24 24">
                <path fill-rule="evenodd"
                      d="M8.485 2.495c.673-1.167 2.357-1.167 3.03 0l6.28 10.875c.673 1.167-.17 2.625-1.516 2.625H3.72c-1.347 0-2.189-1.458-1.515-2.625L8.485 2.495ZM12 9a.75.75 0 0 1 .75.75v3.5a.75.75 0 0 1-1.5 0v-3.5A.75.75 0 0 1 12 9Zm0 8a1 1 0 1 0 0-2 1 1 0 0 0 0 2Z"
                      clip-rule="evenodd"/>
            </svg>
            <div>
                <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-400">Note de Sécurité</h3>
                <p class="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
                    Cette page de tests est réservée aux contrôleurs et ne doit pas être accessible en production.
                    Assurez-vous de restreindre l'accès à cette page dans un environnement de production.
                </p>
            </div>
        </div>
    </div>
</div>

<?php
$pageContent = ob_get_clean();
include 'layout.php';
?>
