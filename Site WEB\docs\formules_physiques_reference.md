# Référence des Formules Physiques - FluidMotion Labs

## 🎯 Objectif
Documentation complète de toutes les formules physiques et mathématiques utilisées dans le système, avec leurs justifications théoriques et références.

## 📐 Formules de Rendement Hydraulique

### 1. Rendement Volumétrique (ηv)
```
ηv = (Qréel / Qthéorique) × 100
```
**Où :**
- `Qréel` : Débit réel mesuré (L/min)
- `Qthéorique` : Débit théorique nominal (L/min)

**Justification :** Le rendement volumétrique mesure l'efficacité du déplacement de fluide par rapport aux spécifications théoriques. Il ne peut jamais dépasser 100% car le débit réel ne peut être supérieur au débit théorique dans des conditions normales.

**Plage normale :** 85-98% pour les systèmes hydrauliques industriels

### 2. Rendement Mécanique (ηm)
```
ηm = (Phydraulique / Pmécanique) × 100
```
**Où :**
- `Phydraulique` : Puissance hydraulique réelle (W)
- `Pmécanique` : Puissance mécanique théorique (W)

**Justification :** Le rendement mécanique mesure l'efficacité de conversion de l'énergie mécanique en énergie hydraulique. Limité à 100% par les lois de la thermodynamique.

**Plage normale :** 75-92% pour les systèmes hydrauliques industriels

### 3. Rendement Global (ηg)
```
ηg = (ηv × ηm) / 100
```
**Justification :** Le rendement global est le produit des rendements volumétrique et mécanique, représentant l'efficacité totale du système.

**Plage normale :** 65-90% pour les systèmes hydrauliques industriels

## ⚡ Formules de Puissance Hydraulique

### Puissance Hydraulique Réelle
```
P = ΔP × Q
```
**Où :**
- `P` : Puissance hydraulique (W)
- `ΔP` : Différence de pression (Pa)
- `Q` : Débit volumique (m³/s)

**Conversion des unités :**
- Débit : `Q(m³/s) = Q(L/min) / 60000`
- Pression : `ΔP(Pa) = |P_CPA - P_CPB|`

### Puissance Théorique Simplifiée
```
P ≈ k × Q × H
```
**Où :**
- `k` : Facteur d'efficacité (≈ 0.18 pour rendement ~80%)
- `Q` : Débit (L/min)
- `H` : Hauteur manométrique équivalente (bar)

## 🌡️ Corrélations Physiques

### Viscosité-Température
```
ν = ν₀ - α × (T - T₀)
```
**Où :**
- `ν` : Viscosité cinématique (cSt)
- `ν₀` : Viscosité de référence (cSt)
- `α` : Coefficient de température (0.8 cSt/°C)
- `T` : Température actuelle (°C)
- `T₀` : Température de référence (40°C)

**Justification :** La viscosité des huiles hydrauliques diminue avec l'augmentation de température selon une relation approximativement linéaire dans la plage de fonctionnement normale.

## 🔧 Formules du Calculateur Hydraulique

### Surfaces de Vérin
```
Sf = π × (D/2)²     [Surface du fond]
St = π × (d/2)²     [Surface de la tige]
Sa = Sf - St        [Surface annulaire]
```
**Où :**
- `D` : Diamètre du piston (mm)
- `d` : Diamètre de la tige (mm)

### Débits
```
Q = 6 × S × V
```
**Où :**
- `Q` : Débit (L/min)
- `S` : Surface (cm²)
- `V` : Vitesse (m/s)

**Justification :** Formule de débit volumique avec facteur de conversion d'unités intégré.

### Vitesse
```
V = Course / Temps
```
**Où :**
- `V` : Vitesse (m/s)
- `Course` : Course du vérin (mm → m)
- `Temps` : Temps de sortie (s)

### Pressions dans le Circuit
```
P_charge = F / Sf                           [Pression due à la charge]
M4 = ΔP_T                                   [Perte de charge écoulement]
M3 = M4 + ΔP_BT                           [+ Perte distributeur]
M2 = (M3 / ratio) + P_charge              [+ Effet surface]
M1 = M2 + ΔP_PA                           [Pression nécessaire]
```

### Rendement du Circuit
```
η = P_charge / M1
```
**Limitation :** `η ≤ 1.0` (100%)

## 📊 Constantes Physiques

### Fluides Hydrauliques
- **Densité huile hydraulique :** 850 kg/m³
- **Viscosité nominale :** 46 cSt à 40°C
- **Plage de température :** 5-90°C

### Constantes Universelles
- **Gravité :** 9.81 m/s²
- **π :** 3.14159...

## 🔄 Conversions d'Unités

### Pression
- `1 bar = 100 000 Pa`
- `1 MPa = 10 bar`

### Débit
- `1 L/min = 1/60 000 m³/s`
- `1 m³/h = 16.67 L/min`

### Surface
- `1 cm² = 100 mm²`
- `1 m² = 10 000 cm²`

### Puissance
- `1 kW = 1000 W`
- `1 ch = 736 W`

## ⚖️ Limites Physiques Validées

### Rendements
- **Minimum :** 0%
- **Maximum :** 100% (limitation physique absolue)

### Pressions
- **Minimum :** 1 bar (pression atmosphérique relative)
- **Maximum :** 350 bar (limite sécurité systèmes industriels)

### Débits
- **Minimum :** 1 L/min
- **Maximum :** 150 L/min (plage typique essais)

### Températures
- **Minimum :** 5°C (point de congélation évité)
- **Maximum :** 90°C (limite thermique huiles)

## 🧪 Validation Expérimentale

### Méthodes de Vérification
1. **Tests avec données connues** : Validation avec résultats d'essais réels
2. **Comparaison littérature** : Vérification avec normes ISO 4413
3. **Cohérence physique** : Respect des lois de conservation
4. **Tests limites** : Comportement aux valeurs extrêmes

### Références Normatives
- **ISO 4413** : Transmissions hydrauliques - Règles générales
- **ISO 5598** : Transmissions hydrauliques - Vocabulaire
- **NF E48-600** : Vérins hydrauliques - Spécifications

## 📝 Notes d'Implémentation

### Précision Numérique
- **Arrondis :** 2 décimales pour les pourcentages
- **Seuils :** Valeurs < 0.01 considérées comme nulles
- **Débordement :** Protection contre division par zéro

### Gestion d'Erreurs
- **Valeurs négatives :** Forcées à zéro
- **Valeurs excessives :** Limitées aux maxima physiques
- **Données manquantes :** Valeurs par défaut cohérentes

## 🔍 Traçabilité des Modifications

### Version 1.0 (Initial)
- Formules de base implémentées
- Problème : Rendements > 100% possibles

### Version 2.0 (Corrigée)
- Ajout limitations physiques 0-100%
- Correction formule rendement volumétrique
- Amélioration génération données synthétiques
- Validation complète des calculs

---

**Dernière mise à jour :** [Date actuelle]  
**Validé par :** Système de tests automatisés  
**Prochaine révision :** Après intégration de nouvelles fonctionnalités
