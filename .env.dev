# Configuration d'environnement pour FluidMotion Web Application - DÉVELOPPEMENT
# Fichier de configuration spécifique au développement

# =============================================================================
# CONFIGURATION DE L'APPLICATION - DÉVELOPPEMENT
# =============================================================================

# Environnement de l'application
APP_ENV=development

# Mode debug activé pour le développement
APP_DEBUG=true

# URL de base de l'application en développement
APP_URL=http://localhost:8080

# Clé secrète de l'application (exemple pour développement - À CHANGER EN PRODUCTION)
APP_SECRET=dev_secret_key_change_in_production_123456789

# =============================================================================
# CONFIGURATION DE LA BASE DE DONNÉES - DÉVELOPPEMENT
# =============================================================================

# Nom de la base de données
DB_NAME=verins_db

# Utilisateur de la base de données
DB_USER=verins_user

# Mot de passe de l'utilisateur (exemple pour développement)
DB_PASSWORD=dev_password_123

# Mot de passe root de MariaDB (exemple pour développement)
DB_ROOT_PASSWORD=dev_root_password_123

# Hôte de la base de données (db pour Docker)
DB_HOST=db

# Port de la base de données
DB_PORT=3306

# =============================================================================
# CONFIGURATION DES PORTS - DÉVELOPPEMENT
# =============================================================================

# Port pour l'application web (8080 pour éviter les conflits)
WEB_PORT=8080

# Port pour PhpMyAdmin (développement)
PHPMYADMIN_PORT=8081

# Port pour la documentation
DOCS_PORT=3000

# =============================================================================
# CONFIGURATION PHP - DÉVELOPPEMENT
# =============================================================================

# Limite mémoire PHP augmentée pour le développement
PHP_MEMORY_LIMIT=512M

# Taille maximale des fichiers uploadés (augmentée pour les tests)
PHP_UPLOAD_MAX_FILESIZE=100M

# Taille maximale des données POST (augmentée pour les tests)
PHP_POST_MAX_SIZE=100M

# =============================================================================
# CONFIGURATION XDEBUG - DÉVELOPPEMENT
# =============================================================================

# Mode Xdebug (develop,debug,coverage)
XDEBUG_MODE=develop,debug

# Hôte client Xdebug
XDEBUG_CLIENT_HOST=host.docker.internal

# Port client Xdebug
XDEBUG_CLIENT_PORT=9003

# =============================================================================
# CONFIGURATION DOCKER - DÉVELOPPEMENT
# =============================================================================

# Target de build Docker (development)
BUILD_TARGET=development

# Image web pour le développement
WEB_IMAGE=ghcr.io/laprovidenceamiens/ciel2_bancdetest-dev:latest

# =============================================================================
# CONFIGURATION DE SÉCURITÉ - DÉVELOPPEMENT
# =============================================================================

# Clé JWT (exemple pour développement - À CHANGER EN PRODUCTION)
JWT_SECRET=dev_jwt_secret_key_123456789abcdef

# Durée de vie des tokens JWT (plus longue en développement)
JWT_EXPIRATION=7200

# Salt pour les mots de passe (exemple pour développement)
PASSWORD_SALT=dev_salt_123456789

# =============================================================================
# CONFIGURATION DE LOGGING - DÉVELOPPEMENT
# =============================================================================

# Niveau de log (debug pour le développement)
LOG_LEVEL=debug

# Rotation des logs (plus courte en développement)
LOG_ROTATION_DAYS=7

# =============================================================================
# CONFIGURATION UTILISATEUR DOCKER - DÉVELOPPEMENT
# =============================================================================

# UID et GID pour éviter les problèmes de permissions
# Utilisez votre UID/GID local : id -u && id -g
UID=1000
GID=1000

# =============================================================================
# CONFIGURATION PHPMYADMIN - DÉVELOPPEMENT
# =============================================================================

# URL absolue pour PhpMyAdmin
PMA_ABSOLUTE_URI=http://localhost:8081/

# =============================================================================
# NOTES DE DÉVELOPPEMENT
# =============================================================================

# Ce fichier contient des valeurs d'exemple pour le développement.
# NE JAMAIS utiliser ces valeurs en production !
# 
# Pour la production, utilisez des valeurs générées avec :
# - openssl rand -base64 32  (pour les mots de passe)
# - openssl rand -hex 32     (pour les clés hexadécimales)
#
# Fonctionnalités de développement activées :
# - Xdebug pour le débogage
# - Affichage des erreurs PHP
# - Logs détaillés
# - Hot-reload des fichiers
# - PhpMyAdmin accessible
# - Permissions de fichiers flexibles
