@echo off
REM Script de test pour vérifier le hot-reload

echo [TEST] Test du hot-reload - Montage dynamique du répertoire Site WEB

REM Créer un fichier de test
echo ^<?php echo "Hot-reload test: " . date('Y-m-d H:i:s'); ?^> > "Site WEB\test-hot-reload.php"

echo [TEST] Fichier de test créé: Site WEB\test-hot-reload.php

echo [TEST] Attente de 3 secondes pour que le fichier soit monté...
timeout /t 3 /nobreak >nul

echo [TEST] Test d'accès au fichier via le conteneur...
curl -s http://localhost:8080/test-hot-reload.php

if errorlevel 1 (
    echo [ERROR] Impossible d'accéder au fichier de test
    echo [INFO] Vérifiez que le conteneur web est démarré et accessible
) else (
    echo.
    echo [SUCCESS] Hot-reload fonctionne! Le fichier est accessible immédiatement.
)

echo.
echo [TEST] Nettoyage - Suppression du fichier de test...
del "Site WEB\test-hot-reload.php" >nul 2>&1

echo [TEST] Test terminé.
echo.
echo [INFO] Services disponibles:
echo [INFO] - Application Web: http://localhost:8080
echo [INFO] - PhpMyAdmin: http://localhost:8081
echo [INFO] - Documentation: http://localhost:3000
