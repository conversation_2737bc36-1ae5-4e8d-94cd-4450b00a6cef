<?php
/**
 * Script de Validation Automatique des Calculs
 * Exécute une série complète de tests pour valider tous les calculs du système
 */

session_start();

// Vérifier les permissions (contrôleur uniquement)
if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'controleur') {
    die('Accès refusé. Cette page nécessite un utilisateur contrôleur connecté.');
}

require_once(__DIR__ . '/tests/unit/ValidationCalculsTest.php');
require_once(__DIR__ . '/lib/rendement.php');
require_once(__DIR__ . '/lib/data_generator.php');

?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Validation Automatique des Calculs - FluidMotion Labs</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .test-success { color: #10b981; }
        .test-error { color: #ef4444; }
        .test-warning { color: #f59e0b; }
        .code-block { 
            background: #1f2937; 
            color: #f9fafb; 
            padding: 1rem; 
            border-radius: 0.5rem; 
            font-family: 'Courier New', monospace;
            overflow-x: auto;
        }
    </style>
</head>
<body class="bg-gray-100 dark:bg-gray-900">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-6xl mx-auto">
            
            <!-- En-tête -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                    🧪 Validation Automatique des Calculs
                </h1>
                <p class="text-gray-600 dark:text-gray-400">
                    Vérification complète de l'exactitude mathématique et physique de tous les calculs du système
                </p>
                <div class="mt-4 flex space-x-4">
                    <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
                        Utilisateur: <?php echo htmlspecialchars($_SESSION['user']['username']); ?>
                    </span>
                    <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
                        Rôle: <?php echo htmlspecialchars($_SESSION['user']['role']); ?>
                    </span>
                </div>
            </div>

            <!-- Résultats des tests -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
                <?php
                // Exécuter les tests de validation
                $test = new ValidationCalculsTest();
                $test->runAllTests();
                ?>
            </div>

            <!-- Tests de performance -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">⚡ Tests de Performance</h2>
                
                <?php
                echo "<h3 class='text-lg font-semibold mb-2'>Temps de Calcul des Rendements</h3>";
                
                // Test de performance sur le calcul de rendement
                $start_time = microtime(true);
                
                // Simuler un calcul de rendement
                try {
                    // Créer des données de test
                    $donnees_cpa = [];
                    $donnees_cpb = [];
                    
                    for ($i = 0; $i < 100; $i++) {
                        $donnees_cpa[] = [
                            'pressure_pascal' => 150000 + rand(-10000, 10000),
                            'flow_lpm' => 10 + rand(-1, 1),
                            'timestamp' => date('Y-m-d H:i:s')
                        ];
                        $donnees_cpb[] = [
                            'pressure_pascal' => 140000 + rand(-10000, 10000),
                            'flow_lpm' => 9.5 + rand(-1, 1),
                            'timestamp' => date('Y-m-d H:i:s')
                        ];
                    }
                    
                    // Simuler le calcul
                    $reflection = new ReflectionClass('Rendement');
                    $method = $reflection->getMethod('calculerMoyennePression');
                    $method->setAccessible(true);
                    
                    $pression_cpa = $method->invoke(null, $donnees_cpa);
                    $pression_cpb = $method->invoke(null, $donnees_cpb);
                    
                    $method_debit = $reflection->getMethod('calculerMoyenneDebit');
                    $method_debit->setAccessible(true);
                    $debit = $method_debit->invoke(null, $donnees_cpa, $donnees_cpb);
                    
                    $end_time = microtime(true);
                    $execution_time = ($end_time - $start_time) * 1000; // en millisecondes
                    
                    if ($execution_time < 100) {
                        echo "<div class='test-success'>✅ Calcul de rendement: " . number_format($execution_time, 2) . "ms (Excellent)</div>";
                    } elseif ($execution_time < 500) {
                        echo "<div class='test-warning'>⚠️ Calcul de rendement: " . number_format($execution_time, 2) . "ms (Acceptable)</div>";
                    } else {
                        echo "<div class='test-error'>❌ Calcul de rendement: " . number_format($execution_time, 2) . "ms (Trop lent)</div>";
                    }
                    
                } catch (Exception $e) {
                    echo "<div class='test-error'>❌ Erreur lors du test de performance: " . $e->getMessage() . "</div>";
                }
                ?>
            </div>

            <!-- Validation des données synthétiques -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">🔄 Validation des Données Synthétiques</h2>
                
                <?php
                echo "<h3 class='text-lg font-semibold mb-2'>Cohérence des Paramètres Générés</h3>";
                
                try {
                    $generator = new DataGenerator();
                    $reflection = new ReflectionClass($generator);
                    $method = $reflection->getMethod('generateRealisticProperties');
                    $method->setAccessible(true);
                    
                    $coherent_count = 0;
                    $total_tests = 20;
                    
                    for ($i = 0; $i < $total_tests; $i++) {
                        $params = $method->invoke($generator);
                        
                        // Extraire les valeurs numériques
                        $debit_nominal = floatval($params['debit_nominal']);
                        $puissance = floatval($params['puissance']);
                        $pression = floatval($params['pression_nominale']);
                        
                        // Vérifier la cohérence physique
                        $puissance_estimee = $debit_nominal * $pression * 0.18;
                        $ecart_puissance = abs($puissance - $puissance_estimee) / $puissance_estimee;
                        
                        if ($ecart_puissance < 0.5) { // Écart < 50%
                            $coherent_count++;
                        }
                    }
                    
                    $coherence_rate = ($coherent_count / $total_tests) * 100;
                    
                    if ($coherence_rate >= 80) {
                        echo "<div class='test-success'>✅ Cohérence des données: " . round($coherence_rate) . "% (" . $coherent_count . "/" . $total_tests . ")</div>";
                    } elseif ($coherence_rate >= 60) {
                        echo "<div class='test-warning'>⚠️ Cohérence des données: " . round($coherence_rate) . "% (" . $coherent_count . "/" . $total_tests . ")</div>";
                    } else {
                        echo "<div class='test-error'>❌ Cohérence des données: " . round($coherence_rate) . "% (" . $coherent_count . "/" . $total_tests . ")</div>";
                    }
                    
                } catch (Exception $e) {
                    echo "<div class='test-error'>❌ Erreur lors de la validation des données synthétiques: " . $e->getMessage() . "</div>";
                }
                ?>
            </div>

            <!-- Recommandations -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">💡 Recommandations</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                        <h3 class="font-semibold text-green-800 dark:text-green-400 mb-2">✅ Points Validés</h3>
                        <ul class="text-sm text-green-700 dark:text-green-300 space-y-1">
                            <li>• Rendements limités à 0-100%</li>
                            <li>• Formules physiquement cohérentes</li>
                            <li>• Conversions d'unités correctes</li>
                            <li>• Gestion des cas limites</li>
                        </ul>
                    </div>
                    
                    <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                        <h3 class="font-semibold text-blue-800 dark:text-blue-400 mb-2">🔧 Améliorations Continues</h3>
                        <ul class="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                            <li>• Exécuter ces tests régulièrement</li>
                            <li>• Ajouter de nouveaux cas de test</li>
                            <li>• Valider avec données réelles</li>
                            <li>• Documenter les modifications</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">🎯 Actions</h2>
                
                <div class="flex flex-wrap gap-4">
                    <a href="/tests.php" 
                       class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                        Retour aux Tests
                    </a>
                    
                    <a href="/data-generator.php" 
                       class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors">
                        Générateur de Données
                    </a>
                    
                    <button onclick="window.location.reload()" 
                            class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors">
                        Relancer les Tests
                    </button>
                    
                    <a href="/docs/formules_physiques_reference.md" 
                       class="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors">
                        Documentation Formules
                    </a>
                </div>
            </div>

        </div>
    </div>

    <script>
        // Auto-refresh toutes les 5 minutes pour surveillance continue
        setTimeout(() => {
            if (confirm('Relancer automatiquement les tests de validation ?')) {
                window.location.reload();
            }
        }, 300000); // 5 minutes
    </script>
</body>
</html>
