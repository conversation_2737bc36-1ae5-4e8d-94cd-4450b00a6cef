<?php
require_once(__DIR__ . '/../config/database.php');

class Courbe
{
    public static function create($essai_id, $type_courbe, $donnees, $mesure = null)
    {
        // Si aucune mesure n'est fournie, calculer une valeur moyenne à partir des données
        if ($mesure === null) {
            $mesure = self::calculateAverageMeasure($donnees);
        }

        $stmt = self::getDb()->prepare("
            INSERT INTO courbes (essai_id, type_courbe, mesure, donnees)
            VALUES (?, ?, ?, ?)
        ");
        return $stmt->execute([
            $essai_id,
            $type_courbe,
            $mesure,
            $donnees
        ]);
    }

    /**
     * Calculer une mesure moyenne à partir des données JSON
     */
    private static function calculateAverageMeasure($donnees_json)
    {
        if (is_string($donnees_json)) {
            $donnees = json_decode($donnees_json, true);
        } else {
            $donnees = $donnees_json;
        }

        if (!is_array($donnees) || empty($donnees)) {
            return 0.0;
        }

        $total_pressure = 0;
        $count = 0;

        foreach ($donnees as $point) {
            if (isset($point['pressure_pascal']) && is_numeric($point['pressure_pascal'])) {
                $total_pressure += $point['pressure_pascal'];
                $count++;
            }
        }

        return $count > 0 ? round($total_pressure / $count, 2) : 0.0;
    }

    // Créer une nouvelle courbe

    private static function getDb()
    {
        return Database::getInstance()->getConnection();
    }

    // Récupérer une courbe par son ID

    public static function getById($id)
    {
        $stmt = self::getDb()->prepare("
            SELECT c.*, e.type AS essai_type
            FROM courbes c
            LEFT JOIN essais e ON c.essai_id = e.id
            WHERE c.id = ?
        ");
        $stmt->execute([$id]);
        $courbe = $stmt->fetch(PDO::FETCH_ASSOC);
        return $courbe;
    }

    // Récupérer toutes les courbes d'un essai
    public static function getByEssaiId($essai_id, $type_courbe = null)
    {
        $query = "SELECT * FROM courbes WHERE essai_id = ?";
        $params = [$essai_id];

        if ($type_courbe !== null) {
            $query .= " AND type_courbe = ?";
            $params[] = $type_courbe;
        }

        $query .= " ORDER BY TIMESTAMP ASC";

        $stmt = self::getDb()->prepare($query);
        $stmt->execute($params);
        $courbes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        return $courbes;
    }

    // Récupérer une courbe spécifique d'un essai
    public static function getByEssaiAndType($essai_id, $type_courbe)
    {
        $stmt = self::getDb()->prepare("
            SELECT * FROM courbes
            WHERE essai_id = ? AND type_courbe = ?
            ORDER BY TIMESTAMP DESC
            LIMIT 1
        ");
        $stmt->execute([$essai_id, $type_courbe]);
        $courbe = $stmt->fetch(PDO::FETCH_ASSOC);
        return $courbe;
    }

    // Mettre à jour les données d'une courbe
    public static function updateDonnees($id, $donnees)
    {
        $stmt = self::getDb()->prepare("
            UPDATE courbes
            SET donnees = ?, TIMESTAMP = CURRENT_TIMESTAMP
            WHERE id = ?
        ");
        return $stmt->execute([json_encode($donnees), $id]);
    }

    // Supprimer une courbe
    public static function delete($id)
    {
        $stmt = self::getDb()->prepare("DELETE FROM courbes WHERE id = ?");
        return $stmt->execute([$id]);
    }

    // Supprimer toutes les courbes d'un essai
    public static function deleteByEssaiId($essai_id)
    {
        $stmt = self::getDb()->prepare("DELETE FROM courbes WHERE essai_id = ?");
        return $stmt->execute([$essai_id]);
    }

    // Vérifier si un essai a toutes ses courbes (CPA, CPB, Résultante)
    public static function hasAllCourbes($essai_id)
    {
        $stmt = self::getDb()->prepare("
            SELECT COUNT(DISTINCT type_courbe) AS count
            FROM courbes
            WHERE essai_id = ?
        ");
        $stmt->execute([$essai_id]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result && $result['count'] >= 2; // Au moins CPA et CPB pour le rendement
    }

    // Vérifier si un essai a au moins les courbes CPA et CPB
    public static function hasRequiredCourbes($essai_id)
    {
        $stmt = self::getDb()->prepare("
            SELECT COUNT(DISTINCT type_courbe) AS count
            FROM courbes
            WHERE essai_id = ? AND type_courbe IN ('CPA', 'CPB')
        ");
        $stmt->execute([$essai_id]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result && $result['count'] >= 2;
    }

    // Obtenir les dernières courbes
    public static function getRecent($limit = 10)
    {
        $stmt = self::getDb()->prepare("
            SELECT c.*, e.type AS essai_type
            FROM courbes c
            LEFT JOIN essais e ON c.essai_id = e.id
            ORDER BY c.TIMESTAMP DESC
            LIMIT ?
        ");
        $stmt->execute([$limit]);
        $courbes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        return $courbes;
    }

    /**
     * Marquer une courbe comme synthétique
     */
    public static function markAsSynthetic($essai_id, $type_courbe)
    {
        $stmt = self::getDb()->prepare("
            UPDATE courbes SET is_synthetic = 1
            WHERE essai_id = ? AND type_courbe = ?
            ORDER BY id DESC LIMIT 1
        ");
        return $stmt->execute([$essai_id, $type_courbe]);
    }

    /**
     * Compter les courbes par type pour un essai
     */
    public static function countByTypeForEssai($essai_id, $types = ['CPA', 'CPB'])
    {
        $placeholders = str_repeat('?,', count($types) - 1) . '?';
        $stmt = self::getDb()->prepare("
            SELECT COUNT(DISTINCT type_courbe) as count
            FROM courbes
            WHERE essai_id = ? AND type_courbe IN ($placeholders)
        ");
        $params = array_merge([$essai_id], $types);
        $stmt->execute($params);
        return $stmt->fetchColumn();
    }

    /**
     * Supprimer toutes les courbes synthétiques
     */
    public static function deleteSynthetic()
    {
        $stmt = self::getDb()->prepare("DELETE FROM courbes WHERE is_synthetic = 1");
        $stmt->execute();
        return $stmt->rowCount();
    }

    /**
     * Supprimer toutes les courbes
     */
    public static function deleteAll()
    {
        $stmt = self::getDb()->prepare("DELETE FROM courbes");
        $stmt->execute();
        $count = $stmt->rowCount();

        // Réinitialiser l'auto-increment
        self::getDb()->exec("ALTER TABLE courbes AUTO_INCREMENT = 1");

        return $count;
    }

    /**
     * Récupérer les courbes avec pagination et optimisation
     */
    public static function getByEssaiIdPaginated($essai_id, $type_courbe = null, $page = 1, $pageSize = 50)
    {
        $offset = ($page - 1) * $pageSize;

        $query = "SELECT * FROM courbes WHERE essai_id = ?";
        $params = [$essai_id];

        if ($type_courbe !== null) {
            $query .= " AND type_courbe = ?";
            $params[] = $type_courbe;
        }

        // Compter le total
        $countQuery = str_replace("SELECT *", "SELECT COUNT(*)", $query);
        $stmt = self::getDb()->prepare($countQuery);
        $stmt->execute($params);
        $total = $stmt->fetchColumn();

        // Récupérer les données paginées
        $query .= " ORDER BY TIMESTAMP ASC LIMIT ? OFFSET ?";
        $params[] = $pageSize;
        $params[] = $offset;

        $stmt = self::getDb()->prepare($query);
        $stmt->execute($params);
        $courbes = $stmt->fetchAll(PDO::FETCH_ASSOC);

        return [
            'data' => $courbes,
            'pagination' => [
                'current_page' => $page,
                'page_size' => $pageSize,
                'total_items' => $total,
                'total_pages' => ceil($total / $pageSize),
                'has_next' => $page < ceil($total / $pageSize),
                'has_prev' => $page > 1
            ]
        ];
    }

    /**
     * Obtenir les statistiques d'une courbe
     */
    public static function getStatistics($essai_id, $type_courbe)
    {
        $courbes = self::getByEssaiId($essai_id, $type_courbe);

        if (empty($courbes)) {
            return null;
        }

        $mesures = array_column($courbes, 'mesure');
        $mesures = array_filter($mesures, 'is_numeric');

        if (empty($mesures)) {
            return null;
        }

        sort($mesures);
        $count = count($mesures);

        return [
            'count' => $count,
            'min' => min($mesures),
            'max' => max($mesures),
            'average' => round(array_sum($mesures) / $count, 2),
            'median' => $count % 2 === 0
                ? round(($mesures[$count / 2 - 1] + $mesures[$count / 2]) / 2, 2)
                : $mesures[floor($count / 2)],
            'std_deviation' => self::calculateStandardDeviation($mesures)
        ];
    }

    /**
     * Calculer l'écart-type
     */
    private static function calculateStandardDeviation($values)
    {
        $count = count($values);
        if ($count <= 1) return 0;

        $mean = array_sum($values) / $count;
        $variance = array_sum(array_map(function ($x) use ($mean) {
                return pow($x - $mean, 2);
            }, $values)) / ($count - 1);

        return round(sqrt($variance), 2);
    }
}
