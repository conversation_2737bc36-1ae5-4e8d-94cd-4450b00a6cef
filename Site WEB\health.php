<?php
/**
 * Point de contrôle de santé pour l'application FluidMotion
 * Utilisé par Docker et les systèmes de monitoring
 */

header('Content-Type: application/json');
header('Cache-Control: no-cache, no-store, must-revalidate');

$health = [
    'status' => 'ok',
    'timestamp' => date('c'),
    'version' => '1.0.0',
    'checks' => []
];

$overallStatus = true;

// Vérification de la base de données
try {
    require_once 'config/database.php';
    $db = Database::getInstance();
    $connection = Database::getConnection();
    
    // Test simple de connexion
    $stmt = $connection->query('SELECT 1');
    $result = $stmt->fetch();
    
    if ($result) {
        $health['checks']['database'] = [
            'status' => 'ok',
            'message' => 'Connexion à la base de données réussie'
        ];
    } else {
        throw new Exception('Échec du test de connexion');
    }
} catch (Exception $e) {
    $health['checks']['database'] = [
        'status' => 'error',
        'message' => 'Erreur de connexion à la base de données: ' . $e->getMessage()
    ];
    $overallStatus = false;
}

// Vérification des répertoires critiques
$directories = [
    'backups' => '/var/www/html/backups',
    'pdf_exports' => '/var/www/html/pdf_exports',
    'temp' => '/var/www/html/temp'
];

foreach ($directories as $name => $path) {
    if (is_dir($path) && is_writable($path)) {
        $health['checks']['directory_' . $name] = [
            'status' => 'ok',
            'message' => "Répertoire $name accessible et en écriture"
        ];
    } else {
        $health['checks']['directory_' . $name] = [
            'status' => 'error',
            'message' => "Répertoire $name non accessible ou non inscriptible"
        ];
        $overallStatus = false;
    }
}

// Vérification des extensions PHP critiques
$requiredExtensions = ['pdo', 'pdo_mysql', 'gd', 'zip', 'mbstring'];
foreach ($requiredExtensions as $extension) {
    if (extension_loaded($extension)) {
        $health['checks']['php_extension_' . $extension] = [
            'status' => 'ok',
            'message' => "Extension PHP $extension chargée"
        ];
    } else {
        $health['checks']['php_extension_' . $extension] = [
            'status' => 'error',
            'message' => "Extension PHP $extension manquante"
        ];
        $overallStatus = false;
    }
}

// Vérification de Composer et des dépendances
if (file_exists('/var/www/html/vendor/autoload.php')) {
    $health['checks']['composer'] = [
        'status' => 'ok',
        'message' => 'Dépendances Composer installées'
    ];
} else {
    $health['checks']['composer'] = [
        'status' => 'warning',
        'message' => 'Dépendances Composer non trouvées'
    ];
}

// Vérification de la mémoire disponible
$memoryLimit = ini_get('memory_limit');
$memoryUsage = memory_get_usage(true);
$memoryPeak = memory_get_peak_usage(true);

$health['checks']['memory'] = [
    'status' => 'ok',
    'message' => 'Utilisation mémoire normale',
    'details' => [
        'limit' => $memoryLimit,
        'current_usage' => round($memoryUsage / 1024 / 1024, 2) . ' MB',
        'peak_usage' => round($memoryPeak / 1024 / 1024, 2) . ' MB'
    ]
];

// Statut global
if (!$overallStatus) {
    $health['status'] = 'error';
    http_response_code(503); // Service Unavailable
} else {
    http_response_code(200); // OK
}

// Retourner la réponse JSON
echo json_encode($health, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
exit;
?>
