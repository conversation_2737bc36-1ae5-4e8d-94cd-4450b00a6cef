<?php
require_once '../auth.php';

$auth = Auth::validateToken();
$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        if (isset($_GET['id'])) {
            $user = User::getById($_GET['id']);
            sendJSON($user);
        } else {
            $users = User::getAllUsers();
            sendJSON($users);
        }
        break;

    case 'POST':
        if ($auth['role'] !== 'controleur') {
            sendJSON(['error' => 'Permission refusée'], 403);
        }
        $data = getRequestData();
        if (User::create($data['username'], $data['password'], $data['role'])) {
            sendJSON(['message' => 'Utilisateur créé']);
        }
        sendJSON(['error' => 'Erreur de création'], 500);
        break;

    case 'PUT':
        if ($auth['role'] !== 'controleur') {
            sendJSON(['error' => 'Permission refusée'], 403);
        }
        $data = getRequestData();
        if (User::update($data['id'], $data['username'], $data['role'])) {
            sendJSON(['message' => 'Utilisateur mis à jour']);
        }
        sendJSON(['error' => 'Erreur de mise à jour'], 500);
        break;

    case 'DELETE':
        if ($auth['role'] !== 'controleur') {
            sendJSON(['error' => 'Permission refusée'], 403);
        }
        if (User::delete($_GET['id'])) {
            sendJSON(['message' => 'Utilisateur supprimé']);
        }
        sendJSON(['error' => 'Erreur de suppression'], 500);
        break;

    default:
        sendJSON(['error' => 'Méthode non autorisée'], 405);
}
