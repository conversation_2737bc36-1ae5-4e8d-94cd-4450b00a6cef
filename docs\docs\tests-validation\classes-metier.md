---
sidebar_position: 11
title: Tests des Classes Métier
description: Tests de validation des classes métier et composants techniques
keywords: [classes, métier, User, Tag, ModeleEssai, ObjDiff]
---

# Tests des Classes Métier

Cette section présente les tests de validation des classes métier et composants techniques de FluidMotion Labs.

## 🎯 Objectifs des Tests

- Valider la gestion des utilisateurs
- Vérifier la gestion des tags
- Contrôler les modèles d'essais
- Tester le système de différences d'objets

## 📊 Vue d'Ensemble

| **Module** | **Classes Métier** |
|------------|---------------------|
| **Nombre de tests** | **4 tests** |
| **Criticité** | **Majeure** |
| **Couverture** | **95%** |
| **Profils concernés** | **Contrôleur + Technique** |

## 🧪 Tests Détaillés

### USR-001 : Gestion des Utilisateurs

| **Aspect** | **Détail** |
|------------|------------|
| **Objectif** | Valider la gestion des utilisateurs |
| **Préconditions** | - Base de données accessible |
| **Étapes de Test** | 1. Créer un utilisateur avec User::create()<br />2. Authentifier avec User::authenticate()<br />3. Récupérer avec User::getById()<br />4. Lister avec User::getAllUsers()<br />5. Tester le hachage des mots de passe |
| **Résultats Attendus** | - Utilisateur créé avec mot de passe haché<br />- Authentification fonctionnelle<br />- Récupération des données correcte<br />- Liste complète des utilisateurs<br />- Sécurité des mots de passe |
| **Critères de Réussite** | ✅ Classe User complète |

### TAG-001 : Gestion des Tags

| **Aspect** | **Détail** |
|------------|------------|
| **Objectif** | Valider la gestion des tags |
| **Préconditions** | - Base de données accessible |
| **Étapes de Test** | 1. Créer un tag avec Tag::create()<br />2. Vérifier l'existence avec Tag::exists()<br />3. Associer à une affaire avec Tag::associerAffaire()<br />4. Récupérer les tags d'une affaire<br />5. Dissocier avec Tag::dissocierAffaire() |
| **Résultats Attendus** | - Tags créés et stockés<br />- Vérification d'existence fonctionnelle<br />- Associations affaires-tags correctes<br />- Récupération des associations<br />- Dissociation propre |
| **Critères de Réussite** | ✅ Classe Tag opérationnelle |

### MOD-001 : Modèles d'Essais

| **Aspect** | **Détail** |
|------------|------------|
| **Objectif** | Valider les modèles d'essais |
| **Préconditions** | - Base de données accessible |
| **Étapes de Test** | 1. Créer un modèle avec ModeleEssai::create()<br />2. Récupérer avec ModeleEssai::getById()<br />3. Lister avec ModeleEssai::getAll()<br />4. Utiliser pour créer un essai<br />5. Vérifier la cohérence des données |
| **Résultats Attendus** | - Modèles créés et stockés<br />- Récupération par ID fonctionnelle<br />- Liste complète disponible<br />- Utilisation pour création d'essais<br />- Données cohérentes |
| **Critères de Réussite** | ✅ Modèles d'essais fonctionnels |

### OBJ-001 : Système de Différences d'Objets

| **Aspect** | **Détail** |
|------------|------------|
| **Objectif** | Valider le système de différences d'objets |
| **Préconditions** | - Objets à comparer disponibles |
| **Étapes de Test** | 1. Comparer deux objets avec ObjDiff::compareForHistory()<br />2. Sauvegarder l'historique avec ObjDiff::saveToHistory()<br />3. Vérifier le formatage des valeurs<br />4. Tester avec objets identiques<br />5. Tester avec objets très différents |
| **Résultats Attendus** | - Différences détectées correctement<br />- Historique sauvegardé en base<br />- Formatage des valeurs approprié<br />- Gestion des cas limites<br />- Performance acceptable |
| **Critères de Réussite** | ✅ Système de diff opérationnel |

## 🏗️ Architecture des Classes

### 👤 Classe User

```php
class User {
    public static function create($username, $password, $role)
    public static function authenticate($username, $password)
    public static function getById($id)
    public static function getAllUsers()
    public function updatePassword($newPassword)
    public function hasRole($role)
}
```

### 🏷️ Classe Tag

```php
class Tag {
    public static function create($nom)
    public static function exists($nom)
    public static function associerAffaire($tagId, $affaireId)
    public static function dissocierAffaire($tagId, $affaireId)
    public static function getTagsAffaire($affaireId)
    public static function getAll()
}
```

### 📋 Classe ModeleEssai

```php
class ModeleEssai {
    public static function create($nom, $type, $parametres)
    public static function getById($id)
    public static function getAll()
    public static function update($id, $data)
    public static function delete($id)
    public function toArray()
}
```

### 🔍 Classe ObjDiff

```php
class ObjDiff {
    public static function compareForHistory($oldObj, $newObj)
    public static function saveToHistory($table, $id, $changes, $userId)
    public static function formatValue($value)
    public static function getHistory($table, $id)
}
```

## 🎭 Tests par Profil

### 👨‍💼 Tests Contrôleur
- **USR-001** : Gestion utilisateurs complète ✅
- **TAG-001** : Gestion tags avancée ✅
- **MOD-001** : Modèles d'essais complets ✅
- **OBJ-001** : Historique et différences ✅

### 🔧 Tests Technique
- **USR-001** : Tests techniques User ✅
- **TAG-001** : Tests techniques Tag ✅
- **MOD-001** : Tests techniques ModeleEssai ✅
- **OBJ-001** : Tests techniques ObjDiff ✅

## 🚨 Points de Vigilance

### Sécurité
- Hachage sécurisé des mots de passe
- Validation des données d'entrée
- Protection contre les injections SQL
- Gestion sécurisée des sessions

### Performance
- Requêtes optimisées
- Index sur les champs fréquents
- Cache pour les données statiques
- Pagination pour les listes

### Intégrité
- Contraintes de base de données
- Validation des relations
- Gestion des suppressions en cascade
- Cohérence des données

## 📋 Checklist de Validation

### Avant les Tests
- [ ] Base de données configurée
- [ ] Classes métier chargées
- [ ] Données de test préparées
- [ ] Environnement de test isolé

### Tests de la Classe User
- [ ] Création utilisateur avec hachage
- [ ] Authentification fonctionnelle
- [ ] Récupération par ID
- [ ] Liste des utilisateurs
- [ ] Gestion des rôles

### Tests de la Classe Tag
- [ ] Création et existence
- [ ] Association avec affaires
- [ ] Récupération des associations
- [ ] Dissociation propre
- [ ] Gestion des doublons

### Tests de la Classe ModeleEssai
- [ ] CRUD complet
- [ ] Utilisation pour création essais
- [ ] Validation des paramètres
- [ ] Cohérence des données

### Tests de la Classe ObjDiff
- [ ] Comparaison d'objets
- [ ] Sauvegarde historique
- [ ] Formatage des valeurs
- [ ] Gestion cas limites

## 🔗 Liens Connexes

- [**Tests d'Authentification**](./authentification) - Utilise la classe User
- [**Tests des Affaires**](./affaires) - Utilise les classes Tag et ObjDiff
- [**Tests des Essais**](./essais) - Utilise la classe ModeleEssai
- [**Guide d'Exécution**](./guide-execution) - Méthodologie de test

---

:::tip Conseil Technique
Testez les classes métier de manière isolée pour identifier rapidement les problèmes et faciliter le débogage.
:::

:::warning Attention
Les tests des classes métier peuvent modifier la base de données. Utilisez un environnement de test isolé.
:::

:::info Navigation
**Précédent** : [Tests API](./api)  
**Suivant** : [Tests de Performance](./performance)
:::
