<?php
require_once(__DIR__ . '/../config/database.php');

class User
{
    public static function create($username, $password, $role)
    {
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        $stmt = self::getDb()->prepare("INSERT INTO users (username, password, role) VALUES (?, ?, ?)");
        return $stmt->execute([$username, $hashedPassword, $role]);
    }

    // Créer un nouvel utilisateur

    private static function getDb()
    {
        return Database::getInstance()->getConnection();
    }

    // Récupérer un utilisateur par son ID

    public static function getById($id)
    {
        $stmt = self::getDb()->prepare("SELECT id, username, role, created_at FROM users WHERE id = ?");
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    // Récupérer un utilisateur par son nom d'utilisateur
    public static function getByUsername($username)
    {
        $stmt = self::getDb()->prepare("SELECT id, username, role, created_at FROM users WHERE username = ?");
        $stmt->execute([$username]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    // Mettre à jour un utilisateur
    public static function update($id, $username, $role)
    {
        $stmt = self::getDb()->prepare("UPDATE users SET username = ?, role = ? WHERE id = ?");
        return $stmt->execute([$username, $role, $id]);
    }

    // Modifier le mot de passe d'un utilisateur
    public static function updatePassword($id, $newPassword)
    {
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
        $stmt = self::getDb()->prepare("UPDATE users SET password = ? WHERE id = ?");
        return $stmt->execute([$hashedPassword, $id]);
    }

    // Supprimer un utilisateur
    public static function delete($id)
    {
        $stmt = self::getDb()->prepare("DELETE FROM users WHERE id = ?");
        return $stmt->execute([$id]);
    }

    // Vérifier les identifiants de connexion
    public static function authenticate($username, $password)
    {
        $stmt = self::getDb()->prepare("SELECT * FROM users WHERE username = ?");
        $stmt->execute([$username]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($user && password_verify($password, $user['password'])) {
            return $user;
        }
        return false;
    }

    // Récupérer tous les utilisateurs
    public static function getAllUsers()
    {
        $stmt = self::getDb()->query("SELECT id, username, role, created_at FROM users");
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Vérifier si un nom d'utilisateur existe déjà
    public static function usernameExists($username)
    {
        $stmt = self::getDb()->prepare("SELECT COUNT(*) FROM users WHERE username = ?");
        $stmt->execute([$username]);
        return $stmt->fetchColumn() > 0;
    }
}
