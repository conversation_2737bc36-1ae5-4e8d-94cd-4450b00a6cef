# Documentation FluidMotion Labs

Documentation opérationnelle pour le système de gestion des essais hydrauliques FluidMotion Labs.

Ce site est construit avec [Docusaurus](https://docusaurus.io/), un générateur de sites statiques moderne.

## 🚀 Installation

```bash
# Installer les dépendances avec Bun
bun install
```

## 🛠️ Développement Local

```bash
# Démarrer le serveur de développement
bun run start
```

Cette commande démarre un serveur de développement local et ouvre une fenêtre de navigateur. La plupart des modifications sont reflétées en direct sans avoir à redémarrer le serveur.

## 📦 Build

**Important** : En raison de problèmes de compatibilité entre Bun et Docusaurus, utilisez Node.js pour le build :

```bash
# Build de production (utilise Node.js pour la compatibilité)
npm run build
```

Cette commande génère le contenu statique dans le répertoire `build` et peut être servi en utilisant n'importe quel service d'hébergement de contenu statique.

## 🐳 Docker

### Développement

```bash
# Construire et exécuter avec Docker Compose
docker-compose up docs-dev

# Ou manuellement
docker build --target development -t fluidmotion-docs-dev .
docker run -p 3000:3000 -v $(pwd):/app -v /app/node_modules fluidmotion-docs-dev
```

### Production

```bash
# Construire et exécuter avec Docker Compose
docker-compose up docs-prod

# Ou manuellement
docker build --target production -t fluidmotion-docs-prod .
docker run -p 8080:80 fluidmotion-docs-prod
```

## 🚀 Déploiement

Pour GitHub Pages :

```bash
# Avec SSH
USE_SSH=true bun run deploy

# Sans SSH
GIT_USER=<Votre nom d'utilisateur GitHub> bun run deploy
```

## 🔧 Scripts Disponibles

- `bun run start` - Serveur de développement
- `npm run build` - Build de production (recommandé)
- `bun run serve` - Servir le site construit
- `bun run clear` - Nettoyer le cache
- `bun run typecheck` - Vérification TypeScript

## 🚨 Notes Importantes

- **Bun** : Utilisé pour la gestion des dépendances et le développement
- **Node.js** : Utilisé pour le build de production (compatibilité)
- **Docker** : Images multi-étapes optimisées pour développement et production
