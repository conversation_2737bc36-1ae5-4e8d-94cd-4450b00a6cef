<?php
require_once __DIR__ . '/../core/BaseController.php';
require_once __DIR__ . '/../../lib/pv.php';

class PVController extends BaseController
{
    public function handle()
    {
        switch ($this->method) {
            case 'GET':
                return $this->index();
            case 'POST':
                return $this->create();
            case 'PUT':
                return $this->update();
            case 'DELETE':
                return $this->delete();
            default:
                $this->error('Méthode non autorisée', 405);
        }
    }

    private function index()
    {
        if (isset($this->data['id'])) {
            $pv = PV::getById($this->data['id']);
            if (!$pv) return $this->error('PV non trouvé', 404);
            $pv['attachments'] = PVAttachment::getByPVId($pv['id']);
            return $this->json($pv);
        }
        if (isset($this->data['affaire_id'])) {
            return $this->json(PV::getByAffaireId($this->data['affaire_id']));
        }
        return $this->json(PV::getAll());
    }

    private function create()
    {
        if (!isset($this->data['numero']) || !isset($this->data['affaire_id'])) {
            return $this->error('Données manquantes');
        }

        if (PV::create(
            $this->data['numero'],
            $this->data['affaire_id'],
            $this->data['contenu'],
            $this->user['id']
        )) {
            return $this->json(['message' => 'PV créé'], 201);
        }
        return $this->error('Erreur de création', 500);
    }

    private function update()
    {
        if (!isset($this->data['id'])) {
            return $this->error('ID manquant');
        }

        if (PV::update(
            $this->data['id'],
            $this->data['numero'],
            $this->data['contenu'],
            $this->data['statut']
        )) {
            return $this->json(['message' => 'PV mis à jour']);
        }
        return $this->error('Erreur de mise à jour', 500);
    }

    private function delete()
    {
        if (!isset($this->data['id'])) {
            return $this->error('ID manquant');
        }

        // Supprimer les pièces jointes avant le PV
        PVAttachment::deleteByPVId($this->data['id']);

        if (PV::delete($this->data['id'])) {
            return $this->json(['message' => 'PV supprimé']);
        }
        return $this->error('Erreur de suppression', 500);
    }
}
