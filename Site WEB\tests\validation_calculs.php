<?php
/**
 * Test de Validation Complète des Calculs - FluidMotion Labs
 * Vérifie l'exactitude mathématique et physique de tous les calculs du système
 */

session_start();

// Vérifier les permissions (contrôleur uniquement)
if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'controleur') {
    die('Accès refusé. Cette page nécessite un utilisateur contrôleur connecté.');
}

require_once(__DIR__ . '/../lib/rendement.php');
require_once(__DIR__ . '/../lib/data_generator.php');
require_once(__DIR__ . '/unit/ValidationCalculsTest.php');

// Fonction pour formater le temps d'exécution
function formatExecutionTime($start_time) {
    $execution_time = (microtime(true) - $start_time) * 1000;
    return number_format($execution_time, 2) . 'ms';
}

// Fonction pour afficher un résultat de test
function displayTestResult($condition, $message, $details = '') {
    $icon = $condition ? '✅' : '❌';
    $class = $condition ? 'text-green-600' : 'text-red-600';
    echo "<div class='$class font-medium'>$icon $message</div>";
    if ($details && !$condition) {
        echo "<div class='text-gray-600 text-sm ml-6'>$details</div>";
    }
}

ob_start();
?>

<div class="p-6">
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold dark:text-white">🧮 Validation Complète des Calculs</h1>
            <p class="text-gray-600 dark:text-gray-400 mt-2">Vérification de l'exactitude mathématique et physique</p>
        </div>
        <div class="text-right">
            <a href="/tests.php" class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors">
                ← Retour aux Tests
            </a>
        </div>
    </div>

    <!-- Informations du test -->
    <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg mb-6">
        <h2 class="text-lg font-semibold text-blue-800 dark:text-blue-400 mb-2">ℹ️ Informations du Test</h2>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div><span class="font-medium">Utilisateur:</span> <?php echo htmlspecialchars($_SESSION['user']['username']); ?></div>
            <div><span class="font-medium">Rôle:</span> <?php echo htmlspecialchars($_SESSION['user']['role']); ?></div>
            <div><span class="font-medium">Date:</span> <?php echo date('d/m/Y H:i:s'); ?></div>
            <div><span class="font-medium">PHP:</span> <?php echo PHP_VERSION; ?></div>
        </div>
    </div>

    <!-- Tests de validation -->
    <div class="space-y-6">
        
        <!-- Test 1: Formules de Rendement -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 class="text-2xl font-bold dark:text-white mb-4">📐 Test des Formules de Rendement</h2>
            
            <?php
            $start_time = microtime(true);
            $tests_passed = 0;
            $tests_total = 0;
            
            echo "<h3 class='text-lg font-semibold mb-3'>Validation des Calculs de Rendement</h3>";
            
            try {
                // Test rendement volumétrique = 100% quand débit réel = débit théorique
                $tests_total++;
                $parametres_theoriques = array(
                    'debit_nominal' => '10 L/min',
                    'puissance' => '100 W'
                );
                
                $essai_mock = array(
                    'parametre_theorique' => json_encode($parametres_theoriques)
                );

                $reflection = new ReflectionClass('Rendement');
                $method = $reflection->getMethod('calculerRendements');
                $method->setAccessible(true);
                
                $result = $method->invoke(null, 150000, 100000, 10, $essai_mock);
                
                $rendement_ok = ($result['rendement_volumetrique'] >= 0 && $result['rendement_volumetrique'] <= 100 &&
                               $result['rendement_mecanique'] >= 0 && $result['rendement_mecanique'] <= 100 &&
                               $result['rendement_global'] >= 0 && $result['rendement_global'] <= 100);
                
                if ($rendement_ok) {
                    $tests_passed++;
                    displayTestResult(true, "Rendements dans la plage 0-100%");
                    echo "<div class='ml-6 text-sm text-gray-600 mt-1'>";
                    echo "Volumétrique: " . round($result['rendement_volumetrique'], 2) . "% | ";
                    echo "Mécanique: " . round($result['rendement_mecanique'], 2) . "% | ";
                    echo "Global: " . round($result['rendement_global'], 2) . "%";
                    echo "</div>";
                } else {
                    displayTestResult(false, "Rendements hors plage 0-100%", "Vérifier les formules de calcul");
                }
                
                // Test cohérence formule globale
                $tests_total++;
                $expected_global = ($result['rendement_volumetrique'] * $result['rendement_mecanique']) / 100;
                $global_coherent = abs($result['rendement_global'] - $expected_global) < 0.01;
                
                if ($global_coherent) {
                    $tests_passed++;
                    displayTestResult(true, "Formule rendement global cohérente");
                } else {
                    displayTestResult(false, "Formule rendement global incohérente", 
                        "Attendu: " . round($expected_global, 2) . "%, Obtenu: " . $result['rendement_global'] . "%");
                }
                
            } catch (Exception $e) {
                displayTestResult(false, "Erreur lors du test de rendement", $e->getMessage());
            }
            
            echo "<div class='mt-4 text-sm text-gray-500'>Temps d'exécution: " . formatExecutionTime($start_time) . "</div>";
            ?>
        </div>

        <!-- Test 2: Conversions d'Unités -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 class="text-2xl font-bold dark:text-white mb-4">🔄 Test des Conversions d'Unités</h2>
            
            <?php
            $start_time = microtime(true);
            
            echo "<h3 class='text-lg font-semibold mb-3'>Validation des Conversions</h3>";
            
            // Test conversion L/min vers m³/s
            $tests_total++;
            $debit_lmin = 60; // L/min
            $debit_m3s = $debit_lmin / 60000; // Conversion utilisée dans le code
            $expected_m3s = 0.001; // 60 L/min = 0.001 m³/s
            
            if (abs($debit_m3s - $expected_m3s) < 0.0001) {
                $tests_passed++;
                displayTestResult(true, "Conversion L/min vers m³/s correcte");
                echo "<div class='ml-6 text-sm text-gray-600'>60 L/min = 0.001 m³/s ✓</div>";
            } else {
                displayTestResult(false, "Conversion L/min vers m³/s incorrecte", 
                    "Attendu: 0.001 m³/s, Obtenu: " . $debit_m3s . " m³/s");
            }
            
            // Test conversion Pascal vers bar
            $tests_total++;
            $pression_pascal = 100000; // Pa
            $pression_bar = $pression_pascal / 100000; // Conversion
            $expected_bar = 1.0; // 100000 Pa = 1 bar
            
            if (abs($pression_bar - $expected_bar) < 0.0001) {
                $tests_passed++;
                displayTestResult(true, "Conversion Pascal vers bar correcte");
                echo "<div class='ml-6 text-sm text-gray-600'>100000 Pa = 1 bar ✓</div>";
            } else {
                displayTestResult(false, "Conversion Pascal vers bar incorrecte", 
                    "Attendu: 1 bar, Obtenu: " . $pression_bar . " bar");
            }
            
            echo "<div class='mt-4 text-sm text-gray-500'>Temps d'exécution: " . formatExecutionTime($start_time) . "</div>";
            ?>
        </div>

        <!-- Test 3: Cohérence des Données Générées -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 class="text-2xl font-bold dark:text-white mb-4">🔄 Test de Cohérence des Données</h2>
            
            <?php
            $start_time = microtime(true);
            
            echo "<h3 class='text-lg font-semibold mb-3'>Validation du Générateur de Données</h3>";
            
            try {
                $generator = new DataGenerator();
                $reflection = new ReflectionClass($generator);
                $method = $reflection->getMethod('generateRealisticProperties');
                $method->setAccessible(true);
                
                $coherent_count = 0;
                $test_count = 10;
                
                for ($i = 0; $i < $test_count; $i++) {
                    $params = $method->invoke($generator);
                    
                    $debit_nominal = floatval($params['debit_nominal']);
                    $puissance = floatval($params['puissance']);
                    $pression = floatval($params['pression_nominale']);
                    
                    // Vérifier les plages réalistes
                    if ($debit_nominal > 0 && $debit_nominal <= 30 && 
                        $puissance > 0 && $puissance <= 500 &&
                        $pression > 0 && $pression <= 350) {
                        $coherent_count++;
                    }
                }
                
                $tests_total++;
                if ($coherent_count == $test_count) {
                    $tests_passed++;
                    displayTestResult(true, "Données générées dans les plages réalistes");
                    echo "<div class='ml-6 text-sm text-gray-600'>$coherent_count/$test_count échantillons valides</div>";
                } else {
                    displayTestResult(false, "Données générées hors plages réalistes", 
                        "$coherent_count/$test_count échantillons valides");
                }
                
                // Test cohérence physique
                $tests_total++;
                $physical_coherent = 0;
                
                for ($i = 0; $i < 5; $i++) {
                    $params = $method->invoke($generator);
                    $debit = floatval($params['debit_nominal']);
                    $puissance = floatval($params['puissance']);
                    $pression = floatval($params['pression_nominale']);
                    
                    // Vérifier la cohérence P ≈ k × Q × H
                    $puissance_estimee = $debit * $pression * 0.18;
                    $ecart = abs($puissance - $puissance_estimee) / max($puissance_estimee, 1);
                    
                    if ($ecart < 0.5) { // Écart < 50%
                        $physical_coherent++;
                    }
                }
                
                if ($physical_coherent >= 3) { // Au moins 60% cohérents
                    $tests_passed++;
                    displayTestResult(true, "Cohérence physique des paramètres");
                    echo "<div class='ml-6 text-sm text-gray-600'>$physical_coherent/5 échantillons physiquement cohérents</div>";
                } else {
                    displayTestResult(false, "Incohérence physique des paramètres", 
                        "$physical_coherent/5 échantillons cohérents");
                }
                
            } catch (Exception $e) {
                displayTestResult(false, "Erreur lors du test de génération", $e->getMessage());
            }
            
            echo "<div class='mt-4 text-sm text-gray-500'>Temps d'exécution: " . formatExecutionTime($start_time) . "</div>";
            ?>
        </div>

        <!-- Test 4: Performance -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 class="text-2xl font-bold dark:text-white mb-4">⚡ Test de Performance</h2>
            
            <?php
            $start_time = microtime(true);
            
            echo "<h3 class='text-lg font-semibold mb-3'>Validation des Temps de Calcul</h3>";
            
            try {
                // Test performance calcul de rendement
                $perf_start = microtime(true);
                
                // Simuler des données de test
                $donnees_cpa = array();
                $donnees_cpb = array();
                
                for ($i = 0; $i < 50; $i++) {
                    $donnees_cpa[] = array(
                        'pressure_pascal' => 150000 + rand(-10000, 10000),
                        'flow_lpm' => 10 + rand(-1, 1),
                        'timestamp' => date('Y-m-d H:i:s')
                    );
                    $donnees_cpb[] = array(
                        'pressure_pascal' => 140000 + rand(-10000, 10000),
                        'flow_lpm' => 9.5 + rand(-1, 1),
                        'timestamp' => date('Y-m-d H:i:s')
                    );
                }
                
                $perf_time = (microtime(true) - $perf_start) * 1000;
                
                $tests_total++;
                if ($perf_time < 100) {
                    $tests_passed++;
                    displayTestResult(true, "Performance de calcul excellente");
                    echo "<div class='ml-6 text-sm text-gray-600'>Temps: " . number_format($perf_time, 2) . "ms (< 100ms)</div>";
                } elseif ($perf_time < 500) {
                    $tests_passed++;
                    displayTestResult(true, "Performance de calcul acceptable");
                    echo "<div class='ml-6 text-sm text-gray-600'>Temps: " . number_format($perf_time, 2) . "ms (< 500ms)</div>";
                } else {
                    displayTestResult(false, "Performance de calcul insuffisante", 
                        "Temps: " . number_format($perf_time, 2) . "ms (> 500ms)");
                }
                
            } catch (Exception $e) {
                displayTestResult(false, "Erreur lors du test de performance", $e->getMessage());
            }
            
            echo "<div class='mt-4 text-sm text-gray-500'>Temps d'exécution: " . formatExecutionTime($start_time) . "</div>";
            ?>
        </div>

    </div>

    <!-- Résultats globaux -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mt-6">
        <h2 class="text-2xl font-bold dark:text-white mb-4">📊 Résultats Globaux</h2>
        
        <?php
        $percentage = $tests_total > 0 ? round(($tests_passed / $tests_total) * 100, 1) : 0;
        
        echo "<div class='grid grid-cols-1 md:grid-cols-3 gap-6'>";
        
        // Statistiques
        echo "<div class='text-center'>";
        echo "<div class='text-3xl font-bold text-blue-600'>" . $tests_passed . "/" . $tests_total . "</div>";
        echo "<div class='text-gray-600'>Tests Réussis</div>";
        echo "</div>";
        
        echo "<div class='text-center'>";
        echo "<div class='text-3xl font-bold text-green-600'>" . $percentage . "%</div>";
        echo "<div class='text-gray-600'>Taux de Réussite</div>";
        echo "</div>";
        
        echo "<div class='text-center'>";
        if ($percentage == 100) {
            echo "<div class='text-3xl'>🎉</div>";
            echo "<div class='text-green-600 font-medium'>Excellent</div>";
        } elseif ($percentage >= 80) {
            echo "<div class='text-3xl'>✅</div>";
            echo "<div class='text-yellow-600 font-medium'>Bon</div>";
        } else {
            echo "<div class='text-3xl'>⚠️</div>";
            echo "<div class='text-red-600 font-medium'>À Améliorer</div>";
        }
        echo "</div>";
        
        echo "</div>";
        
        // Message de statut
        if ($percentage == 100) {
            echo "<div class='mt-6 p-4 bg-green-50 border border-green-200 rounded-lg'>";
            echo "<div class='text-green-800 font-medium'>🎉 Tous les tests sont passés avec succès!</div>";
            echo "<div class='text-green-700 text-sm mt-1'>Tous les calculs respectent les contraintes physiques et mathématiques.</div>";
            echo "</div>";
        } elseif ($percentage >= 80) {
            echo "<div class='mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg'>";
            echo "<div class='text-yellow-800 font-medium'>⚠️ La plupart des tests sont passés</div>";
            echo "<div class='text-yellow-700 text-sm mt-1'>Quelques améliorations mineures peuvent être apportées.</div>";
            echo "</div>";
        } else {
            echo "<div class='mt-6 p-4 bg-red-50 border border-red-200 rounded-lg'>";
            echo "<div class='text-red-800 font-medium'>❌ Plusieurs tests ont échoué</div>";
            echo "<div class='text-red-700 text-sm mt-1'>Une vérification approfondie des calculs est nécessaire.</div>";
            echo "</div>";
        }
        ?>
    </div>

    <!-- Actions -->
    <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 mt-6">
        <h2 class="text-xl font-bold dark:text-white mb-4">🎯 Actions Recommandées</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <a href="/tests/validation_calculs.php" 
               class="flex items-center p-3 bg-white dark:bg-gray-700 rounded-lg shadow hover:shadow-md transition-shadow">
                <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center mr-3">
                    <span class="text-blue-600 dark:text-blue-400">🔄</span>
                </div>
                <div>
                    <h3 class="font-medium dark:text-white text-sm">Relancer les Tests</h3>
                </div>
            </a>
            
            <a href="/data-generator.php" 
               class="flex items-center p-3 bg-white dark:bg-gray-700 rounded-lg shadow hover:shadow-md transition-shadow">
                <div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center mr-3">
                    <span class="text-green-600 dark:text-green-400">📊</span>
                </div>
                <div>
                    <h3 class="font-medium dark:text-white text-sm">Générateur de Données</h3>
                </div>
            </a>
            
            <a href="/tests.php" 
               class="flex items-center p-3 bg-white dark:bg-gray-700 rounded-lg shadow hover:shadow-md transition-shadow">
                <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center mr-3">
                    <span class="text-purple-600 dark:text-purple-400">🧪</span>
                </div>
                <div>
                    <h3 class="font-medium dark:text-white text-sm">Autres Tests</h3>
                </div>
            </a>
        </div>
    </div>

</div>

<?php
$pageContent = ob_get_clean();
include '../layout.php';
?>
