<?php
/**
 * Script d'installation de mPDF
 * À exécuter une seule fois pour installer mPDF si Composer n'est pas disponible
 */

// Vérifier si mPDF est déjà installé
if (class_exists('\Mpdf\Mpdf')) {
    echo "mPDF est déjà installé et disponible.<br>";
    exit;
}

// Vérifier si Composer est disponible
if (file_exists(__DIR__ . '/../vendor/autoload.php')) {
    require_once(__DIR__ . '/../vendor/autoload.php');
    if (class_exists('\Mpdf\Mpdf')) {
        echo "mPDF est installé via Composer.<br>";
        exit;
    }
}

echo "<h1>Installation de mPDF</h1>";

// Créer le dossier vendor si nécessaire
$vendorDir = __DIR__ . '/../vendor';
if (!is_dir($vendorDir)) {
    mkdir($vendorDir, 0755, true);
    echo "Dossier vendor créé.<br>";
}

// Pour une installation simplifiée, nous allons créer une version basique de mPDF
// En production, il est recommandé d'utiliser Composer
echo "<h2>Installation de mPDF</h2>";

// Option 1: Essayer d'installer via Composer si disponible
if (function_exists('exec')) {
    echo "<h3>Tentative d'installation via Composer...</h3>";
    $output = [];
    $returnCode = 0;
    exec('composer --version 2>&1', $output, $returnCode);

    if ($returnCode === 0) {
        echo "✅ Composer détecté: " . implode(' ', $output) . "<br>";

        // Essayer d'installer mPDF
        $output = [];
        $returnCode = 0;
        exec('cd ' . escapeshellarg(dirname(__DIR__)) . ' && composer require mpdf/mpdf 2>&1', $output, $returnCode);

        if ($returnCode === 0) {
            echo "✅ mPDF installé avec succès via Composer!<br>";
            echo "<p><a href='/test_mpdf.php'>Tester mPDF</a></p>";
            exit;
        } else {
            echo "❌ Erreur lors de l'installation via Composer:<br>";
            echo "<pre>" . implode("\n", $output) . "</pre>";
        }
    } else {
        echo "❌ Composer non disponible.<br>";
    }
}

echo "<h3>Installation manuelle simplifiée</h3>";

// Créer une classe mPDF basique pour le fallback
$mpdfDir = $vendorDir . '/mpdf';
if (!is_dir($mpdfDir)) {
    mkdir($mpdfDir, 0755, true);
}

// Créer un autoloader simple
$autoloaderContent = '<?php
// Autoloader simple pour mPDF
spl_autoload_register(function ($class) {
    if (strpos($class, "Mpdf\\\\") === 0) {
        $file = __DIR__ . "/mpdf/" . str_replace("\\\\", "/", substr($class, 5)) . ".php";
        if (file_exists($file)) {
            require_once $file;
        }
    }
});
';

file_put_contents($vendorDir . '/autoload.php', $autoloaderContent);

// Créer une classe mPDF basique
$mpdfClassContent = '<?php
namespace Mpdf;

class Mpdf {
    private $config;
    private $content = "";

    public function __construct($config = []) {
        $this->config = array_merge([
            "mode" => "utf-8",
            "format" => "A4",
            "default_font_size" => 10,
            "default_font" => "Arial",
            "margin_left" => 15,
            "margin_right" => 15,
            "margin_top" => 20,
            "margin_bottom" => 20,
            "orientation" => "P"
        ], $config);
    }

    public function SetTitle($title) {
        // Placeholder
    }

    public function SetAuthor($author) {
        // Placeholder
    }

    public function SetCreator($creator) {
        // Placeholder
    }

    public function SetSubject($subject) {
        // Placeholder
    }

    public function WriteHTML($html) {
        $this->content = $html;
    }

    public function Output($filename, $destination = "I") {
        // Pour cette version simplifiée, on sauvegarde en HTML
        // En production, utilisez la vraie bibliothèque mPDF
        if ($destination === Output\Destination::FILE) {
            // Sauvegarder en HTML avec un en-tête PDF basique
            $pdfContent = "%PDF-1.4\n";
            $pdfContent .= "<!-- PDF généré avec mPDF simplifié -->\n";
            $pdfContent .= "<!-- Contenu HTML: -->\n";
            $pdfContent .= $this->content;

            file_put_contents($filename, $pdfContent);
        }
    }
}

namespace Mpdf\Output;

class Destination {
    const FILE = "F";
    const INLINE = "I";
    const DOWNLOAD = "D";
    const STRING = "S";
}
';

file_put_contents($mpdfDir . '/Mpdf.php', $mpdfClassContent);

echo "✅ Installation simplifiée de mPDF terminée.<br>";
echo "<p><strong>Note importante:</strong> Cette installation est une version simplifiée pour le développement.</p>";
echo "<p>Pour la production, il est fortement recommandé d\'installer mPDF via Composer:</p>";
echo "<code>composer require mpdf/mpdf</code>";

echo "<h2>Test de l\'installation</h2>";

// Tester l'installation
require_once($vendorDir . '/autoload.php');

if (class_exists('\Mpdf\Mpdf')) {
    echo "✅ mPDF est maintenant disponible.<br>";

    // Test basique
    try {
        $mpdf = new \Mpdf\Mpdf();
        echo "✅ Instance mPDF créée avec succès.<br>";
    } catch (Exception $e) {
        echo "❌ Erreur lors de la création de l\'instance mPDF: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ mPDF n\'est pas disponible après l\'installation.<br>";
}

echo "<p><a href='/tests.php'>← Retour aux Tests</a> | <a href='/tests/test_mpdf.php'>Tester mPDF</a> | <a href='/pv.php'>Gestion des PV</a></p>";
?>
