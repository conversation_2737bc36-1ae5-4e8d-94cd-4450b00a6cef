#!/bin/bash

# Script de démarrage pour l'environnement de développement FluidMotion
# Usage: ./dev-start.sh [command]

set -e

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction de logging
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Vérifier que Docker est installé et en cours d'exécution
check_docker() {
    if ! command -v docker &> /dev/null; then
        error "Docker n'est pas installé. Veuillez installer Docker Desktop."
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        error "Docker n'est pas en cours d'exécution. Veuillez démarrer Docker Desktop."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose n'est pas installé."
        exit 1
    fi
}

# Vérifier les fichiers de configuration
check_config() {
    if [ ! -f ".env.dev" ]; then
        warn "Fichier .env.dev non trouvé. Création d'un fichier par défaut..."
        cp .env.example .env.dev 2>/dev/null || true
    fi
    
    if [ ! -f "docker-compose.dev.yml" ]; then
        error "Fichier docker-compose.dev.yml non trouvé."
        exit 1
    fi
    
    if [ ! -f "Site WEB/Dockerfile.dev" ]; then
        error "Fichier Site WEB/Dockerfile.dev non trouvé."
        exit 1
    fi
}

# Configurer les permissions
setup_permissions() {
    log "Configuration des permissions..."
    
    # Créer les répertoires nécessaires s'ils n'existent pas
    mkdir -p "Site WEB/backups"
    mkdir -p "Site WEB/pdf_exports"
    mkdir -p "Site WEB/temp/mpdf"
    mkdir -p "Site WEB/vendor"
    
    # Définir les permissions appropriées
    if [[ "$OSTYPE" == "linux-gnu"* ]] || [[ "$OSTYPE" == "darwin"* ]]; then
        # Linux ou macOS
        chmod -R 755 "Site WEB"
        chmod -R 777 "Site WEB/backups" "Site WEB/pdf_exports" "Site WEB/temp" 2>/dev/null || true
    fi
}

# Démarrer l'environnement de développement
start_dev() {
    log "Démarrage de l'environnement de développement FluidMotion..."
    
    check_docker
    check_config
    setup_permissions
    
    # Copier le fichier d'environnement de développement
    cp .env.dev .env
    
    # Construire et démarrer les conteneurs
    log "Construction des images Docker..."
    docker-compose -f docker-compose.dev.yml build
    
    log "Démarrage des conteneurs..."
    docker-compose -f docker-compose.dev.yml up -d
    
    # Attendre que les services soient prêts
    log "Attente du démarrage des services..."
    sleep 10
    
    # Vérifier l'état des conteneurs
    docker-compose -f docker-compose.dev.yml ps
    
    # Afficher les informations de connexion
    echo ""
    info "=== ENVIRONNEMENT DE DÉVELOPPEMENT DÉMARRÉ ==="
    info "Application Web: http://localhost:8080"
    info "PhpMyAdmin: http://localhost:8081"
    info "Documentation: http://localhost:3000"
    info ""
    info "Logs en temps réel: docker-compose -f docker-compose.dev.yml logs -f"
    info "Arrêter: ./dev-start.sh stop"
    info "Redémarrer: ./dev-start.sh restart"
    info "================================================"
}

# Arrêter l'environnement de développement
stop_dev() {
    log "Arrêt de l'environnement de développement..."
    docker-compose -f docker-compose.dev.yml down
    log "Environnement de développement arrêté."
}

# Redémarrer l'environnement de développement
restart_dev() {
    log "Redémarrage de l'environnement de développement..."
    stop_dev
    start_dev
}

# Afficher les logs
show_logs() {
    docker-compose -f docker-compose.dev.yml logs -f
}

# Nettoyer l'environnement de développement
clean_dev() {
    warn "Nettoyage de l'environnement de développement..."
    docker-compose -f docker-compose.dev.yml down -v --remove-orphans
    docker system prune -f
    log "Nettoyage terminé."
}

# Entrer dans le conteneur web
shell_web() {
    log "Connexion au conteneur web..."
    docker-compose -f docker-compose.dev.yml exec web bash
}

# Afficher l'aide
show_help() {
    echo "Script de gestion de l'environnement de développement FluidMotion"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start     Démarrer l'environnement de développement (défaut)"
    echo "  stop      Arrêter l'environnement de développement"
    echo "  restart   Redémarrer l'environnement de développement"
    echo "  logs      Afficher les logs en temps réel"
    echo "  clean     Nettoyer complètement l'environnement"
    echo "  shell     Ouvrir un shell dans le conteneur web"
    echo "  help      Afficher cette aide"
    echo ""
    echo "Exemples:"
    echo "  $0                # Démarrer l'environnement"
    echo "  $0 start          # Démarrer l'environnement"
    echo "  $0 logs           # Voir les logs"
    echo "  $0 shell          # Ouvrir un shell dans le conteneur"
}

# Fonction principale
main() {
    case "${1:-start}" in
        start)
            start_dev
            ;;
        stop)
            stop_dev
            ;;
        restart)
            restart_dev
            ;;
        logs)
            show_logs
            ;;
        clean)
            clean_dev
            ;;
        shell)
            shell_web
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            error "Commande inconnue: $1"
            show_help
            exit 1
            ;;
    esac
}

# Exécuter la fonction principale avec tous les arguments
main "$@"
