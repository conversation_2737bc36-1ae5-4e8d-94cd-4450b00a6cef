<?php

/**
 * Classe Database - Singleton pour la gestion de la connexion à la base de données
 * Utilise les variables d'environnement pour la configuration
 */
class Database
{
    private static ?Database $instance = null;
    private PDO $connection;
    private string $db_host;
    private string $db_name;
    private string $db_user;
    private string $db_pass;
    private string $db_charset = 'utf8mb4';
    private int $db_port;

    private function __construct()
    {
        // Configuration depuis les variables d'environnement avec valeurs par défaut
        $this->db_host = $_ENV['DB_HOST'] ?? getenv('DB_HOST') ?: 'localhost';
        $this->db_name = $_ENV['DB_NAME'] ?? getenv('DB_NAME') ?: 'verins_db';
        $this->db_user = $_ENV['DB_USER'] ?? getenv('DB_USER') ?: 'root';
        $this->db_pass = $_ENV['DB_PASS'] ?? getenv('DB_PASS') ?: '';
        $this->db_port = (int)($_ENV['DB_PORT'] ?? getenv('DB_PORT') ?: 3306);

        // Construction du DSN
        $dsn = "mysql:host={$this->db_host};port={$this->db_port};dbname={$this->db_name};charset={$this->db_charset}";

        // Options de connexion PDO sécurisées
        $options = [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
            PDO::ATTR_PERSISTENT => false,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$this->db_charset} COLLATE {$this->db_charset}_unicode_ci",
            PDO::ATTR_TIMEOUT => 30,
        ];

        try {
            $this->connection = new PDO($dsn, $this->db_user, $this->db_pass, $options);

            // Configuration additionnelle pour la sécurité
            $this->connection->exec("SET sql_mode = 'STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION'");
            $this->connection->exec("SET time_zone = '+00:00'");

        } catch (PDOException $e) {
            error_log("Erreur de connexion à la base de données: " . $e->getMessage());
            throw new PDOException("Impossible de se connecter à la base de données", (int)$e->getCode());
        }
    }

    /**
     * Méthode statique pour obtenir l'instance de la classe
     * @return Database|null
     */
    public static function getInstance(): ?Database
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Méthode statique pour obtenir la connexion PDO
     * @return PDO
     */
    public static function getConnection(): PDO
    {
        if (self::$instance === null) {
            self::getInstance();
        }
        return self::$instance->connection;
    }

    /**
     * Teste la connexion à la base de données
     * @return bool
     */
    public static function testConnection(): bool
    {
        try {
            $connection = self::getConnection();
            $stmt = $connection->query('SELECT 1');
            return $stmt !== false;
        } catch (Exception $e) {
            error_log("Test de connexion échoué: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Obtient les informations de configuration de la base de données
     * @return array
     */
    public function getConnectionInfo(): array
    {
        return [
            'host' => $this->db_host,
            'port' => $this->db_port,
            'database' => $this->db_name,
            'user' => $this->db_user,
            'charset' => $this->db_charset
        ];
    }

    /**
     * Ferme la connexion à la base de données
     */
    public static function closeConnection(): void
    {
        if (self::$instance !== null) {
            self::$instance->connection = null;
            self::$instance = null;
        }
    }

    /**
     * Méthode de réveil pour éviter la désérialisation d'un singleton
     * @throws Exception
     */
    public function __wakeup()
    {
        throw new \Exception("Cannot unserialize singleton");
    }

    /**
     * Empêche le clonage de l'instance
     */
    private function __clone()
    {
    }
}
