import clsx from 'clsx';
import Heading from '@theme/Heading';
import styles from './styles.module.css';

const FeatureList = [
  {
    title: 'Interface Intuitive',
    Svg: require('@site/static/img/undraw_docusaurus_mountain.svg').default,
    description: (
      <>
        FluidMotion Labs offre une interface moderne et intuitive, conçue pour 
        optimiser votre productivité. Navigation simple, raccourcis clavier et 
        design responsive pour tous vos appareils.
      </>
    ),
  },
  {
    title: 'Workflow Complet',
    Svg: require('@site/static/img/undraw_docusaurus_tree.svg').default,
    description: (
      <>
        De la création d'une affaire à la livraison du rapport final, 
        FluidMotion Labs couvre l'intégralité de votre processus de test. 
        Gestion des essais, modèles réutilisables et génération PDF automatique.
      </>
    ),
  },
  {
    title: 'Sécurité et Fiabilité',
    Svg: require('@site/static/img/undraw_docusaurus_react.svg').default,
    description: (
      <>
        Système de sauvegarde intégré, contrôle d'accès par rôles et traçabilité 
        complète. Vos données sont protégées et votre activité est documentée 
        pour une conformité optimale.
      </>
    ),
  },
];

function Feature({Svg, title, description}) {
  return (
    <div className={clsx('col col--4')}>
      <div className="text--center">
        <Svg className={styles.featureSvg} role="img" />
      </div>
      <div className="text--center padding-horiz--md">
        <Heading as="h3">{title}</Heading>
        <p>{description}</p>
      </div>
    </div>
  );
}

export default function HomepageFeatures() {
  return (
    <section className={styles.features}>
      <div className="container">
        <div className="row">
          {FeatureList.map((props, idx) => (
            <Feature key={idx} {...props} />
          ))}
        </div>
      </div>
    </section>
  );
}
