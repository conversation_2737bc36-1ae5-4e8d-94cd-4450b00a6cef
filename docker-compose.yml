services:
  web:
    image: ${WEB_IMAGE:-ghcr.io/laprovidenceamiens/ciel2_bancdetest:latest}
    build:
      context: ./Site WEB
      dockerfile: Dockerfile
      target: ${BUILD_TARGET:-production}
    container_name: fluidmotion_web
    restart: unless-stopped
    ports:
      - "${WEB_PORT:-80}:80"
    volumes:
      - backups:/var/www/html/backups
      - pdf_exports:/var/www/html/pdf_exports
      - temp:/var/www/html/temp
    environment:
      DB_HOST: db
      DB_PORT: 3306
      DB_NAME: ${DB_NAME:-verins_db}
      DB_USER: ${DB_USER:-verins_user}
      DB_PASS: ${DB_PASSWORD:-verins_password}
      PHP_MEMORY_LIMIT: ${PHP_MEMORY_LIMIT:-256M}
      PHP_UPLOAD_MAX_FILESIZE: ${PHP_UPLOAD_MAX_FILESIZE:-50M}
      PHP_POST_MAX_SIZE: ${PHP_POST_MAX_SIZE:-50M}
    depends_on:
      db:
        condition: service_healthy
    networks:
      - fluidmotion_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health.php"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
  docs:
    image: ${WEB_IMAGE:-ghcr.io/laprovidenceamiens/ciel2_bancdetest-docs:latest}
    build:
      context: ./docs
      dockerfile: Dockerfile
      target: ${BUILD_TARGET:-release}
    container_name: fluidmotion_docs
    restart: unless-stopped
    ports:
      - "${DOCS_PORT:-3000}:3000"
    networks:
      - fluidmotion_network
  db:
    image: mariadb:lts
    container_name: fluidmotion_db
    restart: unless-stopped
    ports:
      - "${DB_PORT:-3306}:3306"
    volumes:
      - db:/var/lib/mysql
      - ./Site WEB/config/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql:ro
      - db_logs:/var/log/mysql
    environment:
      MARIADB_ROOT_PASSWORD: ${DB_ROOT_PASSWORD:-root_password_change_me}
      MARIADB_DATABASE: ${DB_NAME:-verins_db}
      MARIADB_USER: ${DB_USER:-verins_user}
      MARIADB_PASSWORD: ${DB_PASSWORD:-verins_password}
      MARIADB_ALLOW_EMPTY_ROOT_PASSWORD: "no"
    networks:
      - fluidmotion_network
    healthcheck:
      test: ["CMD", "healthcheck.sh", "--connect", "--innodb_initialized"]
      start_period: 10s
      interval: 10s
      timeout: 5s
      retries: 3
    command: >
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --innodb-buffer-pool-size=256M
      --max-connections=100
      --query-cache-size=0
      --query-cache-type=0
  phpmyadmin:
    image: phpmyadmin/phpmyadmin:5
    container_name: fluidmotion_phpmyadmin
    restart: unless-stopped
    environment:
      PMA_HOST: db
      PMA_PORT: 3306
      UPLOAD_LIMIT: 100M
      MEMORY_LIMIT: 512M
      PMA_ABSOLUTE_URI: ${PMA_ABSOLUTE_URI:-}
    ports:
      - "${PHPMYADMIN_PORT:-8080}:80"
    depends_on:
      db:
        condition: service_healthy
    networks:
      - fluidmotion_network

volumes:
  db:
    driver: local
  db_logs:
    driver: local
  backups:
    driver: local
  pdf_exports:
    driver: local
  temp:
    driver: local

networks:
  fluidmotion_network:
    driver: bridge
