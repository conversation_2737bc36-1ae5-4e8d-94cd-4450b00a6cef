<?php
require_once(__DIR__ . '/../../lib/affaires.php');
require_once(__DIR__ . '/TestBase.php');

class AffaireTest extends TestBase
{
    private $test_affaire_id;

    public function testCreateAffaire()
    {
        $numero = 'TEST-' . time();
        $client = 'Client Test';
        $description = 'Description test';
        $tags = ['test', 'unitaire'];
        $user_id = 1;

        $result = Affaire::create($numero, $client, $description, $tags, $user_id);
        $this->assertTrue($result, 'La création d\'affaire devrait réussir');

        // Vérifier que l'affaire existe
        $affaires = Affaire::getAll();
        $found = false;
        foreach ($affaires as $affaire) {
            if ($affaire['numero'] === $numero) {
                $found = true;
                $this->assertEquals($client, $affaire['client']);
                $this->assertEquals($description, $affaire['description']);
                // Nettoyer
                Affaire::delete($affaire['id']);
                break;
            }
        }
        $this->assertTrue($found, 'L\'affaire créée devrait être trouvée');
    }

    public function testGetAffaireById()
    {
        $affaire = Affaire::getById($this->test_affaire_id);
        $this->assertNotNull($affaire, 'L\'affaire devrait être trouvée');
        $this->assertEquals($this->test_affaire_id, $affaire['id']);
        $this->assertEquals('TEST-AFFAIRE', $affaire['numero']);
    }

    public function testGetAffaireByIdInvalid()
    {
        $affaire = Affaire::getById(99999);
        $this->assertNull($affaire, 'Une affaire inexistante devrait retourner null');
    }

    public function testUpdateAffaire()
    {
        $nouveau_client = 'Client Modifié';
        $nouvelle_description = 'Description modifiée';
        $nouveau_statut = 'Terminé';
        $tags = ['modifié'];

        $result = Affaire::update(
            $this->test_affaire_id,
            'TEST-AFFAIRE',
            $nouveau_client,
            $nouvelle_description,
            $nouveau_statut,
            $tags,
            1
        );

        $this->assertTrue($result, 'La mise à jour devrait réussir');

        // Vérifier les modifications
        $affaire = Affaire::getById($this->test_affaire_id);
        $this->assertEquals($nouveau_client, $affaire['client']);
        $this->assertEquals($nouvelle_description, $affaire['description']);
        $this->assertEquals($nouveau_statut, $affaire['statut']);
    }

    public function testGetAllAffaires()
    {
        $affaires = Affaire::getAll();
        $this->assertIsArray($affaires, 'getAll devrait retourner un tableau');
        $this->assertGreaterThan(0, count($affaires), 'Il devrait y avoir au moins une affaire');
    }

    public function testGetAffairesByStatut()
    {
        $affaires_en_cours = Affaire::getByStatut('En cours');
        $this->assertIsArray($affaires_en_cours, 'getByStatut devrait retourner un tableau');

        // Vérifier que toutes les affaires retournées ont le bon statut
        foreach ($affaires_en_cours as $affaire) {
            $this->assertEquals('En cours', $affaire['statut']);
        }
    }

    public function testCreateAffaireDuplicate()
    {
        // Tenter de créer une affaire avec le même numéro
        $result = Affaire::create('TEST-AFFAIRE', 'Client', 'Description', [], 1);
        $this->assertFalse($result, 'La création d\'une affaire avec un numéro existant devrait échouer');
    }

    public function testDeleteAffaire()
    {
        // Créer une affaire temporaire pour la suppression
        $numero = 'TEST-DELETE-' . time();
        Affaire::create($numero, 'Client', 'Description', [], 1);

        $affaires = Affaire::getAll();
        $affaire_id = null;
        foreach ($affaires as $affaire) {
            if ($affaire['numero'] === $numero) {
                $affaire_id = $affaire['id'];
                break;
            }
        }

        $this->assertNotNull($affaire_id, 'L\'affaire à supprimer devrait exister');

        $result = Affaire::delete($affaire_id);
        $this->assertTrue($result, 'La suppression devrait réussir');

        // Vérifier que l'affaire n'existe plus
        $affaire = Affaire::getById($affaire_id);
        $this->assertNull($affaire, 'L\'affaire supprimée ne devrait plus exister');
    }

    public function testGetAffairesRecentes()
    {
        $affaires_recentes = Affaire::getRecentes(5);
        $this->assertIsArray($affaires_recentes, 'getRecentes devrait retourner un tableau');
        $this->assertLessThanOrEqual(5, count($affaires_recentes), 'Ne devrait pas retourner plus de 5 affaires');
    }

    public function runAllTests()
    {
        $this->setUp();

        echo "<h3>Tests Affaire</h3>";

        $this->runTest('testCreateAffaire');
        $this->runTest('testGetAffaireById');
        $this->runTest('testGetAffaireByIdInvalid');
        $this->runTest('testUpdateAffaire');
        $this->runTest('testGetAllAffaires');
        $this->runTest('testGetAffairesByStatut');
        $this->runTest('testCreateAffaireDuplicate');
        $this->runTest('testDeleteAffaire');
        $this->runTest('testGetAffairesRecentes');

        $this->tearDown();

        return $this->getResults();
    }

    public function setUp()
    {
        parent::setUp();
        // Créer une affaire de test
        $this->test_affaire_id = $this->createTestAffaire();
    }

    private function createTestAffaire()
    {
        Affaire::create('TEST-AFFAIRE', 'Client Test', 'Description test', ['test'], 1);
        $affaires = Affaire::getAll();
        foreach ($affaires as $affaire) {
            if ($affaire['numero'] === 'TEST-AFFAIRE') {
                return $affaire['id'];
            }
        }
        return null;
    }

    public function tearDown()
    {
        // Nettoyer les données de test
        if ($this->test_affaire_id) {
            Affaire::delete($this->test_affaire_id);
        }
        parent::tearDown();
    }
}
