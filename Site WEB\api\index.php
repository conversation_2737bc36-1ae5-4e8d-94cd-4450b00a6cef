<?php
require_once __DIR__ . '/core/Middleware.php';

// Gestion CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Parser l'URL pour obtenir la route
$request_uri = $_SERVER['REQUEST_URI'];
$uri_parts = explode('/', trim(parse_url($request_uri, PHP_URL_PATH), '/'));
$route = $uri_parts[count($uri_parts) - 1] ?? '';

$controller = null;

switch ($route) {
    case 'auth':
        require_once __DIR__ . '/controllers/AuthController.php';
        $controller = new AuthController();
        $controller->handle();
        break;
    case 'users':
        require_once __DIR__ . '/controllers/UserController.php';
        $controller = new UserController();
        break;
    case 'affaires':
        require_once __DIR__ . '/controllers/AffaireController.php';
        $controller = new AffaireController();
        break;
    case 'essais':
        require_once __DIR__ . '/controllers/EssaiController.php';
        $controller = new EssaiController();
        break;
    case 'courbes':
        require_once __DIR__ . '/controllers/CourbeController.php';
        $controller = new CourbeController();
        break;
    case 'pv':
        require_once __DIR__ . '/controllers/PVController.php';
        $controller = new PVController();
        break;
    case 'backup':
        require_once __DIR__ . '/controllers/BackupController.php';
        $controller = new BackupController();
        break;
    case 'pdf':
        require_once __DIR__ . '/controllers/PDFController.php';
        $controller = new PDFController();
        break;
    case 'rendement':
        require_once __DIR__ . '/controllers/RendementController.php';
        $controller = new RendementController();
        break;
}

if ($controller) {
    $auth = new AuthMiddleware(function ($user) use ($controller) {
        $controller->setUser($user);
        return $controller->handle();
    });
    $auth->handle();
} else {
    http_response_code(404);
    echo json_encode(['error' => 'Route non trouvée']);
}
