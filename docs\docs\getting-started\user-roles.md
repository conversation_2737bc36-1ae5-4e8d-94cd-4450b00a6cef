# Rôles Utilisateur et Permissions

## Vue d'ensemble des Rôles

FluidMotion Labs utilise un système de rôles pour contrôler l'accès aux différentes fonctionnalités. Chaque utilisateur se voit attribuer un rôle qui détermine ses permissions et les modules auxquels il peut accéder.

## 👨‍💼 Rôle Contrôleur

<div className="role-controleur">

### Permissions Complètes

Le rôle **Contrôleur** dispose de tous les droits dans le système, incluant :

#### Fonctionnalités Standard
- ✅ Accès au tableau de bord complet
- ✅ Gestion complète des affaires (CRUD)
- ✅ Gestion complète des essais (CRUD)
- ✅ Gestion complète des PV (CRUD)
- ✅ Consultation des vues détaillées
- ✅ Export PDF des rapports

#### Fonctionnalités Avancées
- ✅ **Sauvegarde et restauration** de la base de données
- ✅ **Outils de diagnostic** système
- ✅ **Générateur de données** de test
- ✅ **Initialisation** de base de données vierge
- ✅ **Import/Export** de sauvegardes
- ✅ **Surveillance** de l'espace disque

</div>

### Interface Spécifique Contrôleur

Les contrôleurs voient des éléments d'interface supplémentaires :

#### Menu de Navigation
- Onglet **"Sauvegarde"** dans la barre de navigation principale
- Menu utilisateur étendu avec :
  - "Tests & Diagnostic"
  - "Générateur de Données"

#### Indicateurs Visuels
- Badge "Contrôleur • Outils avancés disponibles" dans le profil utilisateur
- Accès à 5 onglets de navigation au lieu de 4

## 👨‍🔧 Rôle Opérateur

<div className="role-operateur">

### Permissions Standard

Le rôle **Opérateur** dispose des permissions nécessaires pour les tâches quotidiennes :

#### Fonctionnalités Accessibles
- ✅ Accès au tableau de bord (statistiques de base)
- ✅ Gestion complète des affaires (CRUD)
- ✅ Gestion complète des essais (CRUD)
- ✅ Gestion complète des PV (CRUD)
- ✅ Consultation des vues détaillées
- ✅ Export PDF des rapports
- ✅ Utilisation des modèles d'essais

#### Fonctionnalités Restreintes
- ❌ Pas d'accès aux sauvegardes
- ❌ Pas d'outils de diagnostic
- ❌ Pas de générateur de données
- ❌ Pas d'accès aux fonctions administratives

</div>

### Interface Standard Opérateur

Les opérateurs voient une interface simplifiée :

#### Menu de Navigation
- 4 onglets principaux : Accueil, Affaires, Essais, PV
- Menu utilisateur standard avec uniquement "Déconnexion"

## Comparaison des Accès

| Fonctionnalité | Contrôleur | Opérateur |
|----------------|------------|-----------|
| **Tableau de bord** | ✅ Complet | ✅ Standard |
| **Affaires** | ✅ CRUD complet | ✅ CRUD complet |
| **Essais** | ✅ CRUD complet | ✅ CRUD complet |
| **PV** | ✅ CRUD complet | ✅ CRUD complet |
| **Modèles d'essais** | ✅ CRUD complet | ✅ CRUD complet |
| **Export PDF** | ✅ | ✅ |
| **Sauvegarde/Restauration** | ✅ | ❌ |
| **Diagnostic système** | ✅ | ❌ |
| **Générateur de données** | ✅ | ❌ |
| **Initialisation DB** | ✅ | ❌ |

## Sécurité et Contrôle d'Accès

### Vérification des Permissions

Le système vérifie automatiquement les permissions à plusieurs niveaux :

1. **Niveau Interface** : Les éléments non autorisés sont masqués
2. **Niveau Navigation** : Redirection automatique si accès non autorisé
3. **Niveau Serveur** : Validation côté serveur pour toutes les actions

### Protection des Données

#### Pour les Contrôleurs
- Accès complet aux données avec responsabilité de sauvegarde
- Possibilité de restaurer des données en cas de problème
- Outils de maintenance préventive

#### Pour les Opérateurs
- Accès limité aux données opérationnelles
- Protection contre les modifications système critiques
- Focus sur les tâches quotidiennes

## Gestion des Comptes

### Comptes par Défaut

Le système est livré avec des comptes de test :

```
Contrôleur :
- Nom d'utilisateur : controleur1
- Mot de passe : password123

Opérateur :
- Nom d'utilisateur : operateur1  
- Mot de passe : password123
```

:::warning Sécurité
Ces comptes par défaut doivent être modifiés ou supprimés en production. Contactez votre administrateur système pour la gestion des comptes utilisateur.
:::

### Identification du Rôle

#### Dans l'Interface
- Le rôle est affiché dans le menu utilisateur (coin supérieur droit)
- Les contrôleurs voient un badge spécial "Contrôleur • Outils avancés disponibles"

#### Fonctionnalités Visuelles
- Navigation adaptée selon le rôle
- Couleurs et indicateurs spécifiques
- Messages d'aide contextuels

## Bonnes Pratiques par Rôle

### Pour les Contrôleurs

<div className="role-controleur">

1. **Sauvegardes Régulières**
   - Effectuer des sauvegardes quotidiennes
   - Vérifier l'espace disque disponible
   - Tester les restaurations périodiquement

2. **Surveillance Système**
   - Utiliser les outils de diagnostic
   - Surveiller les performances
   - Maintenir la base de données

3. **Formation des Opérateurs**
   - Accompagner les nouveaux utilisateurs
   - Valider les procédures
   - Assurer la qualité des données

</div>

### Pour les Opérateurs

<div className="role-operateur">

1. **Saisie de Qualité**
   - Vérifier les données avant validation
   - Utiliser les modèles d'essais
   - Maintenir la cohérence

2. **Organisation**
   - Utiliser le système de tags
   - Suivre les workflows établis
   - Documenter les procédures

3. **Communication**
   - Signaler les problèmes techniques
   - Proposer des améliorations
   - Collaborer efficacement

</div>

---

:::tip Évolution des Rôles
Les permissions peuvent évoluer selon les besoins de votre organisation. Contactez votre administrateur pour toute modification de rôle.
:::

:::info Prochaine Étape
Maintenant que vous comprenez les rôles, découvrez comment [effectuer votre première connexion](./first-login).
:::
