<?php
require_once(__DIR__ . '/../config/database.php');

class Rappel
{
    public static function create($affaire_id, $titre, $description, $date_echeance, $cree_par)
    {
        $stmt = self::getDb()->prepare("
            INSERT INTO rappels 
            (affaire_id, titre, description, date_echeance, cree_par) 
            VALUES (?, ?, ?, ?, ?)
        ");
        return $stmt->execute([
            $affaire_id,
            $titre,
            $description,
            $date_echeance,
            $cree_par
        ]);
    }

    // Créer un nouveau rappel

    private static function getDb()
    {
        return Database::getInstance()->getConnection();
    }

    // Mettre à jour un rappel

    public static function update($id, $titre, $description, $date_echeance, $statut)
    {
        $stmt = self::getDb()->prepare("
            UPDATE rappels 
            SET titre = ?, description = ?, date_echeance = ?, statut = ? 
            WHERE id = ?
        ");
        return $stmt->execute([
            $titre,
            $description,
            $date_echeance,
            $statut,
            $id
        ]);
    }

    // Obtenir les rappels d'une affaire
    public static function getByAffaireId($affaire_id)
    {
        $stmt = self::getDb()->prepare("
            SELECT r.*, u.username AS cree_par_nom 
            FROM rappels r 
            LEFT JOIN users u ON r.cree_par = u.id 
            WHERE r.affaire_id = ? 
            ORDER BY r.date_echeance ASC
        ");
        $stmt->execute([$affaire_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Obtenir tous les rappels en attente
    public static function getRappelsEnAttente()
    {
        $stmt = self::getDb()->prepare("
            SELECT r.*, a.numero AS numero_affaire, u.username AS cree_par_nom 
            FROM rappels r 
            JOIN affaires a ON r.affaire_id = a.id 
            LEFT JOIN users u ON r.cree_par = u.id 
            WHERE r.statut = 'En attente' 
            AND r.date_echeance >= CURRENT_DATE 
            ORDER BY r.date_echeance ASC
        ");
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Supprimer un rappel
    public static function delete($id)
    {
        $stmt = self::getDb()->prepare("DELETE FROM rappels WHERE id = ?");
        return $stmt->execute([$id]);
    }

    // Obtenir les rappels urgents (échéance dans moins de 7 jours)
    public static function getRappelsUrgents()
    {
        $stmt = self::getDb()->prepare("
            SELECT r.*, a.numero AS numero_affaire, u.username AS cree_par_nom 
            FROM rappels r 
            JOIN affaires a ON r.affaire_id = a.id 
            LEFT JOIN users u ON r.cree_par = u.id 
            WHERE r.statut = 'En attente' 
            AND r.date_echeance BETWEEN CURRENT_DATE AND date_add(CURRENT_DATE, INTERVAL 7 DAY)
            ORDER BY r.date_echeance ASC
        ");
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}