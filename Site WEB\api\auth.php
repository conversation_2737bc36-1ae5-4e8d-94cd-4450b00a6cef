<?php
require_once 'config.php';

class Auth
{
    private static $secret_key = "votre_clé_secrète_très_longue";
    private static $token_duration = 3600; // 1 heure

    public static function generateToken($user)
    {
        $issued_at = time();
        $expiration = $issued_at + self::$token_duration;

        $payload = [
            'iat' => $issued_at,
            'exp' => $expiration,
            ...$user,
        ];

        return self::encodeToken($payload);
    }

    private static function encodeToken($payload)
    {
        $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
        $header = base64_encode($header);
        $payload = base64_encode(json_encode($payload));

        $signature = hash_hmac('sha256', "$header.$payload", self::$secret_key, true);
        $signature = base64_encode($signature);

        return "$header.$payload.$signature";
    }

    public static function validateToken()
    {
        $headers = getallheaders();
        if (!isset($headers['Authorization']) && !isset($headers['authorization'])) {
            sendJSON(['error' => 'Token non fourni'], 401);
        }

        $token = str_replace('Bearer ', '', $headers['Authorization'] ?? $headers['authorization']);
        try {
            return self::decodeToken($token);
        } catch (Exception $e) {
            sendJSON(['error' => 'Token invalide'], 401);
        }
    }

    private static function decodeToken($token)
    {
        $parts = explode('.', $token);
        if (count($parts) !== 3) {
            throw new Exception('Format de token invalide');
        }

        $payload = json_decode(base64_decode($parts[1]), true);
        if ($payload['exp'] < time()) {
            throw new Exception('Token expiré');
        }

        return $payload;
    }
}
