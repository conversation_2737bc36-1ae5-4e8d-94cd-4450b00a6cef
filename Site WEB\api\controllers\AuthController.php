<?php
require_once __DIR__ . '/../core/BaseController.php';
require_once __DIR__ . '/../../lib/user.php';
require_once __DIR__ . '/../auth.php';

class AuthController extends BaseController
{
    public function handle()
    {
        switch ($this->method) {
            case 'POST':
                return $this->login();
            default:
                $this->error('Méthode non autorisée', 405);
        }
    }

    private function login()
    {
        if (!isset($this->data['username']) || !isset($this->data['password'])) {
            return $this->error('<PERSON><PERSON><PERSON> manquantes');
        }

        $user = User::authenticate($this->data['username'], $this->data['password']);
        if (!$user) {
            return $this->error('Identifiants invalides', 401);
        }

        $token = Auth::generateToken($user);
        return $this->json([
            'token' => $token,
            'user' => [
                'id' => $user['id'],
                'username' => $user['username'],
                'role' => $user['role']
            ]
        ]);
    }
}
