# Configuration d'environnement pour FluidMotion Web Application
# Copiez ce fichier vers .env et modifiez les valeurs selon votre environnement

# =============================================================================
# CONFIGURATION DE L'APPLICATION
# =============================================================================

# Environnement de l'application (development, testing, production)
APP_ENV=production

# Mode debug (true/false) - TOUJOURS false en production
APP_DEBUG=false

# URL de base de l'application
APP_URL=http://localhost:8080

# Clé secrète de l'application (générer avec: openssl rand -base64 32)
APP_SECRET=

# =============================================================================
# CONFIGURATION DE LA BASE DE DONNÉES
# =============================================================================

# Nom de la base de données
DB_NAME=verins_db

# Utilisateur de la base de données
DB_USER=verins_user

# Mot de passe de l'utilisateur (générer avec: openssl rand -base64 32)
DB_PASS=

# Mot de passe root de MariaDB (générer avec: openssl rand -base64 32)
DB_ROOT_PASSWORD=

# Hôte de la base de données (db pour Docker, localhost pour local)
DB_HOST=db

# Port de la base de données
DB_PORT=3306

# Chemin pour les données de la base de données (volumes Docker)
DB_DATA_PATH=./data/mysql

# =============================================================================
# CONFIGURATION DES PORTS
# =============================================================================

# Port pour l'application web
WEB_PORT=8080

# Port pour PhpMyAdmin (développement uniquement)
PHPMYADMIN_PORT=8081

# Port pour Redis (si utilisé)
REDIS_PORT=6379

# =============================================================================
# CONFIGURATION REDIS (OPTIONNEL)
# =============================================================================

# Mot de passe Redis (générer avec: openssl rand -base64 32)
REDIS_PASSWORD=

# =============================================================================
# CONFIGURATION PHP
# =============================================================================

# Limite mémoire PHP
PHP_MEMORY_LIMIT=256M

# Taille maximale des fichiers uploadés
PHP_UPLOAD_MAX_FILESIZE=50M

# Taille maximale des données POST
PHP_POST_MAX_SIZE=50M

# =============================================================================
# CONFIGURATION DES CHEMINS
# =============================================================================

# Chemin pour les sauvegardes
BACKUP_PATH=./backups

# Chemin pour les exports PDF
PDF_EXPORTS_PATH=./pdf_exports

# =============================================================================
# CONFIGURATION DOCKER
# =============================================================================

# Target de build Docker (development, production)
BUILD_TARGET=production

# =============================================================================
# CONFIGURATION DE SÉCURITÉ
# =============================================================================

# Clé JWT (générer avec: openssl rand -hex 32)
JWT_SECRET=

# Durée de vie des tokens JWT (en secondes)
JWT_EXPIRATION=3600

# Salt pour les mots de passe (générer avec: openssl rand -base64 32)
PASSWORD_SALT=

# =============================================================================
# CONFIGURATION EMAIL (SI UTILISÉ)
# =============================================================================

# Configuration SMTP
MAIL_HOST=
MAIL_PORT=587
MAIL_USERNAME=
MAIL_PASSWORD=
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="FluidMotion"

# =============================================================================
# CONFIGURATION DE LOGGING
# =============================================================================

# Niveau de log (debug, info, warning, error)
LOG_LEVEL=warning

# Rotation des logs (en jours)
LOG_ROTATION_DAYS=30

# =============================================================================
# INSTRUCTIONS DE GÉNÉRATION DES SECRETS
# =============================================================================

# Pour générer des mots de passe sécurisés :
# openssl rand -base64 32

# Pour générer des clés hexadécimales :
# openssl rand -hex 32

# Pour générer des clés JWT (256-bit) :
# openssl rand -base64 32

# =============================================================================
# VARIABLES MYSQL AVANCÉES
# =============================================================================

# Génération automatique du mot de passe root (true/false)
MYSQL_RANDOM_ROOT_PASSWORD=false

# Mot de passe à usage unique (true/false)
MYSQL_ONETIME_PASSWORD=false
