# Docker Development Setup - FluidMotion

## 📋 Résumé des Modifications

Ce document résume les modifications apportées pour configurer l'environnement de développement Docker avec montage dynamique du répertoire "Site WEB".

## 🆕 Fichiers Créés

### 1. **Site WEB/Dockerfile.dev**
- Dockerfile spécialement optimisé pour le développement
- Xdebug activé pour le débogage
- Configuration PHP adaptée au développement
- Permissions flexibles pour le montage de volumes
- Hot-reload activé (OPcache désactivé)

### 2. **docker-compose.dev.yml** (modifié)
- Montage dynamique du répertoire "Site WEB" via bind mount
- Configuration des variables d'environnement de développement
- Ports adaptés pour éviter les conflits (8080, 8081, 3000)
- Volume pour les logs de développement
- Configuration utilisateur pour les permissions

### 3. **.env.dev**
- Variables d'environnement spécifiques au développement
- Mots de passe d'exemple (NON sécurisés pour la production)
- Configuration Xdebug
- Paramètres PHP augmentés pour le développement

### 4. **dev-start.sh** et **dev-start.bat**
- Scripts de démarrage pour Linux/macOS et Windows
- Gestion automatique des permissions
- Vérifications de prérequis
- Commandes de gestion simplifiées

### 5. **README_DEV.md**
- Guide complet de développement
- Instructions de démarrage
- Configuration Xdebug
- Dépannage et bonnes pratiques

### 6. **.vscode/launch.json**
- Configuration VS Code pour Xdebug
- Path mappings corrects
- Paramètres de débogage optimisés

### 7. **.vscode/settings.json** (mis à jour)
- Configuration PHP pour VS Code
- Exclusions de fichiers appropriées
- Commandes Docker intégrées
- Formatage automatique

## 🔧 Fonctionnalités Implémentées

### ✅ Montage Dynamique
- Le répertoire `Site WEB` est monté en temps réel
- Aucune reconstruction nécessaire pour les modifications
- Hot-reload automatique des fichiers PHP

### ✅ Environnement de Développement Complet
- **Xdebug** : Débogage pas à pas avec VS Code
- **Logs détaillés** : Erreurs PHP affichées
- **PhpMyAdmin** : Interface de gestion de base de données
- **Permissions flexibles** : Écriture autorisée dans tous les répertoires

### ✅ Configuration Automatisée
- Scripts de démarrage multi-plateformes
- Vérification automatique des prérequis
- Configuration des permissions automatique
- Installation des dépendances Composer

### ✅ Isolation des Environnements
- Configuration séparée développement/production
- Variables d'environnement spécifiques
- Images Docker distinctes
- Ports non conflictuels

## 🚀 Utilisation

### Démarrage Rapide

#### Linux/macOS
```bash
./dev-start.sh
```

#### Windows
```cmd
dev-start.bat
```

### Services Disponibles
- **Application** : http://localhost:8080
- **PhpMyAdmin** : http://localhost:8081
- **Documentation** : http://localhost:3000

### Commandes Utiles
```bash
# Voir les logs
./dev-start.sh logs

# Ouvrir un shell
./dev-start.sh shell

# Redémarrer
./dev-start.sh restart

# Nettoyer
./dev-start.sh clean
```

## 🐛 Débogage avec Xdebug

### Configuration VS Code
1. Installer l'extension **PHP Debug**
2. Utiliser la configuration fournie dans `.vscode/launch.json`
3. Placer des points d'arrêt
4. Démarrer le débogage (F5)

### Paramètres Xdebug
- **Port** : 9003
- **Host** : host.docker.internal
- **Mode** : develop,debug,coverage
- **Path Mapping** : `/var/www/html` → `${workspaceFolder}/Site WEB`

## 📁 Structure des Volumes

### Bind Mounts (Développement)
```yaml
volumes:
  - type: bind
    source: ./Site WEB
    target: /var/www/html
    bind:
      propagation: cached
```

### Named Volumes (Données Persistantes)
- `backups:/var/www/html/backups`
- `pdf_exports:/var/www/html/pdf_exports`
- `temp:/var/www/html/temp`
- `dev_logs:/var/log/app`

## 🔒 Sécurité

### ⚠️ Avertissements de Sécurité
- **Mots de passe par défaut** dans `.env.dev`
- **Xdebug activé** (performance réduite)
- **Erreurs PHP affichées** (exposition d'informations)
- **CORS ouvert** pour le développement
- **Permissions flexibles** sur les fichiers

### 🛡️ Pour la Production
- Utiliser `docker-compose.yml` (pas `.dev.yml`)
- Générer des mots de passe sécurisés
- Désactiver Xdebug
- Configurer les permissions strictes
- Utiliser HTTPS et CSP headers

## 🔄 Workflow de Développement

### 1. Modification du Code
1. Éditer les fichiers dans `Site WEB/`
2. Les changements sont immédiatement visibles
3. Aucun redémarrage de conteneur nécessaire

### 2. Débogage
1. Placer des points d'arrêt dans VS Code
2. Démarrer le débogage (F5)
3. Naviguer vers la page à déboguer
4. Le débogueur s'arrête aux points d'arrêt

### 3. Base de Données
- Accéder via PhpMyAdmin : http://localhost:8081
- Credentials dans `.env.dev`
- Sauvegardes automatiques dans `Site WEB/backups/`

### 4. Tests
```bash
./dev-start.sh shell
cd /var/www/html
php tests/test_runner.php
```

## 🚨 Dépannage

### Problèmes Courants

#### Port déjà utilisé
```bash
# Modifier les ports dans .env.dev
WEB_PORT=8081
PHPMYADMIN_PORT=8082
```

#### Permissions de fichiers
```bash
# Nettoyer et redémarrer
./dev-start.sh clean
./dev-start.sh start
```

#### Xdebug ne fonctionne pas
1. Vérifier que VS Code écoute sur le port 9003
2. Vérifier les path mappings dans `launch.json`
3. Redémarrer le conteneur web

#### Base de données inaccessible
```bash
# Vérifier l'état
docker-compose -f docker-compose.dev.yml ps

# Redémarrer la DB
docker-compose -f docker-compose.dev.yml restart db
```

## 📊 Comparaison Développement vs Production

| Aspect | Développement | Production |
|--------|---------------|------------|
| **Montage** | Bind mount dynamique | Copy dans l'image |
| **Xdebug** | ✅ Activé | ❌ Désactivé |
| **Erreurs PHP** | ✅ Affichées | ❌ Cachées |
| **OPcache** | ❌ Désactivé | ✅ Activé |
| **Permissions** | 777 (flexibles) | 755 (strictes) |
| **Logs** | Debug complet | Erreurs uniquement |
| **Sécurité** | Développement | Production |

## 🎯 Avantages de cette Configuration

### ✅ Productivité
- **Hot-reload** : Modifications instantanées
- **Débogage intégré** : Xdebug avec VS Code
- **Pas de rebuild** : Développement rapide

### ✅ Isolation
- **Environnements séparés** : Dev/Prod distincts
- **Dépendances isolées** : Pas de conflit local
- **Base de données dédiée** : Données de test

### ✅ Facilité d'utilisation
- **Scripts automatisés** : Démarrage en une commande
- **Documentation complète** : Guide détaillé
- **Multi-plateforme** : Linux/macOS/Windows

Cette configuration permet un développement efficace tout en maintenant la compatibilité avec l'environnement de production existant.
