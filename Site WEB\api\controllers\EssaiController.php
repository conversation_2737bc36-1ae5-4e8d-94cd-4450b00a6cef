<?php
require_once __DIR__ . '/../core/BaseController.php';
require_once __DIR__ . '/../../lib/essais.php';

class EssaiController extends BaseController
{
    public function handle()
    {
        switch ($this->method) {
            case 'GET':
                return $this->index();
            case 'POST':
                return $this->create();
            case 'PUT':
                return $this->update();
            case 'DELETE':
                return $this->delete();
            default:
                $this->error('Méthode non autorisée', 405);
        }
    }

    private function index()
    {
        if (isset($this->data['id'])) {
            $essai = Essai::getById($this->data['id']);
            if (!$essai) return $this->error('Essai non trouvé', 404);
            return $this->json($essai);
        }
        if (isset($this->data['affaire_id'])) {
            return $this->json(Essai::getByAffaireId($this->data['affaire_id']));
        }
        return $this->json(Essai::getByStatut($this->data['statut'] ?? null));
    }

    private function create()
    {
        if (!isset($this->data['affaire_id']) || !isset($this->data['type'])) {
            return $this->error('Données manquantes');
        }

        if (Essai::create(
            $this->data['affaire_id'],
            $this->data['type'],
            $this->data['parametre_theorique'],
            $this->data['date_essai'],
            $this->data['mode_operatoire'] ?? null
        )) {
            return $this->json(['message' => 'Essai créé'], 201);
        }
        return $this->error('Erreur de création', 500);
    }

    private function update()
    {
        if (!isset($this->data['id'])) {
            return $this->error('ID manquant');
        }

        if (Essai::update(
            $this->data['id'],
            $this->data['type'],
            $this->data['parametre_theorique'],
            $this->data['statut'],
            $this->data['resultat'] ?? null,
            $this->data['mode_operatoire'] ?? null
        )) {
            return $this->json(['message' => 'Essai mis à jour']);
        }
        return $this->error('Erreur de mise à jour', 500);
    }

    private function delete()
    {
        if (!isset($this->data['id'])) {
            return $this->error('ID manquant');
        }

        if (Essai::delete($this->data['id'])) {
            return $this->json(['message' => 'Essai supprimé']);
        }
        return $this->error('Erreur de suppression', 500);
    }
}
