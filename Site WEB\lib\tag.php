<?php
require_once(__DIR__ . '/../config/database.php');

class Tag
{
    public static function create($nom)
    {
        $stmt = self::getDb()->prepare("INSERT INTO tags (nom) VALUES (?)");
        return $stmt->execute([$nom]);
    }

    // Créer un nouveau tag

    private static function getDb()
    {
        return Database::getInstance()->getConnection();
    }

    // Vérifier si un tag existe

    public static function exists($nom)
    {
        $stmt = self::getDb()->prepare("SELECT COUNT(*) FROM tags WHERE nom = ?");
        $stmt->execute([$nom]);
        return $stmt->fetchColumn() > 0;
    }

    // Récupérer un tag par son nom
    public static function getByNom($nom)
    {
        $stmt = self::getDb()->prepare("SELECT * FROM tags WHERE nom = ?");
        $stmt->execute([$nom]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    // Associer un tag à une affaire
    public static function associerAffaire($affaire_id, $tag_id)
    {
        $stmt = self::getDb()->prepare("INSERT IGNORE INTO affaires_tags (affaire_id, tag_id) VALUES (?, ?)");
        return $stmt->execute([$affaire_id, $tag_id]);
    }

    // Dissocier un tag d'une affaire
    public static function dissocierAffaire($affaire_id, $tag_id)
    {
        $stmt = self::getDb()->prepare("DELETE FROM affaires_tags WHERE affaire_id = ? AND tag_id = ?");
        return $stmt->execute([$affaire_id, $tag_id]);
    }

    // Dissocier tous les tags d'une affaire
    public static function dissocierTousTagsAffaire($affaire_id)
    {
        $stmt = self::getDb()->prepare("DELETE FROM affaires_tags WHERE affaire_id = ?");
        return $stmt->execute([$affaire_id]);
    }

    // Récupérer tous les tags
    public static function getAll()
    {
        $stmt = self::getDb()->query("SELECT * FROM tags ORDER BY nom");
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Obtenir les tags d'une affaire
    public static function getByAffaireId($affaire_id)
    {
        $stmt = self::getDb()->prepare("
            SELECT t.* 
            FROM tags t 
            JOIN affaires_tags AT ON t.id = AT.tag_id 
            WHERE AT.affaire_id = ?
            ORDER BY t.nom
        ");
        $stmt->execute([$affaire_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}