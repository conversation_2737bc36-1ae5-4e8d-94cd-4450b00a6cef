<?php
require_once(__DIR__ . '/../config/database.php');

class ModeleEssai
{
    public static function getAll()
    {
        $stmt = self::getDb()->query("SELECT * FROM modeles_essais ORDER BY nom");
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    private static function getDb()
    {
        return Database::getConnection();
    }

    public static function getById($id)
    {
        $stmt = self::getDb()->prepare("SELECT * FROM modeles_essais WHERE id = ?");
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public static function create($nom, $type, $parametre_theorique, $mode_operatoire = null)
    {
        $stmt = self::getDb()->prepare("
            INSERT INTO modeles_essais (nom, type, parametre_theorique, mode_operatoire) 
            VALUES (?, ?, ?, ?)
        ");
        return $stmt->execute([$nom, $type, $parametre_theorique, $mode_operatoire]);
    }
}