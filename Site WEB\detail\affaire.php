<?php
session_start();
require_once(__DIR__ . '/../lib/affaires.php');
require_once(__DIR__ . '/../lib/essais.php');
require_once(__DIR__ . '/../lib/pv.php');
require_once(__DIR__ . '/../lib/tag.php');
require_once(__DIR__ . '/../lib/rappel.php');

if (!isset($_SESSION['user'])) {
    header('Location: /auth/login.php');
    exit;
}

if (!isset($_GET['id'])) {
    header('Location: /affaires.php');
    exit;
}

$affaire = Affaire::getById($_GET['id']);
if (!$affaire) {
    header('Location: /affaires.php');
    exit;
}

// Récupérer les essais liés à cette affaire
$essais = Essai::getByAffaireId($affaire['id']);

// Récupérer les PV liés à cette affaire
$pvs = PV::getByAffaireId($affaire['id']);

// Récupérer les rappels liés à cette affaire
$rappels = Rappel::getByAffaireId($affaire['id']);

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'update':
                try {
                    if (Affaire::update(
                        $_POST['id'],
                        $_POST['numero'],
                        $_POST['client'],
                        $_POST['description'],
                        $_POST['statut'],
                        array_map('trim', explode(',', $_POST['tags'])),
                        $_SESSION['user']['id']
                    )) {
                        $success = "Affaire mise à jour avec succès";
                    } else {
                        $error = "Erreur lors de la mise à jour de l'affaire";
                    }
                } catch (Exception $e) {
                    $error = "Erreur lors de la mise à jour de l'affaire : " . $e->getMessage();
                }
                break;
            case 'delete':
                try {
                    if (Affaire::delete($_POST['id'])) {
                        $success = "Affaire supprimée avec succès";
                        header('Location: /affaires.php');
                    } else {
                        $error = "Erreur lors de la suppression de l'affaire";
                    }
                } catch (Exception $e) {
                    $error = "Erreur lors de la suppression de l'affaire : " . $e->getMessage();
                }
                break;
            case 'create_rappel':
                if (Rappel::create(
                    $_POST['affaire_id'],
                    $_POST['titre'],
                    $_POST['description'],
                    $_POST['date_echeance'],
                    $_SESSION['user']['id']
                )) {
                    $success = "Rappel créé avec succès";
                } else {
                    $error = "Erreur lors de la création du rappel";
                }
                break;
            case 'update_rappel':
                if (Rappel::update(
                    $_POST['rappel_id'],
                    $_POST['titre'],
                    $_POST['description'],
                    $_POST['date_echeance'],
                    $_POST['statut']
                )) {
                    $success = "Rappel mis à jour avec succès";
                } else {
                    $error = "Erreur lors de la mise à jour du rappel";
                }
                break;
            case 'delete_rappel':
                if (Rappel::delete($_POST['rappel_id'])) {
                    $success = "Rappel supprimé avec succès";
                } else {
                    $error = "Erreur lors de la suppression du rappel";
                }
                break;
        }
    }
}

ob_start();
?>

    <div class="p-4">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-2xl font-bold dark:text-white">Détail de l'affaire
                #<?php echo htmlspecialchars($affaire['numero']); ?></h2>
            <div>
                <button data-modal-target="editAffaireModal" data-modal-toggle="editAffaireModal"
                        class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">
                    Modifier
                </button>
                <button data-modal-target="deleteAffaireModal" data-modal-toggle="deleteAffaireModal"
                        class="focus:outline-none text-white bg-red-700 hover:bg-red-800 focus:ring-4 focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-900">
                    Supprimer
                </button>
            </div>
        </div>

        <div class="grid md:grid-cols-2 gap-4">
            <!-- Informations générales -->
            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
                <h3 class="text-lg font-semibold mb-4 dark:text-white">Informations générales</h3>
                <dl class="grid grid-cols-1 gap-4">
                    <div>
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Numéro d'affaire</dt>
                        <dd class="text-lg font-semibold text-gray-900 dark:text-white"><?php echo htmlspecialchars($affaire['numero']); ?></dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Client</dt>
                        <dd class="text-lg font-semibold text-gray-900 dark:text-white"><?php echo htmlspecialchars($affaire['client']); ?></dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Date de création</dt>
                        <dd class="text-lg font-semibold text-gray-900 dark:text-white"><?php echo htmlspecialchars($affaire['date_creation']); ?></dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Statut</dt>
                        <dd>
                        <span class="text-sm font-medium px-2.5 py-0.5 rounded-md border 
                            <?php
                        switch ($affaire['statut']) {
                            case 'Annulé':
                                echo 'bg-red-100 text-red-800 dark:bg-gray-700 dark:text-red-400 border-red-100 dark:border-red-500';
                                break;
                            case 'Terminé':
                                echo 'bg-green-100 text-green-800 dark:bg-gray-700 dark:text-green-400 border-green-100 dark:border-green-500';
                                break;
                            case 'En cours':
                                echo 'bg-blue-100 text-blue-800 dark:bg-gray-700 dark:text-blue-400 border-blue-100 dark:border-blue-500';
                                break;
                            default:
                                echo 'bg-yellow-100 text-yellow-800 dark:bg-gray-700 dark:text-yellow-400 border-yellow-100 dark:border-yellow-500';
                        }
                        ?>">
                            <?php echo htmlspecialchars($affaire['statut']); ?>
                        </span>
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Créé par</dt>
                        <dd class="text-lg font-semibold text-gray-900 dark:text-white"><?php echo htmlspecialchars($affaire['created_by_username']); ?></dd>
                    </div>
                </dl>
            </div>

            <!-- Description -->
            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
                <h3 class="text-lg font-semibold mb-4 dark:text-white">Description</h3>
                <div class="prose dark:prose-invert">
                    <p class="text-gray-900 dark:text-white"><?php echo nl2br(htmlspecialchars($affaire['description'] ?? '')); ?></p>
                </div>
            </div>

            <!-- Tags -->
            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
                <h3 class="text-lg font-semibold mb-4 dark:text-white">Tags</h3>
                <div class="flex flex-wrap gap-2">
                    <?php
                    $tags = Tag::getByAffaireId($affaire['id']);
                    if ($tags):
                        foreach ($tags as $tag): ?>
                            <span class="bg-blue-100 text-blue-800 text-sm font-medium px-2.5 py-0.5 rounded-full dark:bg-gray-700 dark:text-blue-400 border border-blue-400">
                            <?php echo htmlspecialchars($tag['nom']); ?>
                        </span>
                        <?php endforeach;
                    else: ?>
                        <p class="text-gray-500 dark:text-gray-400">Aucun tag associé</p>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Historique -->
            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
                <h3 class="text-lg font-semibold mb-4 dark:text-white">Historique des modifications</h3>
                <?php
                $historique = ObjDiff::getHistory($affaire['id'], "affaires");
                if ($historique): ?>
                    <div class="relative overflow-x-auto">
                        <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                            <tr>
                                <th scope="col" class="px-6 py-3">Date</th>
                                <th scope="col" class="px-6 py-3">Modification</th>
                                <th scope="col" class="px-6 py-3">Par</th>
                            </tr>
                            </thead>
                            <tbody>
                            <?php foreach ($historique as $modification): ?>
                                <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                                    <td class="px-6 py-4">
                                        <?php echo (new DateTime($modification['date_modification']))->format('d/m/Y H:i'); ?>
                                    </td>
                                    <td class="px-6 py-4">
                                        <?php
                                        $changes = json_decode($modification['changements'], true);
                                        foreach ($changes as $field => $change) {
                                            if ($field === 'creation') {
                                                echo "Création de l'affaire";
                                            } elseif ($field === 'suppression') {
                                                echo "Suppression de l'affaire";
                                            } else {
                                                echo htmlspecialchars(ucfirst($field)) . ' : ';
                                                echo htmlspecialchars($change['old'] ?? 'Non défini') . ' → ' .
                                                    htmlspecialchars($change['new'] ?? 'Non défini');
                                            }
                                            echo '<br>';
                                        }
                                        ?>
                                    </td>
                                    <td class="px-6 py-4">
                                        <?php echo htmlspecialchars($modification['modifie_par']); ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <p class="text-gray-500 dark:text-gray-400">Aucune modification enregistrée</p>
                <?php endif; ?>
            </div>

            <!-- Liste des essais -->
            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow md:col-span-2">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold dark:text-white">Essais associés</h3>
                    <a href="/essais.php?new=true&affaire=<?php echo $affaire['id']; ?>"
                       class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">
                        Nouvel essai
                    </a>
                </div>
                <?php if ($essais): ?>
                    <div class="relative overflow-x-auto">
                        <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                            <tr>
                                <th scope="col" class="px-6 py-3">Type</th>
                                <th scope="col" class="px-6 py-3">Date</th>
                                <th scope="col" class="px-6 py-3">Statut</th>
                                <th scope="col" class="px-6 py-3">Actions</th>
                            </tr>
                            </thead>
                            <tbody>
                            <?php foreach ($essais as $essai): ?>
                                <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                                    <td class="px-6 py-4"><?php echo htmlspecialchars($essai['type']); ?></td>
                                    <td class="px-6 py-4"><?php echo htmlspecialchars($essai['date_essai']); ?></td>
                                    <td class="px-6 py-4">
                                        <span class="text-xs font-medium px-2.5 py-0.5 rounded-md border 
                                            <?php
                                        switch ($essai['statut']) {
                                            case 'Annulé':
                                                echo 'bg-red-100 text-red-800 dark:bg-gray-700 dark:text-red-400 border-red-100 dark:border-red-500';
                                                break;
                                            case 'Terminé':
                                                echo 'bg-green-100 text-green-800 dark:bg-gray-700 dark:text-green-400 border-green-100 dark:border-green-500';
                                                break;
                                            case 'En cours':
                                                echo 'bg-blue-100 text-blue-800 dark:bg-gray-700 dark:text-blue-400 border-blue-100 dark:border-blue-500';
                                                break;
                                            default:
                                                echo 'bg-yellow-100 text-yellow-800 dark:bg-gray-700 dark:text-yellow-400 border-yellow-100 dark:border-yellow-500';
                                        }
                                        ?>">
                                            <?php echo htmlspecialchars($essai['statut']); ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4">
                                        <a href="/detail/essai.php?id=<?php echo $essai['id']; ?>"
                                           class="font-medium text-blue-600 dark:text-blue-500 hover:underline">Voir
                                            détails</a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <p class="text-gray-500 dark:text-gray-400">Aucun essai n'a été créé pour cette affaire.</p>
                <?php endif; ?>
            </div>

            <!-- Liste des PV -->
            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow md:col-span-2">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold dark:text-white">Procès-verbaux</h3>
                    <a href="/pv.php?new=true&affaire=<?php echo $affaire['id']; ?>"
                       class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">
                        Nouveau PV
                    </a>
                </div>
                <?php if ($pvs): ?>
                    <div class="relative overflow-x-auto">
                        <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                            <tr>
                                <th scope="col" class="px-6 py-3">Numéro</th>
                                <th scope="col" class="px-6 py-3">Date</th>
                                <th scope="col" class="px-6 py-3">Statut</th>
                                <th scope="col" class="px-6 py-3">Créé par</th>
                                <th scope="col" class="px-6 py-3">Actions</th>
                            </tr>
                            </thead>
                            <tbody>
                            <?php foreach ($pvs as $pv): ?>
                                <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                                    <td class="px-6 py-4"><?php echo htmlspecialchars($pv['numero']); ?></td>
                                    <td class="px-6 py-4"><?php echo htmlspecialchars($pv['date_creation']); ?></td>
                                    <td class="px-6 py-4">
                                        <span class="text-xs font-medium px-2.5 py-0.5 rounded-md border 
                                            <?php
                                        switch ($pv['statut']) {
                                            case 'Annulé':
                                                echo 'bg-red-100 text-red-800 dark:bg-gray-700 dark:text-red-400 border-red-100 dark:border-red-500';
                                                break;
                                            case 'Finalisé':
                                            case 'Envoyé':
                                                echo 'bg-green-100 text-green-800 dark:bg-gray-700 dark:text-green-400 border-green-100 dark:border-green-500';
                                                break;
                                            default:
                                                echo 'bg-yellow-100 text-yellow-800 dark:bg-gray-700 dark:text-yellow-400 border-yellow-100 dark:border-yellow-500';
                                        }
                                        ?>">
                                            <?php echo htmlspecialchars($pv['statut']); ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4"><?php echo htmlspecialchars($pv['created_by_name']); ?></td>
                                    <td class="px-6 py-4">
                                        <a href="/detail/pv.php?id=<?php echo $pv['id']; ?>"
                                           class="font-medium text-blue-600 dark:text-blue-500 hover:underline">Voir
                                            détails</a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <p class="text-gray-500 dark:text-gray-400">Aucun procès-verbal n'a été créé pour cette affaire.</p>
                <?php endif; ?>
            </div>

            <!-- Rappels -->
            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow md:col-span-2">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold dark:text-white">Rappels</h3>
                    <button data-modal-target="createRappelModal" data-modal-toggle="createRappelModal"
                            class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">
                        Nouveau rappel
                    </button>
                </div>
                <?php if ($rappels): ?>
                    <div class="relative overflow-x-auto">
                        <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                            <tr>
                                <th scope="col" class="px-6 py-3">Titre</th>
                                <th scope="col" class="px-6 py-3">Description</th>
                                <th scope="col" class="px-6 py-3">Date d'échéance</th>
                                <th scope="col" class="px-6 py-3">Statut</th>
                                <th scope="col" class="px-6 py-3">Créé par</th>
                                <th scope="col" class="px-6 py-3">Actions</th>
                            </tr>
                            </thead>
                            <tbody>
                            <?php foreach ($rappels as $rappel): ?>
                                <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                                    <td class="px-6 py-4"><?php echo htmlspecialchars($rappel['titre']); ?></td>
                                    <td class="px-6 py-4"><?php echo htmlspecialchars($rappel['description']); ?></td>
                                    <td class="px-6 py-4"><?php echo htmlspecialchars($rappel['date_echeance']); ?></td>
                                    <td class="px-6 py-4">
                                        <span class="text-xs font-medium px-2.5 py-0.5 rounded-md border 
                                            <?php
                                        switch ($rappel['statut']) {
                                            case 'Completé':
                                                echo 'bg-green-100 text-green-800 dark:bg-gray-700 dark:text-green-400 border-green-100 dark:border-green-500';
                                                break;
                                            case 'Annulé':
                                                echo 'bg-red-100 text-red-800 dark:bg-gray-700 dark:text-red-400 border-red-100 dark:border-red-500';
                                                break;
                                            default:
                                                echo 'bg-yellow-100 text-yellow-800 dark:bg-gray-700 dark:text-yellow-400 border-yellow-100 dark:border-yellow-500';
                                        }
                                        ?>">
                                            <?php echo htmlspecialchars($rappel['statut']); ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4"><?php echo htmlspecialchars($rappel['cree_par_nom']); ?></td>
                                    <td class="px-6 py-4">
                                        <button data-modal-target="editRappelModal<?php echo $rappel['id']; ?>"
                                                data-modal-toggle="editRappelModal<?php echo $rappel['id']; ?>"
                                                class="font-medium text-blue-600 dark:text-blue-500 hover:underline">
                                            Modifier
                                        </button>
                                        <button data-modal-target="deleteRappelModal<?php echo $rappel['id']; ?>"
                                                data-modal-toggle="deleteRappelModal<?php echo $rappel['id']; ?>"
                                                class="font-medium text-red-600 dark:text-red-500 hover:underline ms-3">
                                            Supprimer
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <p class="text-gray-500 dark:text-gray-400">Aucun rappel n'a été créé pour cette affaire.</p>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Modal de modification -->
    <div id="editAffaireModal" tabindex="-1" aria-hidden="true"
         class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
        <div class="relative p-4 w-full max-w-md max-h-full">
            <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                        Modifier l'affaire
                    </h3>
                    <button type="button"
                            class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                            data-modal-hide="editAffaireModal">
                        <svg class="w-3 h-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                        </svg>
                        <span class="sr-only">Fermer</span>
                    </button>
                </div>
                <form class="p-4 md:p-5" method="POST">
                    <input type="hidden" name="action" value="update">
                    <input type="hidden" name="id" value="<?php echo $affaire['id']; ?>">
                    <div class="grid gap-4 mb-4">
                        <div>
                            <label for="numero" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Numéro</label>
                            <input type="text" name="numero" id="numero"
                                   value="<?php echo htmlspecialchars($affaire['numero']); ?>"
                                   class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                   required>
                        </div>
                        <div>
                            <label for="client" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Client</label>
                            <input type="text" name="client" id="client"
                                   value="<?php echo htmlspecialchars($affaire['client']); ?>"
                                   class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                   required>
                        </div>
                        <div>
                            <label for="description"
                                   class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Description</label>
                            <textarea name="description" id="description" rows="4"
                                      class="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"><?php echo htmlspecialchars($affaire['description']); ?></textarea>
                        </div>
                        <div>
                            <label for="statut" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Statut</label>
                            <select name="statut" id="statut"
                                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                    required>
                                <option value="En cours" <?php echo $affaire['statut'] == 'En cours' ? 'selected' : ''; ?>>
                                    En cours
                                </option>
                                <option value="Terminé" <?php echo $affaire['statut'] == 'Terminé' ? 'selected' : ''; ?>>
                                    Terminé
                                </option>
                                <option value="Annulé" <?php echo $affaire['statut'] == 'Annulé' ? 'selected' : ''; ?>>
                                    Annulé
                                </option>
                            </select>
                        </div>
                        <div>
                            <label for="tags" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Tags
                                (séparés par des virgules)</label>
                            <?php
                            $tags = Tag::getByAffaireId($affaire['id']);
                            $tagString = implode(', ', array_map(function ($tag) {
                                return $tag['nom'];
                            }, $tags));
                            ?>
                            <input type="text" name="tags" id="tags" value="<?php echo htmlspecialchars($tagString); ?>"
                                   class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                   placeholder="urgent, prioritaire, en attente...">
                        </div>
                    </div>
                    <button type="submit"
                            class="text-white inline-flex items-center bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                        Enregistrer les modifications
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal de suppression -->
    <div id="deleteAffaireModal" tabindex="-1" aria-hidden="true"
         class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
        <div class="relative p-4 w-full max-w-md max-h-full">
            <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                        Confirmer la suppression
                    </h3>
                    <button type="button"
                            class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                            data-modal-hide="deleteAffaireModal">
                        <svg class="w-3 h-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                        </svg>
                        <span class="sr-only">Fermer</span>
                    </button>
                </div>
                <div class="p-4 md:p-5">
                    <p class="text-gray-500 dark:text-gray-300">Êtes-vous sûr de vouloir supprimer cette affaire ? Cette
                        action supprimera également tous les essais et procès-verbaux associés. Cette action est
                        irréversible.</p>
                    <form method="POST" class="mt-5">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="id" value="<?php echo $affaire['id']; ?>">
                        <button type="submit"
                                class="text-white bg-red-600 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 dark:focus:ring-red-800 font-medium rounded-lg text-sm inline-flex items-center px-5 py-2.5 text-center me-2">
                            Oui, supprimer
                        </button>
                        <button type="button" data-modal-hide="deleteAffaireModal"
                                class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900 focus:z-10 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-500 dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-gray-600">
                            Non, annuler
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de création de rappel -->
    <div id="createRappelModal" tabindex="-1" aria-hidden="true"
         class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
        <div class="relative p-4 w-full max-w-md max-h-full">
            <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                        Nouveau rappel
                    </h3>
                    <button type="button"
                            class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                            data-modal-hide="createRappelModal">
                        <svg class="w-3 h-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                        </svg>
                        <span class="sr-only">Fermer</span>
                    </button>
                </div>
                <form class="p-4 md:p-5" method="POST">
                    <input type="hidden" name="action" value="create_rappel">
                    <input type="hidden" name="affaire_id" value="<?php echo $affaire['id']; ?>">
                    <div class="grid gap-4 mb-4">
                        <div>
                            <label for="titre" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Titre</label>
                            <input type="text" name="titre" id="titre"
                                   class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                   required>
                        </div>
                        <div>
                            <label for="description"
                                   class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Description</label>
                            <textarea name="description" id="description" rows="4"
                                      class="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"></textarea>
                        </div>
                        <div>
                            <label for="date_echeance"
                                   class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Date
                                d'échéance</label>
                            <input type="date" name="date_echeance" id="date_echeance"
                                   class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                   required>
                        </div>
                    </div>
                    <button type="submit"
                            class="text-white inline-flex items-center bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                        Créer le rappel
                    </button>
                </form>
            </div>
        </div>
    </div>

<?php foreach ($rappels as $rappel): ?>
    <!-- Modal de modification de rappel -->
    <div id="editRappelModal<?php echo $rappel['id']; ?>" tabindex="-1" aria-hidden="true"
         class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
        <div class="relative p-4 w-full max-w-md max-h-full">
            <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                        Modifier le rappel
                    </h3>
                    <button type="button"
                            class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                            data-modal-hide="editRappelModal<?php echo $rappel['id']; ?>">
                        <svg class="w-3 h-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                        </svg>
                        <span class="sr-only">Fermer</span>
                    </button>
                </div>
                <form class="p-4 md:p-5" method="POST">
                    <input type="hidden" name="action" value="update_rappel">
                    <input type="hidden" name="rappel_id" value="<?php echo $rappel['id']; ?>">
                    <div class="grid gap-4 mb-4">
                        <div>
                            <label for="titre<?php echo $rappel['id']; ?>"
                                   class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Titre</label>
                            <input type="text" name="titre" id="titre<?php echo $rappel['id']; ?>"
                                   value="<?php echo htmlspecialchars($rappel['titre']); ?>"
                                   class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                   required>
                        </div>
                        <div>
                            <label for="description<?php echo $rappel['id']; ?>"
                                   class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Description</label>
                            <textarea name="description" id="description<?php echo $rappel['id']; ?>" rows="4"
                                      class="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"><?php echo htmlspecialchars($rappel['description']); ?></textarea>
                        </div>
                        <div>
                            <label for="date_echeance<?php echo $rappel['id']; ?>"
                                   class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Date
                                d'échéance</label>
                            <input type="date" name="date_echeance" id="date_echeance<?php echo $rappel['id']; ?>"
                                   value="<?php echo htmlspecialchars($rappel['date_echeance']); ?>"
                                   class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                   required>
                        </div>
                        <div>
                            <label for="statut<?php echo $rappel['id']; ?>"
                                   class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Statut</label>
                            <select name="statut" id="statut<?php echo $rappel['id']; ?>"
                                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                    required>
                                <option value="En attente" <?php echo $rappel['statut'] == 'En attente' ? 'selected' : ''; ?>>
                                    En attente
                                </option>
                                <option value="Completé" <?php echo $rappel['statut'] == 'Completé' ? 'selected' : ''; ?>>
                                    Completé
                                </option>
                                <option value="Annulé" <?php echo $rappel['statut'] == 'Annulé' ? 'selected' : ''; ?>>
                                    Annulé
                                </option>
                            </select>
                        </div>
                    </div>
                    <button type="submit"
                            class="text-white inline-flex items-center bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                        Enregistrer les modifications
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal de suppression de rappel -->
    <div id="deleteRappelModal<?php echo $rappel['id']; ?>" tabindex="-1" aria-hidden="true"
         class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
        <div class="relative p-4 w-full max-w-md max-h-full">
            <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                        Confirmer la suppression
                    </h3>
                    <button type="button"
                            class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                            data-modal-hide="deleteRappelModal<?php echo $rappel['id']; ?>">
                        <svg class="w-3 h-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                        </svg>
                        <span class="sr-only">Fermer</span>
                    </button>
                </div>
                <div class="p-4 md:p-5">
                    <p class="text-gray-500 dark:text-gray-300">Êtes-vous sûr de vouloir supprimer ce rappel ? Cette
                        action est irréversible.</p>
                    <form method="POST" class="mt-5">
                        <input type="hidden" name="action" value="delete_rappel">
                        <input type="hidden" name="rappel_id" value="<?php echo $rappel['id']; ?>">
                        <button type="submit"
                                class="text-white bg-red-600 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 dark:focus:ring-red-800 font-medium rounded-lg text-sm inline-flex items-center px-5 py-2.5 text-center me-2">
                            Oui, supprimer
                        </button>
                        <button type="button" data-modal-hide="deleteRappelModal<?php echo $rappel['id']; ?>"
                                class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900 focus:z-10 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-500 dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-gray-600">
                            Non, annuler
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php endforeach; ?>

<?php
$pageContent = ob_get_clean();
include '../layout.php';
?>