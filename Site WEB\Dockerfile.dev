# Dockerfile de développement pour FluidMotion Web Application
# Optimisé pour le développement avec montage de volumes dynamiques

FROM php:8.4-apache AS development

# Définir les variables d'environnement pour le développement
ENV APACHE_DOCUMENT_ROOT=/var/www/html
ENV APACHE_RUN_USER=www-data
ENV APACHE_RUN_GROUP=www-data
ENV APP_ENV=development

# Installer les dépendances système nécessaires pour le développement
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        libpng-dev \
        libjpeg-dev \
        libfreetype6-dev \
        libzip-dev \
        libonig-dev \
        libicu-dev \
        libcurl4-openssl-dev \
        libxml2-dev \
        mariadb-client \
        default-mysql-client \
        unzip \
        git \
        procps \
        cron \
        supervisor \
        curl \
        wget \
        vim \
        nano \
        htop \
        inotify-tools \
    && rm -rf /var/lib/apt/lists/*

# Configurer et installer les extensions PHP
RUN docker-php-ext-configure gd --with-freetype --with-jpeg && \
    docker-php-ext-install \
        mysqli \
        pdo \
        pdo_mysql \
        mbstring \
        curl \
        xml \
        opcache \
        gd \
        zip \
        intl

# Installer Xdebug pour le développement
RUN pecl install xdebug && \
    docker-php-ext-enable xdebug

# Installer Composer
COPY --from=composer:2 /usr/bin/composer /usr/bin/composer

# Configuration Xdebug pour le développement
COPY <<EOF /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini
xdebug.mode=develop,debug,coverage
xdebug.client_host=host.docker.internal
xdebug.start_with_request=yes
xdebug.client_port=9003
xdebug.log=/tmp/xdebug.log
xdebug.idekey=VSCODE
xdebug.discover_client_host=1
EOF

# Configuration PHP optimisée pour le développement
COPY <<EOF /usr/local/etc/php/conf.d/development.ini
; Configuration de développement
display_errors=On
error_reporting=E_ALL
log_errors=On
error_log=/var/log/php_errors.log
memory_limit=512M
upload_max_filesize=100M
post_max_size=100M
max_execution_time=300
max_input_time=300

; Configuration OPcache pour le développement (désactivé pour hot-reload)
opcache.enable=0
opcache.validate_timestamps=1
opcache.revalidate_freq=0

; Configuration de session
session.cookie_httponly=1
session.use_strict_mode=1
session.cookie_secure=0

; Configuration pour le développement
auto_prepend_file=
auto_append_file=
default_charset=UTF-8
EOF

# Configurer Apache pour le développement
RUN a2enmod rewrite headers ssl expires && \
    sed -i 's/AllowOverride None/AllowOverride All/g' /etc/apache2/apache2.conf

# Configuration Apache pour le développement avec hot-reload
COPY <<EOF /etc/apache2/conf-available/development.conf
# Configuration de développement
ServerTokens Full
ServerSignature On

# Headers de développement
Header always set X-Development-Mode "true"
Header always set Access-Control-Allow-Origin "*"
Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"

# Configuration du répertoire racine pour le développement
<Directory /var/www/html>
    Options +Indexes +FollowSymLinks +MultiViews
    AllowOverride All
    Require all granted
    DirectoryIndex index.php index.html
    
    # Désactiver le cache pour le développement
    ExpiresActive Off
    Header unset ETag
    Header set Cache-Control "max-age=0, no-cache, no-store, must-revalidate"
    Header set Pragma "no-cache"
    Header set Expires "Wed, 11 Jan 1984 05:00:00 GMT"
</Directory>

# Permettre l'accès aux répertoires de configuration en développement
<Directory /var/www/html/config>
    Require all granted
</Directory>

<Directory /var/www/html/lib>
    Require all granted
</Directory>

# Configuration pour les fichiers PHP
<FilesMatch "\.php$">
    SetHandler application/x-httpd-php
</FilesMatch>
EOF

RUN a2enconf development

# Créer les répertoires nécessaires avec les bonnes permissions
RUN mkdir -p /var/www/html/backups \
             /var/www/html/pdf_exports \
             /var/www/html/temp/mpdf \
             /var/www/html/vendor \
             /var/log/app \
    && chown -R www-data:www-data /var/www/html \
    && chown -R www-data:www-data /var/log/app \
    && chmod -R 755 /var/www/html

# Créer le script d'entrée pour le développement
COPY <<EOF /usr/local/bin/docker-entrypoint-dev.sh
#!/bin/bash
set -e

# Fonction de logging
log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] [DEV] $1"
}

# Vérifier la disponibilité de la base de données (non bloquant)
check_db_connection() {
    if [ -n "\$DB_HOST" ] && [ -n "\$DB_USER" ] && [ -n "\$DB_PASS" ]; then
        log "Vérification de la connexion à la base de données..."
        if mysql -h"\$DB_HOST" -u"\$DB_USER" -p"\$DB_PASS" -e "SELECT 1" >/dev/null 2>&1; then
            log "Base de données disponible!"
        else
            log "Base de données non disponible (l'application démarrera quand même)"
        fi
    else
        log "Variables de base de données non définies"
    fi
}

# Configurer les permissions pour le développement
setup_permissions() {
    log "Configuration des permissions pour le développement..."
    
    # S'assurer que www-data peut écrire dans tous les répertoires nécessaires
    chown -R www-data:www-data /var/www/html
    
    # Permissions spéciales pour les répertoires de travail
    chmod -R 755 /var/www/html
    chmod -R 777 /var/www/html/backups /var/www/html/pdf_exports /var/www/html/temp
    
    # Créer les répertoires s'ils n'existent pas
    mkdir -p /var/www/html/backups /var/www/html/pdf_exports /var/www/html/temp/mpdf
    chown -R www-data:www-data /var/www/html/backups /var/www/html/pdf_exports /var/www/html/temp
}

# Installer les dépendances Composer si nécessaire
install_dependencies() {
    if [ -f /var/www/html/composer.json ]; then
        log "Installation des dépendances Composer..."
        cd /var/www/html
        composer install --dev --optimize-autoloader --no-interaction
        chown -R www-data:www-data /var/www/html/vendor
    fi
}

# Vérifier les outils de base de données
check_db_tools() {
    log "Vérification des outils de base de données..."
    mysqldump --version
    mysql --version
}

# Afficher les informations de développement
show_dev_info() {
    log "=== MODE DÉVELOPPEMENT ACTIVÉ ==="
    log "Xdebug: Activé (port 9003)"
    log "Erreurs PHP: Affichées"
    log "OPcache: Désactivé"
    log "Hot-reload: Activé"
    log "Répertoire monté: /var/www/html"
    log "=================================="
}

# Fonction principale
main() {
    log "Démarrage de l'application FluidMotion en mode développement..."
    
    show_dev_info
    setup_permissions
    check_db_tools
    install_dependencies
    check_db_connection
    
    log "Démarrage d'Apache en mode développement..."
    exec apache2-foreground
}

# Exécuter la fonction principale
main "\$@"
EOF

RUN chmod +x /usr/local/bin/docker-entrypoint-dev.sh

# Health check adapté pour le développement
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/health.php || exit 1

# Exposer le port
EXPOSE 80

# Point d'entrée pour le développement
ENTRYPOINT ["/usr/local/bin/docker-entrypoint-dev.sh"]
