from flask import Flask, jsonify, request, abort
from flask_cors import CORS
import os
import json
import time
from datetime import datetime
import pymysql
import sys
import random  # Pour la simulation

# Ajouter le chemin vers les fichiers de configuration du site web
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../Site WEB/config')))

# Importation conditionnelle
SIMULATION_MODE = False  # Sera mis à True si la connexion série échoue

try:
    # Import de notre classe SerialComm et DataAcquisition
    from serial_comm import SerialComm
    from data_acquisition import DataAcquisition
except ImportError as e:
    print(f"Erreur d'importation: {e}")
    print("Passer en mode SIMULATION")
    SIMULATION_MODE = True

app = Flask(__name__)
CORS(app)  # Active CORS pour permettre les requêtes cross-domain

# Configuration de la base de données
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '',
    'db': 'verins_db',
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

# Instances globales
serial_comm = None
data_acquisition = None

class SimulatedSerialComm:
    """Classe qui simule la communication série quand l'Arduino n'est pas connecté"""
    
    def get_all_sensors(self):
        """Simule les données des capteurs"""
        pressure = random.uniform(0, 100)
        flow = random.uniform(0, 100)
        
        return {
            'pressure': pressure,
            'flow': flow,
            'timestamp': datetime.now(),
            'pressure_pascal': self._pressure_to_pascal(pressure),
            'flow_lpm': self._flow_to_lpm(flow)
        }
    
    def get_pressure(self):
        """Simule les données du capteur de pression"""
        pressure = random.uniform(0, 100)
        return {
            'raw_value': pressure,
            'timestamp': datetime.now(),
            'pascal': self._pressure_to_pascal(pressure)
        }
    
    def get_flow(self):
        """Simule les données du capteur de débit"""
        flow = random.uniform(0, 100)
        return {
            'raw_value': flow,
            'timestamp': datetime.now(),
            'lpm': self._flow_to_lpm(flow)
        }
    
    def _pressure_to_pascal(self, percentage):
        """Convertit la pression en pourcentage en Pascal"""
        return percentage * 1000
    
    def _flow_to_lpm(self, percentage):
        """Convertit le débit en pourcentage en litres par minute"""
        return percentage * 0.1
        
    def close(self):
        """Simule la fermeture de la connexion"""
        pass

def get_db_connection():
    """Établit et retourne une connexion à la base de données"""
    try:
        connection = pymysql.connect(
            host=DB_CONFIG['host'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password'],
            db=DB_CONFIG['db'],
            charset=DB_CONFIG['charset'],
            cursorclass=DB_CONFIG['cursorclass']
        )
        return connection
    except Exception as e:
        print(f"Erreur de connexion à la base de données: {e}")
        return None

def init_serial():
    """Initialise la connexion série avec l'Arduino ou passe en mode simulation"""
    global serial_comm, data_acquisition, SIMULATION_MODE
    
    if (SIMULATION_MODE):
        print("Mode SIMULATION activé - Utilisation de données simulées")
        serial_comm = SimulatedSerialComm()
        data_acquisition = DataAcquisition(DB_CONFIG, serial_comm)
        return True
    
    try:
        # Déterminer le port série en fonction du système d'exploitation
        import platform
        if platform.system() == 'Windows':
            port = 'COM3'  # Port par défaut sous Windows
        else:
            port = '/dev/ttyACM0'  # Port par défaut sous Linux/Raspberry Pi

        print(f"Tentative de connexion au port {port}...")
        serial_comm = SerialComm(port, baudrate=9600)
        
        # Initialiser le gestionnaire d'acquisition
        data_acquisition = DataAcquisition(DB_CONFIG, serial_comm)
        return True
    except Exception as e:
        print(f"Erreur d'initialisation de la communication série: {e}")
        print("Passer en mode SIMULATION")
        SIMULATION_MODE = True
        serial_comm = SimulatedSerialComm()
        data_acquisition = DataAcquisition(DB_CONFIG, serial_comm)
        return True

@app.route('/api/health', methods=['GET'])
def health_check():
    """Vérifie que l'API est en fonctionnement"""
    return jsonify({
        'status': 'ok',
        'timestamp': datetime.now().isoformat(),
        'mode': 'SIMULATION' if SIMULATION_MODE else 'HARDWARE'
    })

@app.route('/api/sensors/all', methods=['GET'])
def get_all_sensors():
    """Obtient les données de tous les capteurs"""
    global serial_comm
    if not serial_comm:
        if not init_serial():
            return jsonify({'error': 'Communication série non disponible'}), 503

    try:
        data = serial_comm.get_all_sensors()
        if data:
            return jsonify(data)
        else:
            return jsonify({'error': 'Aucune donnée reçue des capteurs'}), 404
    except Exception as e:
        return jsonify({'error': f'Erreur lors de la lecture des capteurs: {str(e)}'}), 500

@app.route('/api/sensors/pressure', methods=['GET'])
def get_pressure():
    """Obtient les données du capteur de pression"""
    global serial_comm
    if not serial_comm:
        if not init_serial():
            return jsonify({'error': 'Communication série non disponible'}), 503

    try:
        data = serial_comm.get_pressure()
        if data:
            return jsonify(data)
        else:
            return jsonify({'error': 'Aucune donnée reçue du capteur de pression'}), 404
    except Exception as e:
        return jsonify({'error': f'Erreur lors de la lecture du capteur de pression: {str(e)}'}), 500

@app.route('/api/sensors/flow', methods=['GET'])
def get_flow():
    """Obtient les données du capteur de débit"""
    global serial_comm
    if not serial_comm:
        if not init_serial():
            return jsonify({'error': 'Communication série non disponible'}), 503

    try:
        data = serial_comm.get_flow()
        if data:
            return jsonify(data)
        else:
            return jsonify({'error': 'Aucune donnée reçue du capteur de débit'}), 404
    except Exception as e:
        return jsonify({'error': f'Erreur lors de la lecture du capteur de débit: {str(e)}'}), 500

@app.route('/api/essais/<int:essai_id>/courbes', methods=['POST'])
def save_data_to_db(essai_id):
    """
    Sauvegarde les données des capteurs dans la base de données
    Requiert un essai_id valide et un type de courbe
    """
    try:
        if not request.json or 'type_courbe' not in request.json:
            return jsonify({'error': 'Type de courbe requis'}), 400

        type_courbe = request.json['type_courbe']
        if type_courbe not in ['CPA', 'CPB', 'Résultante']:
            return jsonify({'error': 'Type de courbe invalide. Utilisez CPA, CPB ou Résultante'}), 400

        # Récupérer les données des capteurs
        global serial_comm
        if not serial_comm:
            if not init_serial():
                return jsonify({'error': 'Communication série non disponible'}), 503

        data = serial_comm.get_all_sensors()
        if not data:
            return jsonify({'error': 'Aucune donnée reçue des capteurs'}), 404

        # Se connecter à la base de données
        connection = get_db_connection()
        if not connection:
            return jsonify({'error': 'Impossible de se connecter à la base de données'}), 500

        try:
            with connection.cursor() as cursor:
                # Vérifier si l'essai existe
                cursor.execute("SELECT id FROM essais WHERE id = %s", (essai_id,))
                if cursor.fetchone() is None:
                    return jsonify({'error': f'Essai avec ID {essai_id} non trouvé'}), 404

                # Préparer les données JSON
                donnees_json = json.dumps({
                    'pressure': data['pressure'],
                    'pressure_pascal': data['pressure_pascal'],
                    'flow': data['flow'],
                    'flow_lpm': data['flow_lpm'],
                    'timestamp': data['timestamp'].isoformat()
                })

                # Insérer les données dans la table des courbes
                cursor.execute(
                    "INSERT INTO courbes (essai_id, type_courbe, donnees) VALUES (%s, %s, %s)",
                    (essai_id, type_courbe, donnees_json)
                )
                connection.commit()

                return jsonify({
                    'success': True,
                    'message': f'Données sauvegardées pour l\'essai {essai_id}',
                    'courbe_id': cursor.lastrowid
                })
        except Exception as e:
            return jsonify({'error': f'Erreur lors de la sauvegarde des données: {str(e)}'}), 500
        finally:
            connection.close()
    except Exception as e:
        return jsonify({'error': f'Erreur du serveur: {str(e)}'}), 500

@app.route('/api/essais', methods=['GET'])
def get_essais():
    """Récupère la liste des essais disponibles"""
    try:
        connection = get_db_connection()
        if not connection:
            return jsonify({'error': 'Impossible de se connecter à la base de données'}), 500

        try:
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT e.id, e.type, a.numero as affaire_numero, e.date_essai, e.statut
                    FROM essais e
                    JOIN affaires a ON e.affaire_id = a.id
                    ORDER BY e.date_essai DESC
                """)
                essais = cursor.fetchall()
                return jsonify(essais)
        except Exception as e:
            return jsonify({'error': f'Erreur lors de la récupération des essais: {str(e)}'}), 500
        finally:
            connection.close()
    except Exception as e:
        return jsonify({'error': f'Erreur du serveur: {str(e)}'}), 500

@app.route('/api/courbes/<int:essai_id>', methods=['GET'])
def get_courbes_by_essai(essai_id):
    """Récupère les courbes associées à un essai spécifique"""
    try:
        connection = get_db_connection()
        if not connection:
            return jsonify({'error': 'Impossible de se connecter à la base de données'}), 500

        try:
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT id, type_courbe, donnees, timestamp
                    FROM courbes
                    WHERE essai_id = %s
                    ORDER BY timestamp DESC
                """, (essai_id,))
                courbes = cursor.fetchall()
                
                # Convertir les données JSON en objets Python
                for courbe in courbes:
                    courbe['donnees'] = json.loads(courbe['donnees'])
                
                return jsonify(courbes)
        except Exception as e:
            return jsonify({'error': f'Erreur lors de la récupération des courbes: {str(e)}'}), 500
        finally:
            connection.close()
    except Exception as e:
        return jsonify({'error': f'Erreur du serveur: {str(e)}'}), 500

@app.route('/api/acquisition/start', methods=['POST'])
def start_acquisition():
    """
    Démarre une acquisition automatique de données à intervalles réguliers
    pour un essai spécifique
    """
    global data_acquisition
    if not data_acquisition:
        if not init_serial():
            return jsonify({'error': 'Communication série non disponible'}), 503

    try:
        if not request.json or 'essai_id' not in request.json or 'type_courbe' not in request.json:
            return jsonify({'error': 'essai_id et type_courbe sont requis'}), 400
        
        essai_id = int(request.json['essai_id'])
        type_courbe = request.json['type_courbe']
        interval = float(request.json.get('interval', 1))  # Intervalle en secondes, par défaut 1s
        duration = int(request.json.get('duration', 60))  # Durée en secondes, par défaut 60s
        
        # Vérifier si l'essai existe
        connection = get_db_connection()
        if not connection:
            return jsonify({'error': 'Impossible de se connecter à la base de données'}), 500
        
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT id FROM essais WHERE id = %s", (essai_id,))
                if cursor.fetchone() is None:
                    return jsonify({'error': f'Essai avec ID {essai_id} non trouvé'}), 404
        finally:
            connection.close()
        
        # Démarrer l'acquisition
        success = data_acquisition.start_acquisition(
            essai_id=essai_id,
            type_courbe=type_courbe,
            interval=interval,
            duration=duration
        )
        
        if success:
            return jsonify({
                'success': True,
                'message': f'Acquisition démarrée pour l\'essai {essai_id}',
                'config': {
                    'essai_id': essai_id,
                    'type_courbe': type_courbe,
                    'interval': interval,
                    'duration': duration
                }
            })
        else:
            return jsonify({'error': 'Impossible de démarrer l\'acquisition'}), 500
            
    except Exception as e:
        return jsonify({'error': f'Erreur du serveur: {str(e)}'}), 500

@app.route('/api/acquisition/stop/<int:essai_id>', methods=['POST'])
def stop_acquisition(essai_id):
    """Arrête une acquisition en cours pour un essai spécifique"""
    global data_acquisition
    if not data_acquisition:
        return jsonify({'error': 'Aucun gestionnaire d\'acquisition disponible'}), 503

    try:
        success = data_acquisition.stop_acquisition(essai_id)
        if success:
            return jsonify({
                'success': True,
                'message': f'Acquisition arrêtée pour l\'essai {essai_id}'
            })
        else:
            return jsonify({'error': f'Aucune acquisition en cours pour l\'essai {essai_id}'}), 404
    except Exception as e:
        return jsonify({'error': f'Erreur du serveur: {str(e)}'}), 500

@app.route('/api/acquisition/status', methods=['GET'])
def get_acquisition_status():
    """Obtient le statut de toutes les acquisitions en cours"""
    global data_acquisition
    if not data_acquisition:
        return jsonify({'error': 'Aucun gestionnaire d\'acquisition disponible'}), 503

    try:
        status = data_acquisition.get_acquisition_status()
        return jsonify(status)
    except Exception as e:
        return jsonify({'error': f'Erreur du serveur: {str(e)}'}), 500

@app.route('/api/acquisition/status/<int:essai_id>', methods=['GET'])
def get_acquisition_status_by_id(essai_id):
    """Obtient le statut d'une acquisition spécifique"""
    global data_acquisition
    if not data_acquisition:
        return jsonify({'error': 'Aucun gestionnaire d\'acquisition disponible'}), 503

    try:
        status = data_acquisition.get_acquisition_status(essai_id)
        if status:
            return jsonify(status)
        else:
            return jsonify({'error': f'Aucune acquisition trouvée pour l\'essai {essai_id}'}), 404
    except Exception as e:
        return jsonify({'error': f'Erreur du serveur: {str(e)}'}), 500

if __name__ == '__main__':
    # Initialiser la communication série au démarrage
    init_serial()
    print(f"Mode: {'SIMULATION' if SIMULATION_MODE else 'HARDWARE'}")
    # Démarrer le serveur Flask
    app.run(host='0.0.0.0', port=5000, debug=True)