import { themes as prismThemes } from 'prism-react-renderer';
import type { Config } from '@docusaurus/types';
import type * as Preset from '@docusaurus/preset-classic';

// This runs in Node.js - Don't use client-side code here (browser APIs, JSX...)

const config: Config = {
  title: 'FluidMotion Labs',
  tagline: 'Documentation Opérationnelle - Système de Gestion des Essais Hydrauliques',
  favicon: 'img/favicon.ico',

  // Future flags, see https://docusaurus.io/docs/api/docusaurus-config#future
  // Commented out v4 flag as it can cause build issues with v3.8.0
  // future: {
  //   v4: true, // Improve compatibility with the upcoming Docusaurus v4
  // },

  // Set the production url of your site here
  url: 'https://LaProvidenceAmiens.github.io',
  // Set the /<baseUrl>/ pathname under which your site is served
  // For GitHub pages deployment, it is often '/<projectName>/'
  baseUrl: '/CIEL2_BancDeTest/',

  // GitHub pages deployment config.
  // If you aren't using GitHub pages, you don't need these.
  organizationName: 'LaProvidenceAmiens', // Usually your GitHub org/user name.
  projectName: 'CIEL2_BancDeTest', // Usually your repo name.
  deploymentBranch: 'develop',
  trailingSlash: true,

  onBrokenLinks: 'warn', // Changed from 'throw' to 'warn' to allow build with broken links
  onBrokenMarkdownLinks: 'warn',

  // Even if you don't use internationalization, you can use this field to set
  // useful metadata like html lang. For example, if your site is Chinese, you
  // may want to replace "en" with "zh-Hans".
  i18n: {
    defaultLocale: 'fr',
    locales: ['fr'],
  },

  presets: [
    [
      'classic',
      {
        docs: {
          sidebarPath: './sidebars.ts',
        },
        blog: false,
        theme: {
          customCss: './src/css/custom.css',
        },
      } satisfies Preset.Options,
    ],
  ],

  themeConfig: {
    // Replace with your project's social card
    image: 'img/docusaurus-social-card.jpg',
    navbar: {
      title: 'FluidMotion Labs',
      logo: {
        alt: 'FluidMotion Labs Logo',
        src: 'img/logo.svg',
      },
      items: [
        {
          type: 'docSidebar',
          sidebarId: 'tutorialSidebar',
          position: 'left',
          label: 'Documentation',
        },
        {
          href: 'https://github.com/LaProvidenceAmiens/CIEL2_BancDeTest',
          label: 'GitHub',
          position: 'right',
        },
      ],
    },
    footer: {
      style: 'dark',
      links: [
        {
          title: 'Documentation',
          items: [
            {
              label: 'Guide de Démarrage',
              to: '/docs/getting-started/introduction',
            },
            {
              label: 'Interface Utilisateur',
              to: '/docs/user-interface/dashboard',
            },
            {
              label: 'Modules Principaux',
              to: '/docs/modules/affaires',
            },
          ],
        },
        {
          title: 'Ressources',
          items: [
            {
              label: 'Workflows',
              to: '/docs/workflows/complete-workflow',
            },
            {
              label: 'Dépannage',
              to: '/docs/troubleshooting/common-issues',
            },
          ],
        },
        {
          title: 'Plus',
          items: [
            {
              label: 'GitHub',
              href: 'https://github.com/LaProvidenceAmiens/CIEL2_BancDeTest',
            },
          ],
        },
      ],
      copyright: `Copyright © ${new Date().getFullYear()} FluidMotion Labs. Documentation créée avec Docusaurus.`,
    },
    prism: {
      theme: prismThemes.github,
      darkTheme: prismThemes.dracula,
    },
    colorMode: {
      defaultMode: 'light',
      disableSwitch: false,
      respectPrefersColorScheme: true,
    },
  } satisfies Preset.ThemeConfig,
};

export default config;
