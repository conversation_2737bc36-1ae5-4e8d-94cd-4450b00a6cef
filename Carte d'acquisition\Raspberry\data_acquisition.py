import threading
import time
import json
import pymysql
from datetime import datetime
from serial_comm import SerialComm

class DataAcquisition:
    def __init__(self, db_config, serial_comm=None):
        """
        Initialise le gestionnaire d'acquisition de données
        
        Args:
            db_config (dict): Configuration de la base de données
            serial_comm (SerialComm): Instance de communication série existante
        """
        self.db_config = db_config
        self.serial_comm = serial_comm
        self.acquisition_thread = None
        self.running = False
        self.acquisitions = {}  # Stocke les acquisitions en cours par ID d'essai
    
    def set_serial_comm(self, serial_comm):
        """Définit l'instance de communication série à utiliser"""
        self.serial_comm = serial_comm
    
    def get_db_connection(self):
        """Établit et retourne une connexion à la base de données"""
        try:
            connection = pymysql.connect(
                host=self.db_config['host'],
                user=self.db_config['user'],
                password=self.db_config['password'],
                db=self.db_config['db'],
                charset=self.db_config['charset'],
                cursorclass=pymysql.cursors.DictCursor
            )
            return connection
        except Exception as e:
            print(f"Erreur de connexion à la base de données: {e}")
            return None
    
    def start_acquisition(self, essai_id, type_courbe, interval=1.0, duration=60):
        """
        Démarre une acquisition automatique à intervalles réguliers
        
        Args:
            essai_id (int): ID de l'essai associé
            type_courbe (str): Type de courbe ('CPA', 'CPB', 'Résultante')
            interval (float): Intervalle entre les acquisitions en secondes
            duration (int): Durée totale de l'acquisition en secondes
        
        Returns:
            bool: True si l'acquisition a démarré avec succès, False sinon
        """
        if not self.serial_comm:
            print("Communication série non initialisée")
            return False
        
        # Vérifier si une acquisition est déjà en cours pour cet essai
        if essai_id in self.acquisitions:
            print(f"Une acquisition est déjà en cours pour l'essai {essai_id}")
            return False
        
        # Créer un nouvel thread d'acquisition
        acquisition_thread = threading.Thread(
            target=self._acquisition_task,
            args=(essai_id, type_courbe, interval, duration),
            daemon=True
        )
        
        # Stocker les informations d'acquisition
        self.acquisitions[essai_id] = {
            'thread': acquisition_thread,
            'start_time': datetime.now(),
            'type_courbe': type_courbe,
            'interval': interval,
            'duration': duration,
            'running': True,
            'points': 0
        }
        
        # Démarrer le thread
        acquisition_thread.start()
        return True
    
    def stop_acquisition(self, essai_id):
        """Arrête l'acquisition pour un essai spécifique"""
        if essai_id in self.acquisitions and self.acquisitions[essai_id]['running']:
            self.acquisitions[essai_id]['running'] = False
            return True
        return False
    
    def get_acquisition_status(self, essai_id=None):
        """
        Obtient le statut des acquisitions en cours
        
        Args:
            essai_id (int, optional): ID de l'essai spécifique ou None pour tous
        
        Returns:
            dict: Statut de l'acquisition ou des acquisitions
        """
        if essai_id is not None:
            if essai_id in self.acquisitions:
                acq = self.acquisitions[essai_id]
                return {
                    'essai_id': essai_id,
                    'running': acq['running'],
                    'start_time': acq['start_time'].isoformat(),
                    'elapsed': (datetime.now() - acq['start_time']).total_seconds(),
                    'type_courbe': acq['type_courbe'],
                    'interval': acq['interval'],
                    'points': acq['points']
                }
            return None
        
        # Retourner le statut de toutes les acquisitions
        statuses = {}
        for eid, acq in self.acquisitions.items():
            statuses[eid] = {
                'running': acq['running'],
                'start_time': acq['start_time'].isoformat(),
                'elapsed': (datetime.now() - acq['start_time']).total_seconds(),
                'type_courbe': acq['type_courbe'],
                'interval': acq['interval'],
                'points': acq['points']
            }
        return statuses
    
    def _acquisition_task(self, essai_id, type_courbe, interval, duration):
        """
        Tâche d'acquisition exécutée dans un thread séparé
        
        Args:
            essai_id (int): ID de l'essai associé
            type_courbe (str): Type de courbe ('CPA', 'CPB', 'Résultante')
            interval (float): Intervalle entre les acquisitions en secondes
            duration (int): Durée totale de l'acquisition en secondes
        """
        start_time = time.time()
        end_time = start_time + duration
        
        try:
            while time.time() < end_time and self.acquisitions[essai_id]['running']:
                # Récupérer les données des capteurs
                data = self.serial_comm.get_all_sensors()
                if data:
                    # Enregistrer les données dans la base de données
                    self._save_data_to_db(essai_id, type_courbe, data)
                    self.acquisitions[essai_id]['points'] += 1
                
                # Attendre l'intervalle spécifié
                time.sleep(interval)
            
            print(f"Acquisition terminée pour l'essai {essai_id}: {self.acquisitions[essai_id]['points']} points collectés")
            self.acquisitions[essai_id]['running'] = False
        
        except Exception as e:
            print(f"Erreur lors de l'acquisition pour l'essai {essai_id}: {e}")
            self.acquisitions[essai_id]['running'] = False
    
    def _save_data_to_db(self, essai_id, type_courbe, data):
        """
        Enregistre les données des capteurs dans la base de données
        
        Args:
            essai_id (int): ID de l'essai associé
            type_courbe (str): Type de courbe ('CPA', 'CPB', 'Résultante')
            data (dict): Données des capteurs à enregistrer
        
        Returns:
            bool: True si l'enregistrement a réussi, False sinon
        """
        connection = self.get_db_connection()
        if not connection:
            print("Impossible de se connecter à la base de données")
            return False
        
        try:
            with connection.cursor() as cursor:
                # Préparer les données JSON
                donnees_json = json.dumps({
                    'pressure': data['pressure'],
                    'pressure_pascal': data['pressure_pascal'],
                    'flow': data['flow'],
                    'flow_lpm': data['flow_lpm'],
                    'timestamp': data['timestamp'].isoformat()
                })
                
                # Insérer les données dans la table des courbes
                cursor.execute(
                    "INSERT INTO courbes (essai_id, type_courbe, donnees) VALUES (%s, %s, %s)",
                    (essai_id, type_courbe, donnees_json)
                )
                connection.commit()
                return True
                
        except Exception as e:
            print(f"Erreur lors de l'enregistrement des données: {e}")
            return False
        
        finally:
            connection.close()