<?php
/**
 * Script de vérification et correction des permissions
 * À exécuter pour s'assurer que tous les dossiers nécessaires sont accessibles
 */

session_start();

// Vérifier que l'utilisateur est connecté et est un contrôleur
if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'controleur') {
    die('Accès refusé. Ce script nécessite un utilisateur contrôleur connecté.');
}

echo "<h1>Vérification des Permissions</h1>";

// Dossiers critiques à vérifier
$criticalDirectories = [
    __DIR__ . '/../backups' => 'Dossier de sauvegarde',
    __DIR__ . '/../pdf_exports' => 'Dossier d\'export PDF',
    __DIR__ . '/../temp' => 'Dossier temporaire',
    __DIR__ . '/../temp/mpdf' => 'Dossier temporaire mPDF'
];

$allOk = true;

foreach ($criticalDirectories as $dir => $description) {
    echo "<h3>$description</h3>";
    echo "Chemin: <code>$dir</code><br>";

    // Vérifier si le dossier existe
    if (!is_dir($dir)) {
        echo "❌ Dossier inexistant. Tentative de création...<br>";
        if (mkdir($dir, 0755, true)) {
            echo "✅ Dossier créé avec succès.<br>";
        } else {
            echo "❌ Impossible de créer le dossier.<br>";
            $allOk = false;
            continue;
        }
    } else {
        echo "✅ Dossier existe.<br>";
    }

    // Vérifier les permissions de lecture
    if (is_readable($dir)) {
        echo "✅ Lecture: OK<br>";
    } else {
        echo "❌ Lecture: Impossible<br>";
        $allOk = false;
    }

    // Vérifier les permissions d'écriture
    if (is_writable($dir)) {
        echo "✅ Écriture: OK<br>";
    } else {
        echo "❌ Écriture: Impossible<br>";

        // Tentative de correction des permissions
        if (chmod($dir, 0755)) {
            echo "✅ Permissions corrigées.<br>";
            if (is_writable($dir)) {
                echo "✅ Écriture maintenant possible.<br>";
            } else {
                echo "❌ Écriture toujours impossible après correction.<br>";
                $allOk = false;
            }
        } else {
            echo "❌ Impossible de corriger les permissions.<br>";
            $allOk = false;
        }
    }

    // Test d'écriture réel
    $testFile = $dir . '/test_write_' . time() . '.tmp';
    if (file_put_contents($testFile, 'test') !== false) {
        echo "✅ Test d'écriture: OK<br>";
        unlink($testFile); // Nettoyer le fichier de test
    } else {
        echo "❌ Test d'écriture: Échec<br>";
        $allOk = false;
    }

    // Afficher les permissions actuelles
    $perms = fileperms($dir);
    $info = sprintf('%o', $perms & 0777);
    echo "Permissions actuelles: $info<br>";

    echo "<br>";
}

echo "<h2>Vérification des fichiers critiques</h2>";

$criticalFiles = [
    __DIR__ . '/../lib/pdf_generator.php' => 'Générateur PDF',
    __DIR__ . '/../lib/backup.php' => 'Système de sauvegarde',
    __DIR__ . '/../pdf_handler.php' => 'Gestionnaire PDF',
    __DIR__ . '/../backup.php' => 'Interface de sauvegarde'
];

foreach ($criticalFiles as $file => $description) {
    echo "<strong>$description:</strong> ";
    if (file_exists($file)) {
        if (is_readable($file)) {
            echo "✅ OK<br>";
        } else {
            echo "❌ Non lisible<br>";
            $allOk = false;
        }
    } else {
        echo "❌ Fichier manquant<br>";
        $allOk = false;
    }
}

echo "<h2>Test de mPDF</h2>";

// Tester mPDF
if (file_exists(__DIR__ . '/../vendor/autoload.php')) {
    require_once(__DIR__ . '/../vendor/autoload.php');
    echo "✅ Autoloader Composer chargé.<br>";

    if (class_exists('\Mpdf\Mpdf')) {
        echo "✅ Classe mPDF disponible.<br>";

        try {
            $tempDir = __DIR__ . '/../temp/mpdf';
            $mpdf = new \Mpdf\Mpdf([
                'tempDir' => $tempDir,
                'mode' => 'utf-8',
                'format' => 'A4'
            ]);
            echo "✅ Instance mPDF créée avec succès.<br>";
        } catch (Exception $e) {
            echo "❌ Erreur mPDF: " . $e->getMessage() . "<br>";
            $allOk = false;
        }
    } else {
        echo "❌ Classe mPDF non disponible.<br>";
        $allOk = false;
    }
} else {
    echo "❌ Autoloader Composer non trouvé.<br>";
    echo "Exécutez <a href='/tests/install_mpdf.php'>install_mpdf.php</a> pour installer mPDF.<br>";
    $allOk = false;
}

echo "<h2>Informations système</h2>";
echo "<ul>";
echo "<li>Version PHP: " . PHP_VERSION . "</li>";
echo "<li>Système d'exploitation: " . PHP_OS . "</li>";
echo "<li>Utilisateur web: " . get_current_user() . "</li>";
echo "<li>Dossier de travail: " . getcwd() . "</li>";
echo "<li>Umask: " . sprintf('%04o', umask()) . "</li>";
echo "</ul>";

echo "<h2>Résumé</h2>";
if ($allOk) {
    echo "<div style='color: green; font-weight: bold; font-size: 18px;'>";
    echo "✅ Tous les tests sont passés avec succès !<br>";
    echo "Le système est prêt pour la génération de PDF et les sauvegardes.";
    echo "</div>";
} else {
    echo "<div style='color: red; font-weight: bold; font-size: 18px;'>";
    echo "❌ Des problèmes ont été détectés.<br>";
    echo "Veuillez corriger les erreurs ci-dessus avant d'utiliser le système.";
    echo "</div>";

    echo "<h3>Actions recommandées:</h3>";
    echo "<ul>";
    echo "<li>Vérifier les permissions des dossiers avec votre administrateur système</li>";
    echo "<li>S'assurer que le serveur web a les droits d'écriture</li>";
    echo "<li>Installer mPDF via Composer si ce n'est pas fait</li>";
    echo "<li>Contacter le support technique si les problèmes persistent</li>";
    echo "</ul>";
}

echo "<p><a href='/tests.php'>← Retour aux Tests</a> | <a href='/tests/test_mpdf.php'>Tester mPDF</a> | <a href='/backup.php'>Gestion des sauvegardes</a> | <a href='/pv.php'>Gestion des PV</a></p>";
?>

<style>
    body {
        font-family: Arial, sans-serif;
        margin: 20px;
    }

    h1 {
        color: #2563eb;
    }

    h2 {
        color: #1e40af;
        border-bottom: 1px solid #e5e7eb;
        padding-bottom: 5px;
    }

    h3 {
        color: #374151;
    }

    code {
        background-color: #f3f4f6;
        padding: 2px 4px;
        border-radius: 3px;
    }

    ul {
        margin-left: 20px;
    }

    a {
        color: #2563eb;
        text-decoration: none;
    }

    a:hover {
        text-decoration: underline;
    }
</style>
