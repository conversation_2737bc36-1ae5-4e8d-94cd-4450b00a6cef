## Cahier des charges du Logiciel Qt

**1. Introduction**

Ce document décrit le cahier des charges pour le développement du logiciel Qt, qui fait partie du système d'informatisation du banc d'essai hydraulique. Ce logiciel sera déployé sur le poste Banc et sera responsable de la supervision, de l'acquisition et de la sauvegarde des données des essais.

**2. Objectifs**

L'objectif principal du logiciel Qt est d'interfacer le banc d'essai hydraulique, d'acquérir les données des capteurs en temps réel, de les visualiser et de les sauvegarder dans la base de données Essais via le réseau.

**3. Exigences Fonctionnelles**

Le logiciel Qt doit implémenter les fonctionnalités suivantes, conformément aux exigences identifiées dans le dossier technique :

*   **Récupération des paramètres de l'essai (F1):**
    *   Permettre à l'opérateur de récupérer le mode opératoire et de sélectionner un essai à partir du numéro d'affaire stocké dans la base de données Essais.
    *   Empêcher la réalisation de deux fois le même essai pour une affaire donnée.
    *   Identifier obligatoirement le manipulateur.
*   **Supervision statique de l'installation (F2):**
    *   Visualiser une représentation statique de l'installation (digitalisation, photos) et du schéma hydraulique sur une IHM Windows.
    *   Respecter les normes de représentation.
*   **Paramétrage des acquisitions (F3):**
    *   Permettre à l'opérateur de choisir la fréquence d'acquisition (1s, 100ms, 10ms) pour les pressions et le débit.
    *   Proposer par défaut les choix définis par le contrôleur.
    *   Permettre une modification de ces choix par l'opérateur.
*   **Départ des acquisitions (F4):**
    *   Attendre un signal (top départ) sur une entrée TOR de la carte d'E/S provenant de l'automate de pilotage du banc test pour démarrer les acquisitions.
*   **Tracé de courbes en direct (F5):**
    *   Visualiser en temps réel les acquisitions des capteurs avec des couleurs distinctes sur une page écran.
    *   Utiliser un mode d'affichage de courbe de type oscilloscope pour visualiser un maximum de points.
    *   Inclure une légende obligatoire.
    *   Graduer les axes en fonction de l'échelle et des unités S.I.
*   **Affichage du tracé de CPA, CPB, résultante, calcul des rendements (F6):**
    *   Après l'acquisition, permettre à l'opérateur de visualiser les courbes de pression d'entrée \( CPA \), pression de sortie \( CPB \), la résultante de \( CPA \) et \( CPB \).
    *   Permettre le calcul des rendements.
*   **Fin des acquisitions (F7):**
    *   Permettre à l'opérateur de valider la fin de l'essai.
    *   L'enregistrement des mesures dans la base de données Essais est déclenché par cette validation.
*   **Sauvegarde des mesures dans la BD Essais (F8):**
    *   Stocker les informations de l'acquisition dans la base de données sur le poste Contrôleur.
    *   Stocker les données brutes d'acquisition.
    *   Stocker les paramètres réels de l'essai.
    *   Utiliser un format standardisé pour les données.
    *   Exiger une confirmation obligatoire avant la sauvegarde.
    *   Bloquer l'essai dans l'affaire après sauvegarde.
*   **Annulation de l'essai (F4, F6):**
    *   Permettre d'annuler l'essai pendant la phase de départ des acquisitions (F4) ou après l'acquisition terminée (F6).
    *   En cas d'annulation, aucune sauvegarde ne doit être effectuée.

**4. Exigences Non Fonctionnelles**

*   **Sécurité:**
    *   Assurer la sécurité des données avant leur transfert vers la base de données Essais.
    *   Gérer l'identification de l'opérateur.
*   **Fiabilité:**
    *   Assurer une acquisition de données précise et fiable.
    *   Gérer les erreurs potentielles liées aux capteurs ou à la communication.
*   **Performance:**
    *   Assurer l'acquisition des données en temps réel avec les fréquences spécifiées.
    *   L'affichage des courbes en direct doit être fluide.
*   **Maniabilité:**
    *   L'interface utilisateur doit être intuitive et facile à utiliser pour l'opérateur du banc.
    *   Utiliser des fenêtres d'affichage et des boîtes de dialogue claires.
*   **Maintenabilité:**
    *   Le code source doit être structuré et documenté pour faciliter la localisation et la correction des erreurs.
    *   Permettre l'ajout ou le retrait de fonctionnalités avec un minimum d'impact sur le système existant.
*   **Compatibilité:**
    *   Le logiciel doit fonctionner sur un PC sous Windows.
    *   Utiliser l'environnement de développement Qt.
    *   Communiquer avec la carte d'entrées analogiques (potentiellement une carte Ethernet) et l'automate de pilotage du banc test (via une entrée TOR).
    *   Communiquer avec le serveur web (Poste Contrôleur) via Ethernet ou Wifi pour la sauvegarde des données.

**5. Architecture Technique**

*   Le logiciel sera développé en C++ en utilisant le framework Qt pour l'interface utilisateur et la gestion des évènements.
*   La communication avec la carte d'acquisition se fera via l'interface spécifiée (potentiellement Ethernet).
*   La communication avec le serveur web (Poste Contrôleur) pour la sauvegarde des données se fera via des requêtes REST.
*   La gestion du signal de départ d'acquisition se fera via l'entrée TOR de la carte d'E/S.

**6. Interfaces**

*   **Interface utilisateur:** IHM graphique développée avec Qt.
*   **Interface carte d'acquisition:** Communication avec la carte d'entrées analogiques pour l'acquisition des données (pressions, débit).
*   **Interface automate de pilotage:** Réception du signal de top départ d'acquisition.
*   **Interface réseau:** Communication via Ethernet ou Wifi avec le poste Contrôleur pour la sauvegarde des données (Requêtes REST).

**7. Données**

*   Le logiciel doit acquérir et traiter les données de pression (CPA, CPB) et de débit.
*   Les données d'acquisition brutes doivent être temporairement stockées avant d'être envoyées à la base de données.

**8. Contraintes**

*   Développement en C++ avec Qt.
*   Utilisation de l'environnement de développement Visual Studio Community ou Visual Studio Code avec Qt.
*   Utilisation du matériel existant au laboratoire BTS Ciel (PC Windows, banc de test simulé, carte d'acquisition à définir).
*   Budget limité pour les éventuelles commandes complémentaires (1000€).
*   Le banc de test sera simulé par un boîtier analogique à réaliser par un étudiant.

**9. Livrables**

*   Code source complet du logiciel Qt sur Github.
*   Documentation technique du logiciel Qt (architecture, code, procédures de maintenance).
*   Manuel utilisateur du logiciel Qt.
*   Scripts d'installation et de configuration.

**10. Glossaire**

*   **Qt:** Framework de développement d'applications graphiques.
*   **C++:** Langage de programmation.
*   **TOR:** Tout Ou Rien (signal binaire).
*   **E/S:** Entrées/Sorties.
*   **Requêtes REST:** Méthode de communication entre systèmes via le protocole HTTP.
*   **CPA:** Pression Cylindre Avant
*   **CPB:** Pression Cylindre Arrière