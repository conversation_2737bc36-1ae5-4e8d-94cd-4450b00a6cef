<?php
session_start();
require_once(__DIR__ . '/../lib/essais.php');
require_once(__DIR__ . '/../lib/affaires.php');
require_once(__DIR__ . '/../lib/courbes.php');
require_once(__DIR__ . '/../lib/rendement.php');
require_once(__DIR__ . '/../lib/performance.php');

if (!isset($_SESSION['user'])) {
    header('Location: /auth/login.php');
    exit;
}

if (!isset($_GET['id'])) {
    header('Location: /essais.php');
    exit;
}

$essai = Essai::getById($_GET['id']);
if (!$essai) {
    header('Location: /essais.php');
    exit;
}

/**
 * Formater les paramètres théoriques JSON en affichage lisible
 */
function formatParametresTheoriques($json_string)
{
    if (empty($json_string)) {
        return '<span class="text-gray-400">Non disponible</span>';
    }

    $parametres = json_decode($json_string, true);
    if (!$parametres || !is_array($parametres)) {
        return '<span class="text-gray-400">Format invalide</span>';
    }

    $formatted = '<div class="space-y-2">';

    // Mapping des clés vers des libellés plus lisibles
    $labels = [
        'pression_nominale' => 'Pression nominale',
        'debit_nominal' => 'Débit nominal',
        'temperature_fluide' => 'Température du fluide',
        'viscosite' => 'Viscosité',
        'puissance' => 'Puissance',
        'course' => 'Course',
        'diametre_piston' => 'Diamètre du piston',
        'diametre_tige' => 'Diamètre de la tige',
        'pression_max' => 'Pression maximale',
        'debit_max' => 'Débit maximal',
        'force_max' => 'Force maximale'
    ];

    foreach ($parametres as $key => $value) {
        $label = $labels[$key] ?? ucfirst(str_replace('_', ' ', $key));
        $formatted .= '<div class="flex justify-between items-center py-1">';
        $formatted .= '<span class="text-sm text-gray-600 dark:text-gray-400">' . htmlspecialchars($label) . ':</span>';
        $formatted .= '<span class="text-sm font-medium text-gray-900 dark:text-white">' . htmlspecialchars($value) . '</span>';
        $formatted .= '</div>';
    }

    $formatted .= '</div>';

    return $formatted;
}

/**
 * Convertir les paramètres JSON en format texte éditable
 */
function jsonToEditableText($json_string)
{
    if (empty($json_string)) {
        return '';
    }

    $parametres = json_decode($json_string, true);
    if (!$parametres || !is_array($parametres)) {
        return $json_string; // Retourner le JSON brut si invalide
    }

    $lines = [];
    foreach ($parametres as $key => $value) {
        $lines[] = $key . ': ' . $value;
    }

    return implode("\n", $lines);
}

/**
 * Convertir le texte éditable en JSON
 */
function editableTextToJson($text)
{
    if (empty(trim($text))) {
        return '{}';
    }

    $lines = explode("\n", trim($text));
    $parametres = [];

    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line)) continue;

        if (strpos($line, ':') !== false) {
            list($key, $value) = explode(':', $line, 2);
            $key = trim($key);
            $value = trim($value);
            if (!empty($key) && !empty($value)) {
                $parametres[$key] = $value;
            }
        }
    }

    return json_encode($parametres, JSON_UNESCAPED_UNICODE);
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'update':
                // Convertir le texte éditable en JSON
                $parametre_theorique_json = editableTextToJson($_POST['parametre_theorique']);

                if (Essai::update(
                    $_POST['id'],
                    $_POST['type'],
                    $parametre_theorique_json,
                    $_POST['statut'],
                    $_POST['resultat'] ?? null,
                    $_POST['mode_operatoire'] ?? null
                )) {
                    $success = "Essai mis à jour avec succès";
                    // Recharger l'essai pour afficher les nouvelles données
                    $essai = Essai::getById($_POST['id']);
                } else {
                    $error = "Erreur lors de la mise à jour de l'essai";
                }
                break;
            case 'delete':
                if (Essai::delete($_POST['id'])) {
                    $success = "Essai supprimé avec succès";
                } else {
                    $error = "Erreur lors de la suppression de l'essai";
                }
                break;
            case 'calculate_rendement':
                $resultat = Rendement::calculerRendement($_POST['essai_id'], $_SESSION['user']['id']);
                if ($resultat['success']) {
                    $success = "Rendement calculé avec succès";
                } else {
                    $error = $resultat['message'];
                }
                break;
        }
    }
}

// Récupérer le rendement existant
$rendement = Rendement::getByEssaiId($essai['id']);

ob_start();
?>

    <script src="https://cdn.jsdelivr.net/npm/apexcharts@3.46.0/dist/apexcharts.min.js"></script>

    <div class="p-4">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-2xl font-bold dark:text-white">Détail de l'essai
                #<?php echo htmlspecialchars($essai['id']); ?></h2>
            <div>
                <button data-modal-target="editEssaiModal<?php echo $essai['id']; ?>"
                        data-modal-toggle="editEssaiModal<?php echo $essai['id']; ?>"
                        class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">
                    Modifier
                </button>
                <button data-modal-target="deleteEssaiModal<?php echo $essai['id']; ?>"
                        data-modal-toggle="deleteEssaiModal<?php echo $essai['id']; ?>"
                        class="focus:outline-none text-white bg-red-700 hover:bg-red-800 focus:ring-4 focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-900">
                    Supprimer
                </button>
            </div>
        </div>

        <div class="grid md:grid-cols-2 gap-4">
            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
                <h3 class="text-lg font-semibold mb-4 dark:text-white">Informations générales</h3>
                <dl class="grid grid-cols-1 gap-4">
                    <div>
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Numéro d'affaire</dt>
                        <dd class="text-lg font-semibold text-gray-900 dark:text-white"><?php echo htmlspecialchars($essai['numero_affaire']); ?></dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Type d'essai</dt>
                        <dd class="text-lg font-semibold text-gray-900 dark:text-white"><?php echo htmlspecialchars($essai['type']); ?></dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Date d'essai</dt>
                        <dd class="text-lg font-semibold text-gray-900 dark:text-white"><?php echo htmlspecialchars($essai['date_essai']); ?></dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Statut</dt>
                        <dd>
                        <span class="text-sm font-medium px-2.5 py-0.5 rounded-md border
                            <?php
                        if ($essai['statut'] == 'Annulé') {
                            echo 'bg-red-100 text-red-800 dark:bg-gray-700 dark:text-red-400 border-red-100 dark:border-red-500';
                        } elseif ($essai['statut'] == 'Terminé') {
                            echo 'bg-green-100 text-green-800 dark:bg-gray-700 dark:text-green-400 border-green-100 dark:border-green-500';
                        } elseif ($essai['statut'] == 'En cours') {
                            echo 'bg-blue-100 text-blue-800 dark:bg-gray-700 dark:text-blue-400 border-blue-100 dark:border-blue-500';
                        } elseif ($essai['statut'] == 'En attente') {
                            echo 'bg-yellow-100 text-yellow-800 dark:bg-gray-700 dark:text-yellow-400 border-yellow-100 dark:border-yellow-500';
                        }
                        ?>">
                            <?php echo htmlspecialchars($essai['statut']); ?>
                        </span>
                        </dd>
                    </div>
                </dl>
            </div>

            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
                <h3 class="text-lg font-semibold mb-4 dark:text-white">Paramètres et Résultats</h3>
                <dl class="grid grid-cols-1 gap-4">
                    <div>
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Paramètres théoriques</dt>
                        <dd class="mt-2"><?php echo formatParametresTheoriques($essai['parametre_theorique']); ?></dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Résultat</dt>
                        <dd class="text-lg font-semibold text-gray-900 dark:text-white"><?php echo $essai['resultat'] ? htmlspecialchars($essai['resultat']) : '<span class="text-gray-400">Non disponible</span>'; ?></dd>
                    </div>
                </dl>
            </div>

            <!-- Section Rendement -->
            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow md:col-span-2">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold dark:text-white">Calcul de Rendement (F15)</h3>
                    <?php if ($essai['statut'] === 'Terminé' && Courbe::hasAllCourbes($essai['id'])): ?>
                        <form method="POST" class="inline">
                            <input type="hidden" name="action" value="calculate_rendement">
                            <input type="hidden" name="essai_id" value="<?php echo $essai['id']; ?>">
                            <button type="submit"
                                    class="text-white bg-green-700 hover:bg-green-800 focus:ring-4 focus:ring-green-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-green-600 dark:hover:bg-green-700 focus:outline-none dark:focus:ring-green-800">
                                <?php echo $rendement ? 'Recalculer' : 'Calculer'; ?> Rendement
                            </button>
                        </form>
                    <?php endif; ?>
                </div>

                <?php if ($rendement): ?>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                        <div class="bg-gradient-to-r from-blue-500 to-blue-600 p-4 rounded-lg text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-blue-100 text-sm">Rendement Global</p>
                                    <p class="text-2xl font-bold"><?php echo number_format($rendement['rendement_global'], 2); ?>
                                        %</p>
                                </div>
                                <div class="text-blue-200">
                                    <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <div class="bg-gradient-to-r from-green-500 to-green-600 p-4 rounded-lg text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-green-100 text-sm">Rendement Volumétrique</p>
                                    <p class="text-2xl font-bold"><?php echo number_format($rendement['rendement_volumetrique'], 2); ?>
                                        %</p>
                                </div>
                                <div class="text-green-200">
                                    <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <div class="bg-gradient-to-r from-purple-500 to-purple-600 p-4 rounded-lg text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-purple-100 text-sm">Rendement Mécanique</p>
                                    <p class="text-2xl font-bold"><?php echo number_format($rendement['rendement_mecanique'], 2); ?>
                                        %</p>
                                </div>
                                <div class="text-purple-200">
                                    <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h4 class="font-semibold text-gray-900 dark:text-white mb-3">Données de Calcul</h4>
                            <dl class="space-y-2">
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600 dark:text-gray-400">Pression moyenne CPA:</dt>
                                    <dd class="text-sm font-medium text-gray-900 dark:text-white"><?php echo number_format($rendement['pression_moyenne_cpa'], 2); ?>
                                        Pa
                                    </dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600 dark:text-gray-400">Pression moyenne CPB:</dt>
                                    <dd class="text-sm font-medium text-gray-900 dark:text-white"><?php echo number_format($rendement['pression_moyenne_cpb'], 2); ?>
                                        Pa
                                    </dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600 dark:text-gray-400">Débit moyen:</dt>
                                    <dd class="text-sm font-medium text-gray-900 dark:text-white"><?php echo number_format($rendement['debit_moyen'], 2); ?>
                                        L/min
                                    </dd>
                                </div>
                            </dl>
                        </div>

                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h4 class="font-semibold text-gray-900 dark:text-white mb-3">Puissances</h4>
                            <dl class="space-y-2">
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600 dark:text-gray-400">Puissance hydraulique:</dt>
                                    <dd class="text-sm font-medium text-gray-900 dark:text-white"><?php echo number_format($rendement['puissance_hydraulique'], 2); ?>
                                        W
                                    </dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600 dark:text-gray-400">Puissance mécanique:</dt>
                                    <dd class="text-sm font-medium text-gray-900 dark:text-white"><?php echo number_format($rendement['puissance_mecanique'], 2); ?>
                                        W
                                    </dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600 dark:text-gray-400">Calculé le:</dt>
                                    <dd class="text-sm font-medium text-gray-900 dark:text-white"><?php echo date('d/m/Y H:i', strtotime($rendement['date_calcul'])); ?></dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600 dark:text-gray-400">Par:</dt>
                                    <dd class="text-sm font-medium text-gray-900 dark:text-white"><?php echo htmlspecialchars($rendement['calcule_par_nom'] ?? 'Inconnu'); ?></dd>
                                </div>
                            </dl>
                        </div>
                    </div>

                    <!-- Section Détails des Calculs -->
                    <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg mt-6">
                        <h4 class="font-semibold text-blue-900 dark:text-blue-100 mb-4 flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            Détails des Calculs Effectués
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="space-y-3">
                                <div class="bg-white dark:bg-gray-800 p-3 rounded border-l-4 border-blue-500">
                                    <h5 class="font-medium text-gray-900 dark:text-white mb-2">Puissance Hydraulique</h5>
                                    <div class="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                                        <div>P<sub>hydraulique</sub> = ΔP × Q</div>
                                        <div>P<sub>hydraulique</sub> = |<?php echo number_format($rendement['pression_moyenne_cpa'], 0); ?> - <?php echo number_format($rendement['pression_moyenne_cpb'], 0); ?>| Pa × <?php echo number_format($rendement['debit_moyen'] / 60000, 6); ?> m³/s</div>
                                        <div class="font-medium text-blue-600 dark:text-blue-400">P<sub>hydraulique</sub> = <?php echo number_format($rendement['puissance_hydraulique'], 2); ?> W</div>
                                    </div>
                                </div>

                                <div class="bg-white dark:bg-gray-800 p-3 rounded border-l-4 border-green-500">
                                    <h5 class="font-medium text-gray-900 dark:text-white mb-2">Rendement Volumétrique</h5>
                                    <div class="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                                        <div>η<sub>v</sub> = (Q<sub>réel</sub> / Q<sub>théorique</sub>) × 100</div>
                                        <div>η<sub>v</sub> = (<?php echo number_format($rendement['debit_moyen'], 2); ?> / <?php echo number_format($rendement['debit_moyen'] * 1.1, 2); ?>) × 100</div>
                                        <div class="font-medium text-green-600 dark:text-green-400">η<sub>v</sub> = <?php echo number_format($rendement['rendement_volumetrique'], 2); ?>%</div>
                                    </div>
                                </div>
                            </div>

                            <div class="space-y-3">
                                <div class="bg-white dark:bg-gray-800 p-3 rounded border-l-4 border-purple-500">
                                    <h5 class="font-medium text-gray-900 dark:text-white mb-2">Rendement Mécanique</h5>
                                    <div class="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                                        <div>η<sub>m</sub> = (P<sub>hydraulique</sub> / P<sub>mécanique</sub>) × 100</div>
                                        <div>η<sub>m</sub> = (<?php echo number_format($rendement['puissance_hydraulique'], 2); ?> / <?php echo number_format($rendement['puissance_mecanique'], 2); ?>) × 100</div>
                                        <div class="font-medium text-purple-600 dark:text-purple-400">η<sub>m</sub> = <?php echo number_format($rendement['rendement_mecanique'], 2); ?>%</div>
                                    </div>
                                </div>

                                <div class="bg-white dark:bg-gray-800 p-3 rounded border-l-4 border-blue-500">
                                    <h5 class="font-medium text-gray-900 dark:text-white mb-2">Rendement Global</h5>
                                    <div class="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                                        <div>η<sub>g</sub> = (η<sub>v</sub> × η<sub>m</sub>) / 100</div>
                                        <div>η<sub>g</sub> = (<?php echo number_format($rendement['rendement_volumetrique'], 2); ?> × <?php echo number_format($rendement['rendement_mecanique'], 2); ?>) / 100</div>
                                        <div class="font-medium text-blue-600 dark:text-blue-400">η<sub>g</sub> = <?php echo number_format($rendement['rendement_global'], 2); ?>%</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Section Guide d'Interprétation -->
                    <div class="bg-amber-50 dark:bg-amber-900/20 p-4 rounded-lg mt-4">
                        <h4 class="font-semibold text-amber-900 dark:text-amber-100 mb-4 flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            Guide d'Interprétation des Résultats
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg">
                                <h5 class="font-medium text-green-600 dark:text-green-400 mb-2">Rendement Volumétrique</h5>
                                <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                                    Mesure l'efficacité du déplacement de fluide par rapport au débit théorique.
                                    Il indique les pertes par fuites internes.
                                </p>
                                <div class="text-xs space-y-1">
                                    <div class="flex justify-between">
                                        <span class="text-green-600">Excellent:</span>
                                        <span class="font-medium">&gt; 95%</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-blue-600">Bon:</span>
                                        <span class="font-medium">90-95%</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-yellow-600">Acceptable:</span>
                                        <span class="font-medium">80-90%</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-red-600">Faible:</span>
                                        <span class="font-medium">&lt; 80%</span>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg">
                                <h5 class="font-medium text-purple-600 dark:text-purple-400 mb-2">Rendement Mécanique</h5>
                                <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                                    Mesure l'efficacité de conversion de l'énergie mécanique en énergie hydraulique.
                                    Il indique les pertes par frottements mécaniques.
                                </p>
                                <div class="text-xs space-y-1">
                                    <div class="flex justify-between">
                                        <span class="text-green-600">Excellent:</span>
                                        <span class="font-medium">&gt; 90%</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-blue-600">Bon:</span>
                                        <span class="font-medium">85-90%</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-yellow-600">Acceptable:</span>
                                        <span class="font-medium">75-85%</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-red-600">Faible:</span>
                                        <span class="font-medium">&lt; 75%</span>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg">
                                <h5 class="font-medium text-blue-600 dark:text-blue-400 mb-2">Rendement Global</h5>
                                <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                                    Performance globale du système hydraulique. C'est le produit des rendements
                                    volumétrique et mécanique.
                                </p>
                                <div class="text-xs space-y-1">
                                    <div class="flex justify-between">
                                        <span class="text-green-600">Excellent:</span>
                                        <span class="font-medium">&gt; 85%</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-blue-600">Bon:</span>
                                        <span class="font-medium">75-85%</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-yellow-600">Acceptable:</span>
                                        <span class="font-medium">65-75%</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-red-600">Faible:</span>
                                        <span class="font-medium">&lt; 65%</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4 p-3 bg-blue-100 dark:bg-blue-800/30 rounded-lg">
                            <p class="text-sm text-blue-800 dark:text-blue-200">
                                <strong>Note importante :</strong> Ces calculs sont basés sur les courbes de pression CPA et CPB mesurées pendant l'essai.
                                Les valeurs de rendement permettent d'évaluer l'état et les performances du système hydraulique testé.
                            </p>
                        </div>
                    </div>
                <?php elseif ($essai['statut'] === 'Terminé'): ?>
                    <?php if (Courbe::hasAllCourbes($essai['id'])): ?>
                        <div class="text-center py-8">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24"
                                 stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"/>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Aucun rendement
                                calculé</h3>
                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Cliquez sur "Calculer Rendement"
                                pour effectuer le calcul.</p>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-8">
                            <svg class="mx-auto h-12 w-12 text-yellow-400" fill="none" viewBox="0 0 24 24"
                                 stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Courbes incomplètes</h3>
                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Les courbes CPA et CPB sont
                                requises pour calculer le rendement.</p>
                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <div class="text-center py-8">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24"
                             stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Essai en cours</h3>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Le calcul de rendement sera disponible
                            une fois l'essai terminé.</p>
                    </div>
                <?php endif; ?>
            </div>

            <?php if ($essai['mode_operatoire']): ?>
                <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow md:col-span-2">
                    <h3 class="text-lg font-semibold mb-4 dark:text-white">Mode opératoire</h3>
                    <div class="prose dark:prose-invert">
                        <p><?php echo htmlspecialchars($essai['mode_operatoire']); ?></p>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Détails supplémentaires sur le mode
                            opératoire peuvent être ajoutés ici.</p>
                    </div>
                </div>
            <?php endif; ?>

            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow md:col-span-2">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold dark:text-white">Courbes</h3>
                    <div class="flex gap-2">
                        <button id="toggleChartOptimization"
                                class="text-sm px-3 py-1 bg-gray-600 hover:bg-gray-700 text-white rounded-lg">
                            Mode Optimisé
                        </button>
                    </div>
                </div>
                <div class="grid grid-cols-1 gap-4">
                    <?php
                    // Utiliser la classe Performance pour optimiser les données avec fallback
                    try {
                        $courbesOptimisees = Performance::getCourbesOptimized($essai['id'], null, 500);
                        $useOptimization = true;
                    } catch (Exception $e) {
                        // Fallback vers la méthode standard si l'optimisation échoue
                        $courbesOptimisees = Courbe::getByEssaiId($essai['id']);
                        $useOptimization = false;
                    }

                    $courbeGroupes = [];

                    foreach ($courbesOptimisees as $courbe) {
                        $type = $courbe['type_courbe'];
                        if (!isset($courbeGroupes[$type])) {
                            $courbeGroupes[$type] = [
                                'donnees' => [],
                                'timestamps' => [],
                                'points_originaux' => $useOptimization ? ($courbe['points_originaux'] ?? 0) : 0,
                                'points_optimises' => $useOptimization ? ($courbe['points_optimises'] ?? 0) : 0
                            ];
                        }

                        // Vérifier si les données sont dans le format JSON (nouvelles données cohérentes)
                        if (!empty($courbe['donnees'])) {
                            $donneesJson = json_decode($courbe['donnees'], true);
                            if (is_array($donneesJson) && !empty($donneesJson)) {
                                // Utiliser les données JSON détaillées (nouveau format cohérent)
                                foreach ($donneesJson as $point) {
                                    if (isset($point['timestamp']) && isset($point['pressure_bar'])) {
                                        $courbeGroupes[$type]['timestamps'][] = $point['timestamp'];
                                        $courbeGroupes[$type]['donnees'][] = floatval($point['pressure_bar']);
                                    }
                                }
                                // Mettre à jour le nombre de points si pas déjà défini
                                if ($courbeGroupes[$type]['points_originaux'] == 0) {
                                    $courbeGroupes[$type]['points_originaux'] = count($donneesJson);
                                    $courbeGroupes[$type]['points_optimises'] = count($donneesJson);
                                }
                            } else {
                                // Fallback vers la mesure unique si JSON invalide
                                $courbeGroupes[$type]['timestamps'][] = $courbe['TIMESTAMP'];
                                $courbeGroupes[$type]['donnees'][] = floatval($courbe['mesure']);
                            }
                        } else {
                            // Format ancien - utiliser la mesure unique
                            $courbeGroupes[$type]['timestamps'][] = $courbe['TIMESTAMP'];
                            $courbeGroupes[$type]['donnees'][] = floatval($courbe['mesure']);
                        }
                    }
                    $courbes = $courbeGroupes;

                    if ($courbes): ?>
                        <?php foreach ($courbes as $type => $courbeGroup): ?>
                        <div class="w-full bg-white rounded-lg shadow-sm dark:bg-gray-800 p-4 md:p-6 overflow-hidden">
                            <div class="flex justify-between mb-5">
                                <div class="grid gap-4 grid-cols-3">
                                    <div>
                                        <h5 class="inline-flex items-center text-gray-500 dark:text-gray-400 leading-none font-normal mb-2">
                                            Mesure</h5>
                                        <p class="text-gray-900 dark:text-white text-2xl leading-none font-bold"><?php echo htmlspecialchars($type); ?></p>
                                    </div>
                                    <div>
                                        <h5 class="inline-flex items-center text-gray-500 dark:text-gray-400 leading-none font-normal mb-2">
                                            Points affichés</h5>
                                        <p class="text-gray-900 dark:text-white text-2xl leading-none font-bold"><?php echo count($courbeGroup['donnees']); ?></p>
                                        <?php if (count($courbeGroup['donnees']) > 1): ?>
                                            <p class="text-xs text-green-600 dark:text-green-400">🔬 Données
                                                cohérentes</p>
                                        <?php endif; ?>
                                    </div>
                                    <?php if ($useOptimization && isset($courbeGroup['points_originaux']) && $courbeGroup['points_originaux'] > 0): ?>
                                        <div>
                                            <h5 class="inline-flex items-center text-gray-500 dark:text-gray-400 leading-none font-normal mb-2">
                                                Optimisation</h5>
                                            <p class="text-green-600 dark:text-green-400 text-sm leading-none font-medium">
                                                <?php echo round(($courbeGroup['points_optimises'] / $courbeGroup['points_originaux']) * 100, 1); ?>
                                                % des données
                                            </p>
                                        </div>
                                    <?php elseif (!$useOptimization): ?>
                                        <div>
                                            <h5 class="inline-flex items-center text-gray-500 dark:text-gray-400 leading-none font-normal mb-2">
                                                Mode</h5>
                                            <p class="text-blue-600 dark:text-blue-400 text-sm leading-none font-medium">
                                                Données complètes
                                            </p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="pt-5 flex flex-col gap-2">
                                    <a href="/detail/courbe.php?essai=<?php echo $essai['id']; ?>&type=<?php echo urlencode($type); ?>"
                                       class="px-4 py-2 text-sm font-medium text-white inline-flex items-center bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 rounded-lg text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                                        <svg class="w-3.5 h-3.5 text-white me-2" aria-hidden="true"
                                             xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 20">
                                            <path d="M14.066 0H7v5a2 2 0 0 1-2 2H0v11a1.97 1.97 0 0 0 1.934 2h12.132A1.97 1.97 0 0 0 16 18V2a1.97 1.97 0 0 0-1.934-2Zm-3 15H4.828a1 1 0 0 1 0-2h6.238a1 1 0 0 1 0 2Zm0-4H4.828a1 1 0 0 1 0-2h6.238a1 1 0 1 1 0 2Z"/>
                                            <path d="M5 5V.13a2.96 2.96 0 0 0-1.293.749L.879 3.707A2.98 2.98 0 0 0 .13 5H5Z"/>
                                        </svg>
                                        Détails
                                    </a>
                                    <button onclick="exportCourbeData('<?php echo $type; ?>', <?php echo $essai['id']; ?>)"
                                            class="px-4 py-2 text-sm font-medium text-white inline-flex items-center bg-purple-700 hover:bg-purple-800 focus:ring-4 focus:outline-none focus:ring-purple-300 rounded-lg text-center">
                                        <svg class="w-3.5 h-3.5 text-white me-2" fill="currentColor"
                                             viewBox="0 0 20 20">
                                            <path d="M13 8V2H7v6H2l8 8 8-8h-5zM0 18h20v2H0v-2z"/>
                                        </svg>
                                        CSV
                                    </button>
                                </div>
                            </div>
                            <div id="chart_<?php echo $type . '_' . $essai['id']; ?>" class="w-full h-96"></div>
                        </div>
                        <script>
                            var options = {
                                chart: {
                                    type: 'line',
                                    height: 384, // 96 * 4 = 384px
                                    maxWidth: "100%",
                                    fontFamily: "Inter, sans-serif",
                                    dropShadow: {
                                        enabled: false,
                                    },
                                    toolbar: {
                                        show: false,
                                    },
                                    zoom: {
                                        enabled: false
                                    }
                                },
                                tooltip: {
                                    enabled: true,
                                    x: {
                                        show: false,
                                    },
                                },
                                dataLabels: {
                                    enabled: false,
                                },
                                stroke: {
                                    width: 6,
                                    curve: 'smooth'
                                },
                                grid: {
                                    show: true,
                                    strokeDashArray: 4,
                                    padding: {
                                        left: 2,
                                        right: 2,
                                        top: -26
                                    },
                                },
                                series: [{
                                    name: '<?php echo htmlspecialchars($type); ?>',
                                    data: <?php echo json_encode($courbeGroup['donnees']); ?>,
                                    color: "#1A56DB"
                                }],
                                xaxis: {
                                    categories: <?php echo json_encode($courbeGroup['timestamps']); ?>,
                                    title: {
                                        text: 'Temps'
                                    },
                                    labels: {
                                        show: true,
                                        style: {
                                            fontFamily: "Inter, sans-serif",
                                            cssClass: 'text-xs font-normal fill-gray-500 dark:fill-gray-400'
                                        }
                                    },
                                    axisBorder: {
                                        show: false,
                                    },
                                    axisTicks: {
                                        show: false,
                                    },
                                },
                                yaxis: {
                                    title: {
                                        text: 'Mesure'
                                    },
                                    show: false,
                                },
                                legend: {
                                    show: false
                                }
                            };

                            var chart = new ApexCharts(document.querySelector("#chart_<?php echo $type . '_' . $essai['id']; ?>"), options);
                            chart.render();
                        </script>
                    <?php endforeach; ?>
                    <?php else: ?>
                        <p class="text-gray-500 dark:text-gray-400">Aucune courbe disponible pour cet essai.</p>
                    <?php endif; ?>
                </div>
            </div>

            <?php if (isset($_GET['compare'])): ?>
                <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow md:col-span-2">
                    <h3 class="text-lg font-semibold mb-4 dark:text-white">Comparaison des essais</h3>
                    <div class="grid grid-cols-2 gap-4">
                        <?php
                        $essaiComparaison = Essai::getById($_GET['compare']);
                        if ($essaiComparaison):
                            $courbesComparaison = Courbe::getByEssaiId($essaiComparaison['id']);
                            $courbesGroupesComparaison = [];

                            foreach ($courbesComparaison as $courbe) {
                                $type = $courbe['type_courbe'];
                                if (!isset($courbesGroupesComparaison[$type])) {
                                    $courbesGroupesComparaison[$type] = ['donnees' => [], 'timestamps' => []];
                                }

                                // Vérifier si les données sont dans le format JSON (nouvelles données cohérentes)
                                if (!empty($courbe['donnees'])) {
                                    $donneesJson = json_decode($courbe['donnees'], true);
                                    if (is_array($donneesJson) && !empty($donneesJson)) {
                                        // Utiliser les données JSON détaillées (nouveau format cohérent)
                                        foreach ($donneesJson as $point) {
                                            if (isset($point['timestamp']) && isset($point['pressure_bar'])) {
                                                $courbesGroupesComparaison[$type]['timestamps'][] = $point['timestamp'];
                                                $courbesGroupesComparaison[$type]['donnees'][] = floatval($point['pressure_bar']);
                                            }
                                        }
                                    } else {
                                        // Fallback vers la mesure unique si JSON invalide
                                        $courbesGroupesComparaison[$type]['timestamps'][] = $courbe['TIMESTAMP'];
                                        $courbesGroupesComparaison[$type]['donnees'][] = floatval($courbe['mesure']);
                                    }
                                } else {
                                    // Format ancien - utiliser la mesure unique
                                    $courbesGroupesComparaison[$type]['timestamps'][] = $courbe['TIMESTAMP'];
                                    $courbesGroupesComparaison[$type]['donnees'][] = floatval($courbe['mesure']);
                                }
                            }

                            foreach ($courbes as $type => $courbeGroup):
                                if (isset($courbesGroupesComparaison[$type])):
                                    ?>
                                    <div class="w-full bg-white rounded-lg shadow-sm dark:bg-gray-800 p-4 md:p-6">
                                        <div class="flex justify-between mb-5">
                                            <div>
                                                <h5 class="text-xl font-bold leading-none text-gray-900 dark:text-white pe-1">
                                                    Comparaison - <?php echo htmlspecialchars($type); ?></h5>
                                            </div>
                                        </div>
                                        <div id="compare_chart_<?php echo $type; ?>" class="w-full h-96"></div>
                                        <script>
                                            var compareOptions = {
                                                chart: {
                                                    type: 'line',
                                                    height: 384,
                                                    maxWidth: "100%",
                                                    fontFamily: "Inter, sans-serif",
                                                    zoom: {
                                                        enabled: true
                                                    }
                                                },
                                                dataLabels: {
                                                    enabled: false
                                                },
                                                stroke: {
                                                    curve: 'smooth',
                                                    width: [2, 2],
                                                    dashArray: [0, 5]
                                                },
                                                grid: {
                                                    borderColor: '#e7e7e7'
                                                },
                                                series: [{
                                                    name: 'Essai actuel',
                                                    data: <?php echo json_encode($courbeGroup['donnees']); ?>
                                                },
                                                    {
                                                        name: 'Essai comparé',
                                                        data: <?php echo json_encode($courbesGroupesComparaison[$type]['donnees']); ?>
                                                    }
                                                ],
                                                xaxis: {
                                                    categories: <?php echo json_encode($courbeGroup['timestamps']); ?>,
                                                    title: {
                                                        text: 'Temps'
                                                    }
                                                },
                                                yaxis: {
                                                    title: {
                                                        text: 'Mesure'
                                                    }
                                                },
                                                legend: {
                                                    position: 'top',
                                                    horizontalAlign: 'right',
                                                    floating: true,
                                                    offsetY: -25,
                                                    offsetX: -5
                                                }
                                            };

                                            new ApexCharts(document.querySelector("#compare_chart_<?php echo $type; ?>"), compareOptions).render();
                                        </script>
                                    </div>
                                <?php
                                endif;
                            endforeach;
                        endif;
                        ?>
                    </div>
                </div>
            <?php endif; ?>

            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow md:col-span-2">
                <h3 class="text-lg font-semibold mb-4 dark:text-white">Essais similaires</h3>
                <?php
                $essaisSimilaires = Essai::getByType($essai['type']);
                $essaisSimilaires = array_filter($essaisSimilaires, function ($e) use ($essai) {
                    return $e['id'] != $essai['id'];
                });
                if ($essaisSimilaires): ?>
                    <div class="relative overflow-x-auto">
                        <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
                            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                            <tr>
                                <th scope="col" class="px-6 py-3">ID</th>
                                <th scope="col" class="px-6 py-3">Date</th>
                                <th scope="col" class="px-6 py-3">Affaire</th>
                                <th scope="col" class="px-6 py-3">Statut</th>
                                <th scope="col" class="px-6 py-3">Actions</th>
                            </tr>
                            </thead>
                            <tbody>
                            <?php foreach ($essaisSimilaires as $essaiSim):
                                if ($essaiSim['id'] != $essai['id']): ?>
                                    <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                                        <th scope="row"
                                            class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                            <?php echo htmlspecialchars($essaiSim['id']); ?>
                                        </th>
                                        <td class="px-6 py-4"><?php echo htmlspecialchars($essaiSim['date_essai']); ?></td>
                                        <td class="px-6 py-4"><?php echo htmlspecialchars($essaiSim['numero_affaire']); ?></td>
                                        <td class="px-6 py-4">
                                            <span class="bg-blue-100 text-blue-800 text-xs font-medium me-2 px-2.5 py-0.5 rounded dark:bg-blue-900 dark:text-blue-300">
                                                <?php echo htmlspecialchars($essaiSim['statut']); ?>
                                            </span>
                                        </td>
                                        <td class="px-6 py-4">
                                            <a href="?id=<?php echo $essai['id']; ?>&compare=<?php echo $essaiSim['id']; ?>"
                                               class="font-medium text-blue-600 dark:text-blue-500 hover:underline">Comparer</a>
                                        </td>
                                    </tr>
                                <?php
                                endif;
                            endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <p class="text-gray-500 dark:text-gray-400">Aucun essai similaire trouvé.</p>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Modal de modification -->
    <div id="editEssaiModal<?php echo $essai['id']; ?>" tabindex="-1" aria-hidden="true"
         class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
        <div class="relative p-4 w-full max-w-md max-h-full">
            <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                        Modifier l'essai
                    </h3>
                    <button type="button"
                            class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                            data-modal-hide="editEssaiModal<?php echo $essai['id']; ?>">
                        <svg class="w-3 h-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                        </svg>
                        <span class="sr-only">Fermer</span>
                    </button>
                </div>
                <form class="p-4 md:p-5" method="POST">
                    <input type="hidden" name="action" value="update">
                    <input type="hidden" name="id" value="<?php echo $essai['id']; ?>">
                    <div class="grid gap-4 mb-4">
                        <div>
                            <label for="type" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Type
                                d'essai</label>
                            <input type="text" name="type" id="type"
                                   value="<?php echo htmlspecialchars($essai['type']); ?>"
                                   class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                   required>
                        </div>
                        <div>
                            <label for="parametre_theorique"
                                   class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Paramètres
                                théoriques</label>
                            <textarea name="parametre_theorique" id="parametre_theorique" rows="6"
                                      placeholder="Format: clé: valeur (une par ligne)&#10;Exemple:&#10;pression_nominale: 250 bar&#10;debit_nominal: 50 L/min&#10;temperature_fluide: 60 °C"
                                      class="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                      required><?php echo htmlspecialchars(jsonToEditableText($essai['parametre_theorique'])); ?></textarea>
                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                                Entrez chaque paramètre sur une ligne séparée au format "nom: valeur"
                            </p>
                        </div>
                        <div>
                            <label for="statut" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Statut</label>
                            <select name="statut" id="statut"
                                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                    required>
                                <option value="En attente" <?php echo $essai['statut'] == 'En attente' ? 'selected' : ''; ?>>
                                    En attente
                                </option>
                                <option value="En cours" <?php echo $essai['statut'] == 'En cours' ? 'selected' : ''; ?>>
                                    En cours
                                </option>
                                <option value="Terminé" <?php echo $essai['statut'] == 'Terminé' ? 'selected' : ''; ?>>
                                    Terminé
                                </option>
                                <option value="Annulé" <?php echo $essai['statut'] == 'Annulé' ? 'selected' : ''; ?>>
                                    Annulé
                                </option>
                            </select>
                        </div>
                        <div>
                            <label for="resultat" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Résultat</label>
                            <textarea name="resultat" id="resultat" rows="4"
                                      class="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"><?php echo htmlspecialchars($essai['resultat'] ?? ''); ?></textarea>
                        </div>
                        <div>
                            <label for="mode_operatoire"
                                   class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Mode
                                opératoire</label>
                            <textarea name="mode_operatoire" id="mode_operatoire" rows="4"
                                      class="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"><?php echo htmlspecialchars($essai['mode_operatoire'] ?? ''); ?></textarea>
                        </div>
                    </div>
                    <button type="submit"
                            class="text-white inline-flex items-center bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                        Enregistrer les modifications
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal de suppression -->
    <div id="deleteEssaiModal<?php echo $essai['id']; ?>" tabindex="-1" aria-hidden="true"
         class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
        <div class="relative p-4 w-full max-w-md max-h-full">
            <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                        Confirmer la suppression
                    </h3>
                    <button type="button"
                            class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                            data-modal-hide="deleteEssaiModal<?php echo $essai['id']; ?>">
                        <svg class="w-3 h-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                        </svg>
                        <span class="sr-only">Fermer</span>
                    </button>
                </div>
                <div class="p-4 md:p-5">
                    <p class="text-gray-500 dark:text-gray-300">Êtes-vous sûr de vouloir supprimer cet essai ? Cette
                        action est irréversible.</p>
                    <form method="POST" class="mt-5">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="id" value="<?php echo $essai['id']; ?>">
                        <button type="submit"
                                class="text-white bg-red-600 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 dark:focus:ring-red-800 font-medium rounded-lg text-sm inline-flex items-center px-5 py-2.5 text-center me-2">
                            Oui, supprimer
                        </button>
                        <button type="button" data-modal-hide="deleteEssaiModal<?php echo $essai['id']; ?>"
                                class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900 focus:z-10 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-500 dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-gray-600">
                            Non, annuler
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Fonction d'export CSV pour les courbes
        function exportCourbeData(typeCourbe, essaiId) {
            // Faire une requête AJAX pour récupérer toutes les données
            fetch(`/api/index.php?controller=courbe&essai_id=${essaiId}&type=${typeCourbe}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (!Array.isArray(data)) {
                        throw new Error('Format de données invalide');
                    }

                    let csv = 'Timestamp,Pression_Bar,Debit_LPM,Temperature_C,Sequence\n';
                    data.forEach(row => {
                        // Vérifier si les données sont dans le nouveau format JSON
                        if (row.donnees) {
                            try {
                                const donneesJson = JSON.parse(row.donnees);
                                if (Array.isArray(donneesJson)) {
                                    donneesJson.forEach(point => {
                                        csv += `${point.timestamp || row.TIMESTAMP},${point.pressure_bar || 0},${point.flow_lpm || 0},${point.temperature || 0},${point.sequence || 1}\n`;
                                    });
                                } else {
                                    // Fallback vers l'ancien format
                                    csv += `${row.TIMESTAMP},${row.mesure || 0},0,0,1\n`;
                                }
                            } catch (e) {
                                // Fallback vers l'ancien format si JSON invalide
                                csv += `${row.TIMESTAMP},${row.mesure || 0},0,0,1\n`;
                            }
                        } else {
                            // Ancien format
                            csv += `${row.TIMESTAMP},${row.mesure || 0},0,0,1\n`;
                        }
                    });

                    const blob = new Blob([csv], {type: 'text/csv'});
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `courbe_${typeCourbe}_essai_${essaiId}.csv`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);
                })
                .catch(error => {
                    console.error('Erreur lors de l\'export:', error);
                    // Fallback: rediriger vers la page de détail avec un paramètre d'export
                    window.open(`/detail/courbe.php?essai=${essaiId}&type=${encodeURIComponent(typeCourbe)}&export=csv`, '_blank');
                });
        }

        // Gestion du mode optimisé pour les graphiques
        document.getElementById('toggleChartOptimization')?.addEventListener('click', function () {
            const button = this;
            const isOptimized = button.textContent.includes('Optimisé');

            if (isOptimized) {
                button.textContent = 'Mode Normal';
                button.className = button.className.replace('bg-gray-600 hover:bg-gray-700', 'bg-green-600 hover:bg-green-700');
                // Ici on pourrait recharger les graphiques avec plus de points
            } else {
                button.textContent = 'Mode Optimisé';
                button.className = button.className.replace('bg-green-600 hover:bg-green-700', 'bg-gray-600 hover:bg-gray-700');
                // Ici on pourrait recharger les graphiques avec moins de points
            }
        });

        // Amélioration des tooltips pour les graphiques
        document.addEventListener('DOMContentLoaded', function () {
            // Ajouter des tooltips informatifs
            const optimizationInfo = document.querySelectorAll('[data-tooltip="optimization"]');
            optimizationInfo.forEach(element => {
                element.title = 'Les données sont échantillonnées pour améliorer les performances d\'affichage';
            });
        });
    </script>

<?php
$pageContent = ob_get_clean();
include '../layout.php';
?>