# Gestion des Affaires

## Vue d'ensemble

Le module **Affaires** est le cœur du système FluidMotion Labs. Il permet de gérer l'ensemble des dossiers clients, depuis la création initiale jusqu'à la finalisation du projet. Chaque affaire représente un contrat ou une demande client nécessitant des essais hydrauliques.

## Interface du Module

### En-tête du Module

#### Informations Contextuelles
- **Titre** : "Gestion des Affaires"
- **Compteur** : Nombre total d'affaires et pagination
- **Bouton principal** : "Nouvelle Affaire" (bleu)

#### Exemple d'affichage
```
Gestion des Affaires                    [Nouvelle Affaire]
1247 affaires - Page 1/25
```

### Zone de Filtres

<div className="feature-highlight">

#### Critères de Recherche Disponibles

**Numéro d'Affaire**
- Recherche exacte ou partielle
- Sensible à la casse
- Placeholder : "Rechercher par numéro..."

**Client**
- Recherche dans le nom du client
- Recherche partielle supportée
- Placeholder : "Rechercher par client..."

**Statut**
- Liste déroulante avec options :
  - "Tous les statuts" (par défaut)
  - "En cours"
  - "Terminé"
  - "Annulé"

</div>

#### Boutons d'Action
- **"Filtrer"** : Applique les critères sélectionnés
- **"Réinitialiser"** : Efface tous les filtres

## Tableau des Affaires

### Colonnes Affichées

| Colonne | Description | Tri |
|---------|-------------|-----|
| **Numéro** | Identifiant unique de l'affaire | ✅ |
| **Client** | Nom de l'entreprise cliente | ✅ |
| **Description** | Résumé du projet (tronqué à 35 caractères) | ❌ |
| **Date de création** | Date de création de l'affaire | ✅ |
| **Statut** | État actuel avec badge coloré | ✅ |
| **Créé par** | Nom d'utilisateur du créateur | ❌ |
| **Actions** | Boutons d'action (Modifier, Supprimer, Voir) | ❌ |

### Statuts et Codes Couleur

#### En cours
- <span className="status-en-cours">En cours</span>
- Affaire active nécessitant des actions
- Couleur : Bleu

#### Terminé
- <span className="status-termine">Terminé</span>
- Affaire complétée avec tous les essais finalisés
- Couleur : Vert

#### Annulé
- <span className="status-annule">Annulé</span>
- Affaire abandonnée ou annulée par le client
- Couleur : Rouge

### Actions par Ligne

#### Modifier
- **Icône** : Texte "Modifier" en bleu
- **Action** : Ouvre la modale de modification
- **Permissions** : Tous les utilisateurs

#### Supprimer
- **Icône** : Texte "Supprimer" en rouge
- **Action** : Ouvre la modale de confirmation
- **Permissions** : Tous les utilisateurs
- **Sécurité** : Confirmation obligatoire

#### Voir
- **Icône** : Texte "Voir" en vert
- **Action** : Redirige vers la vue détaillée
- **Permissions** : Tous les utilisateurs

## Création d'une Nouvelle Affaire

### Ouverture de la Modale

<div className="step-number">1</div>
<div className="step-content">

Cliquez sur le bouton **"Nouvelle Affaire"** dans l'en-tête du module.

</div>

### Formulaire de Création

#### Champs Obligatoires

<div className="step-number">2</div>
<div className="step-content">

**Numéro**
- Identifiant unique de l'affaire
- Format libre (recommandé : préfixe + numéro)
- Exemple : "AFF-2024-001"

**Client**
- Nom de l'entreprise ou du client
- Texte libre
- Exemple : "Hydraulique Industries SA"

</div>

#### Champs Optionnels

<div className="step-number">3</div>
<div className="step-content">

**Description**
- Zone de texte libre
- Résumé du projet ou des besoins
- Affiché tronqué dans les listes

**Tags**
- Mots-clés séparés par des virgules
- Exemples : "urgent, prioritaire, en attente"
- Utilisés pour l'organisation et la recherche

</div>

### Validation et Enregistrement

<div className="step-number">4</div>
<div className="step-content">

- Cliquez sur **"Créer l'affaire"**
- L'affaire est créée avec le statut "En cours"
- Redirection automatique vers la liste mise à jour

</div>

## Modification d'une Affaire

### Accès à la Modification

#### Depuis la Liste
- Cliquez sur **"Modifier"** dans la ligne de l'affaire
- Ouverture de la modale de modification

#### Depuis la Vue Détaillée
- Bouton "Modifier" dans la vue détaillée
- Même formulaire que la création

### Formulaire de Modification

#### Champs Modifiables
- **Numéro** : Modifiable (attention aux références)
- **Client** : Modifiable
- **Description** : Modifiable
- **Statut** : Sélection parmi les options disponibles
- **Tags** : Modifiables (séparés par virgules)

#### Champs Non Modifiables
- **Date de création** : Fixe
- **Créé par** : Fixe
- **ID système** : Fixe

### Gestion des Tags

#### Ajout de Tags
- Saisissez les nouveaux tags séparés par des virgules
- Les tags existants sont conservés
- Création automatique des nouveaux tags

#### Suppression de Tags
- Supprimez le tag de la liste
- Le tag reste disponible pour d'autres affaires
- Suppression de la liaison uniquement

## Suppression d'une Affaire

### Processus de Suppression

<div className="warning-box">

**⚠️ Attention : Action Irréversible**

La suppression d'une affaire entraîne :
- Suppression de tous les essais associés
- Suppression de tous les PV liés
- Suppression de toutes les données de mesure
- Perte définitive des informations

</div>

### Étapes de Suppression

<div className="step-number">1</div>
<div className="step-content">

**Initiation**
- Cliquez sur "Supprimer" dans la ligne de l'affaire
- Ouverture de la modale de confirmation

</div>

<div className="step-number">2</div>
<div className="step-content">

**Confirmation**
- Lisez attentivement le message d'avertissement
- Cliquez sur "Oui, supprimer" pour confirmer
- Ou "Non, annuler" pour abandonner

</div>

<div className="step-number">3</div>
<div className="step-content">

**Exécution**
- Suppression immédiate et définitive
- Mise à jour automatique de la liste
- Message de confirmation affiché

</div>

## Pagination et Performance

### Système de Pagination

#### Configuration par Défaut
- **50 affaires par page** (optimisé pour les performances)
- **Navigation** par pages numérotées
- **Informations** sur le nombre total d'éléments

#### Contrôles de Pagination
- **Précédent/Suivant** : Navigation séquentielle
- **Numéros de page** : Accès direct
- **Affichage** : "Affichage de X à Y sur Z résultats"

### Optimisation des Performances

#### Chargement Intelligent
- Chargement uniquement des données visibles
- Pagination côté serveur
- Réduction des temps de chargement

#### Filtres Efficaces
- Application des filtres côté serveur
- Réduction du volume de données transférées
- Réponse rapide même avec de gros volumes

## Bonnes Pratiques

### Nomenclature des Affaires

<div className="success-box">

**Recommandations pour les numéros d'affaire :**
- Utilisez un préfixe cohérent (ex: "AFF-")
- Incluez l'année (ex: "AFF-2024-")
- Numérotez séquentiellement (ex: "AFF-2024-001")
- Évitez les caractères spéciaux

</div>

### Gestion des Tags

#### Tags Recommandés
- **Priorité** : urgent, normal, faible
- **Type** : maintenance, développement, validation
- **Client** : nouveau, récurrent, vip
- **Statut** : en_attente, planifié, retardé

#### Bonnes Pratiques
- Utilisez des tags courts et explicites
- Maintenez une liste cohérente
- Évitez la multiplication excessive
- Documentez les conventions d'équipe

### Organisation et Suivi

#### Workflow Recommandé
1. **Création** avec informations complètes
2. **Planification** des essais nécessaires
3. **Suivi** régulier de l'avancement
4. **Mise à jour** du statut selon la progression
5. **Finalisation** avec changement de statut

---

:::tip Efficacité
Utilisez les filtres pour organiser votre travail : filtrez par statut "En cours" pour voir vos affaires actives.
:::

:::warning Suppression
Avant de supprimer une affaire, vérifiez qu'aucune donnée importante ne sera perdue. Préférez le statut "Annulé" si nécessaire.
:::
