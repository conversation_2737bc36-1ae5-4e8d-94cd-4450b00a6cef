Bien sûr. Voici une proposition de cahier des charges pour le site web et le logiciel Qt, basée sur le document fourni.

## Cahier des charges du Site Web (IHM Web)

**1. Introduction**

Ce document décrit le cahier des charges pour le développement de l'interface homme-machine (IHM) web, qui fait partie du système d'informatisation du banc d'essai hydraulique. L'IHM web sera utilisée par le personnel du poste Contrôleur pour gérer les affaires, les essais, visualiser les courbes, éditer les PV clients et sauvegarder les données.

**2. Objectifs**

L'objectif principal de l'IHM web est de fournir une interface conviviale et sécurisée permettant au Contrôleur de :

*   Gérer les informations des affaires issues du système GPAO.
*   Créer et modifier les modes opératoires des essais.
*   Visualiser les résultats des essais sous forme de courbes.
*   Générer et éditer les Procès-Verbaux (PV) clients.
*   Gérer la sauvegarde et la restauration de la base de données des essais.
*   Gérer les utilisateurs et leurs rôles.

**3. Exigences Fonctionnelles**

L'IHM web doit implémenter les fonctionnalités suivantes, conformément aux exigences identifiées dans le dossier technique :

*   **Gestion des affaires (F9, F10):**
    *   Récupérer les informations des affaires à partir d'un fichier texte (F9).
    *   Enregistrer les nouvelles affaires dans la base de données Essais (F10).
    *   Permettre la modification des affaires existantes.
    *   Prévenir la création de doublons basés sur le numéro d'affaire.
*   **Gestion des essais et du mode opératoire (F11, F12, F13):**
    *   Permettre la création de nouveaux essais pour une affaire donnée (F11).
    *   Stocker les paramètres théoriques des essais (fréquence d'acquisition, type d'essai, liste des essais à faire) (F11).
    *   Permettre la modification des essais non effectués (F13).
    *   Permettre la suppression des essais non effectués (F13).
    *   Permettre l'édition d'une fiche d'essai (F13).
    *   Indiquer la validation d'un essai réalisé physiquement sur le banc (F12).
    *   Empêcher la revalidation d'un essai déjà validé (F12).
    *   Afficher la liste des essais pour une affaire donnée (F12).
*   **Visualisation des courbes en différé (F14):**
    *   Permettre l'accès à l'ensemble des essais réalisés pour une affaire.
    *   Visualiser les courbes de pression d'entrée \( CPA = f(t) \), pression de sortie \( CPB = f(t) \), et la résultante.
    *   Afficher les courbes caractéristiques du vérin.
*   **Calcul des rendements (F15):**
    *   Permettre le calcul des rendements pour l'essai en cours et à la demande.
*   **Édition des PV clients (F16, F17, F18):**
    *   Générer un PV client à partir d'une affaire, en sélectionnant les essais, courbes et vérins pertinents (F16).
    *   Inclure la saisie d'informations complémentaires pour le PV (F16).
    *   Enregistrer le PV dans la base de données après validation (F16).
    *   Permettre la modification de certains champs du PV (F17).
    *   Permettre la suppression d'un PV (F17).
    *   Éditer le PV à l'écran (F18).
    *   Permettre l'envoi automatique du PV par email au format PDF (F18).
    *   Permettre l'impression du PV sur papier (F18).
*   **Sauvegarde des essais du serveur sur un autre support (F19):**
    *   Permettre de copier l'intégralité de la base de données Essais sur un autre support (F19).
    *   Vérifier que l'espace libre sur le support de sauvegarde est suffisant (F19).
*   **Restauration de la base de données (F20, F21):**
    *   Permettre de recharger une base de données Essais à partir du support de sauvegarde (F20).
    *   Vérifier que l'espace libre sur le support de restauration est suffisant (F20).
    *   Permettre l'initialisation d'une base de données Essais vierge (F21).
    *   Effectuer l'écrasement de la base de données existante sur le poste Contrôleur uniquement après validation de l'utilisateur (F20, F21).
*   **Gestion des utilisateurs et des rôles:**
    *   Permettre la gestion des comptes utilisateurs.
    *   Différencier les droits d'accès entre les opérateurs et les contrôleurs.
*   **Authentification:**
    *   Exiger une authentification pour l'accès aux fonctionnalités critiques.

**4. Exigences Non Fonctionnelles**

*   **Sécurité:**
    *   Protéger la base de données contre les accès non autorisés et les manipulations.
    *   Prévenir les risques d'injections SQL et autres attaques via l'IHM web.
    *   Assurer la confidentialité et l'intégrité des données.
*   **Fiabilité:**
    *   Assurer la disponibilité et la continuité des traitements (Exigence de disponibilité 99.9%).
    *   Mettre en place un mécanisme de sauvegarde et de restauration fiable des données critiques.
*   **Performance:**
    *   Le temps de réponse de l'interface doit être raisonnable, même lors de la visualisation de données volumineuses.
*   **Maniabilité:**
    *   L'interface utilisateur doit être intuitive et facile à utiliser pour le Contrôleur.
    *   Utiliser des fenêtres d'affichage et des boîtes de dialogue claires.
*   **Maintenabilité:**
    *   Le code source doit être structuré et documenté pour faciliter la localisation et la correction des erreurs.
    *   Permettre l'ajout ou le retrait de fonctionnalités avec un minimum d'impact sur le système existant.
*   **Traçabilité:**
    *   Les données des essais doivent être archivées pendant 5 ans pour des raisons de traçabilité.
*   **Compatibilité:**
    *   L'IHM web doit être compatible avec un serveur web (Apache) et une base de données (MariaDB) fonctionnant sous Linux Debian.
    *   Utiliser les langages de programmation C++, PHP et JS.

**5. Architecture Technique**

*   L'IHM web sera développée en utilisant Node JS et des WebSockets pour la communication en temps réel si nécessaire.
*   L'accès à la base de données (MariaDB) se fera via un serveur Apache.
*   L'authentification des utilisateurs doit être gérée côté serveur.
*   La communication avec le poste Banc pour la récupération des données d'acquisition se fera via le réseau Ethernet (Requêtes REST).

**6. Interfaces**

*   **Interface utilisateur:** Interface web accessible via un navigateur.
*   **Interface base de données:** Connexion à la base de données MariaDB.
*   **Interface avec le poste Banc:** Communication via Requêtes REST pour la récupération des données.
*   **Interface système GPAO:** Lecture d'un fichier texte pour récupérer les informations des affaires.
*   **Interface email:** Utilisation d'une bibliothèque C++ pour l'envoi de PV en PDF.

**7. Données**

*   La base de données Essais doit stocker les informations des affaires, les modes opératoires des essais, les données d'acquisition (brutes), les PV clients, les informations utilisateurs.
*   Les données d'acquisition doivent être stockées dans un format standardisé, sans unité, avec les unités gérées par l'application.

**8. Contraintes**

*   Utilisation des langages C++, PHP et JS.
*   Utilisation de Node JS et WebSockets.
*   Déploiement sur un système d'exploitation Linux Debian avec serveur Apache et MariaDB.
*   Budget limité pour les éventuelles commandes complémentaires (1000€).
*   Utilisation du matériel existant au laboratoire BTS Ciel.

**9. Livrables**

*   Code source complet de l'IHM web sur Github.
*   Documentation technique de l'IHM web (architecture, code, procédures de maintenance).
*   Manuel utilisateur de l'IHM web.
*   Scripts d'installation et de configuration.

**10. Glossaire**

*   **IHM:** Interface Homme-Machine
*   **PV:** Procès-Verbal
*   **GPAO:** Gestion de Production Assistée par Ordinateur
*   **SGBD:** Système de Gestion de Base de Données
*   **REST:** Representational State Transfer
*   **WebSockets:** Protocole de communication bidirectionnelle en temps réel sur une connexion TCP.
*   **CPA:** Pression Cylindre Avant
*   **CPB:** Pression Cylindre Arrière