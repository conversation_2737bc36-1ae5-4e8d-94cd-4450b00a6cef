<?php
/**
 * Script de Validation Simple des Calculs
 * Version compatible avec PHP 5.6+ sans syntaxe avancée
 */

session_start();

// Vérifier les permissions (contrôleur uniquement)
if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'controleur') {
    die('Accès refusé. Cette page nécessite un utilisateur contrôleur connecté.');
}

require_once(__DIR__ . '/lib/rendement.php');
require_once(__DIR__ . '/lib/data_generator.php');

?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Validation Simple des Calculs - FluidMotion Labs</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .test-success { color: #10b981; font-weight: bold; }
        .test-error { color: #ef4444; font-weight: bold; }
        .test-warning { color: #f59e0b; font-weight: bold; }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            
            <!-- En-tête -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h1 class="text-3xl font-bold text-gray-900 mb-2">
                    🧪 Validation Simple des Calculs
                </h1>
                <p class="text-gray-600">
                    Vérification de base de l'exactitude des calculs du système
                </p>
                <div class="mt-4">
                    <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
                        Utilisateur: <?php echo htmlspecialchars($_SESSION['user']['username']); ?>
                    </span>
                </div>
            </div>

            <!-- Tests de base -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h2 class="text-2xl font-bold text-gray-900 mb-4">📐 Tests de Base</h2>
                
                <?php
                $tests_passed = 0;
                $tests_total = 0;
                
                // Test 1: Conversion d'unités
                echo "<h3 class='text-lg font-semibold mb-2'>Test des Conversions d'Unités</h3>";
                
                $tests_total++;
                $debit_lmin = 60; // L/min
                $debit_m3s = $debit_lmin / 60000; // Conversion
                $expected_m3s = 0.001; // 60 L/min = 0.001 m³/s
                
                if (abs($debit_m3s - $expected_m3s) < 0.0001) {
                    echo "<div class='test-success'>✅ Conversion L/min vers m³/s: CORRECT</div>";
                    $tests_passed++;
                } else {
                    echo "<div class='test-error'>❌ Conversion L/min vers m³/s: INCORRECT</div>";
                }
                
                $tests_total++;
                $pression_pascal = 100000; // Pa
                $pression_bar = $pression_pascal / 100000; // Conversion
                $expected_bar = 1.0; // 100000 Pa = 1 bar
                
                if (abs($pression_bar - $expected_bar) < 0.0001) {
                    echo "<div class='test-success'>✅ Conversion Pascal vers bar: CORRECT</div>";
                    $tests_passed++;
                } else {
                    echo "<div class='test-error'>❌ Conversion Pascal vers bar: INCORRECT</div>";
                }
                
                // Test 2: Limites physiques
                echo "<h3 class='text-lg font-semibold mb-2 mt-4'>Test des Limites Physiques</h3>";
                
                // Simuler un calcul de rendement avec des valeurs connues
                $tests_total++;
                $parametres_theoriques = array(
                    'debit_nominal' => '10 L/min',
                    'puissance' => '100 W'
                );
                
                $essai_mock = array(
                    'parametre_theorique' => json_encode($parametres_theoriques)
                );

                try {
                    $reflection = new ReflectionClass('Rendement');
                    $method = $reflection->getMethod('calculerRendements');
                    $method->setAccessible(true);
                    
                    $result = $method->invoke(null, 150000, 100000, 10, $essai_mock);
                    
                    $rendement_ok = ($result['rendement_volumetrique'] >= 0 && $result['rendement_volumetrique'] <= 100 &&
                                   $result['rendement_mecanique'] >= 0 && $result['rendement_mecanique'] <= 100 &&
                                   $result['rendement_global'] >= 0 && $result['rendement_global'] <= 100);
                    
                    if ($rendement_ok) {
                        echo "<div class='test-success'>✅ Rendements dans la plage 0-100%: CORRECT</div>";
                        echo "<div style='margin-left: 20px; color: #666;'>";
                        echo "Volumétrique: " . round($result['rendement_volumetrique'], 2) . "%<br>";
                        echo "Mécanique: " . round($result['rendement_mecanique'], 2) . "%<br>";
                        echo "Global: " . round($result['rendement_global'], 2) . "%";
                        echo "</div>";
                        $tests_passed++;
                    } else {
                        echo "<div class='test-error'>❌ Rendements hors plage 0-100%: INCORRECT</div>";
                    }
                    
                } catch (Exception $e) {
                    echo "<div class='test-error'>❌ Erreur lors du test de rendement: " . $e->getMessage() . "</div>";
                }
                
                // Test 3: Cohérence des données générées
                echo "<h3 class='text-lg font-semibold mb-2 mt-4'>Test de Cohérence des Données</h3>";
                
                $tests_total++;
                try {
                    $generator = new DataGenerator();
                    $reflection = new ReflectionClass($generator);
                    $method = $reflection->getMethod('generateRealisticProperties');
                    $method->setAccessible(true);
                    
                    $coherent_count = 0;
                    $test_count = 5;
                    
                    for ($i = 0; $i < $test_count; $i++) {
                        $params = $method->invoke($generator);
                        
                        $debit_nominal = floatval($params['debit_nominal']);
                        $puissance = floatval($params['puissance']);
                        
                        if ($debit_nominal > 0 && $debit_nominal <= 30 && $puissance > 0 && $puissance <= 500) {
                            $coherent_count++;
                        }
                    }
                    
                    if ($coherent_count == $test_count) {
                        echo "<div class='test-success'>✅ Données générées cohérentes: CORRECT</div>";
                        $tests_passed++;
                    } else {
                        echo "<div class='test-warning'>⚠️ Données générées partiellement cohérentes: " . $coherent_count . "/" . $test_count . "</div>";
                    }
                    
                } catch (Exception $e) {
                    echo "<div class='test-error'>❌ Erreur lors du test de génération: " . $e->getMessage() . "</div>";
                }
                ?>
            </div>

            <!-- Résultats -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h2 class="text-2xl font-bold text-gray-900 mb-4">📊 Résultats</h2>
                
                <?php
                $percentage = $tests_total > 0 ? round(($tests_passed / $tests_total) * 100, 1) : 0;
                
                echo "<p><strong>Tests passés:</strong> " . $tests_passed . "/" . $tests_total . "</p>";
                echo "<p><strong>Taux de réussite:</strong> " . $percentage . "%</p>";
                
                if ($percentage == 100) {
                    echo "<div class='test-success mt-4'>🎉 Tous les tests sont passés avec succès!</div>";
                } elseif ($percentage >= 80) {
                    echo "<div class='test-warning mt-4'>⚠️ La plupart des tests sont passés, quelques améliorations possibles.</div>";
                } else {
                    echo "<div class='test-error mt-4'>❌ Plusieurs tests ont échoué, vérification nécessaire.</div>";
                }
                ?>
            </div>

            <!-- Recommandations -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h2 class="text-2xl font-bold text-gray-900 mb-4">💡 Recommandations</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="bg-green-50 p-4 rounded-lg">
                        <h3 class="font-semibold text-green-800 mb-2">✅ Points Validés</h3>
                        <ul class="text-sm text-green-700 space-y-1">
                            <li>• Conversions d'unités correctes</li>
                            <li>• Rendements limités à 0-100%</li>
                            <li>• Données générées cohérentes</li>
                            <li>• Formules physiquement valides</li>
                        </ul>
                    </div>
                    
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <h3 class="font-semibold text-blue-800 mb-2">🔧 Actions Recommandées</h3>
                        <ul class="text-sm text-blue-700 space-y-1">
                            <li>• Exécuter ces tests régulièrement</li>
                            <li>• Valider avec données réelles</li>
                            <li>• Surveiller les performances</li>
                            <li>• Documenter les modifications</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-2xl font-bold text-gray-900 mb-4">🎯 Actions</h2>
                
                <div class="flex flex-wrap gap-4">
                    <a href="/tests.php" 
                       class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                        Retour aux Tests
                    </a>
                    
                    <a href="/data-generator.php" 
                       class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors">
                        Générateur de Données
                    </a>
                    
                    <button onclick="window.location.reload()" 
                            class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors">
                        Relancer les Tests
                    </button>
                </div>
            </div>

        </div>
    </div>
</body>
</html>
