<?php
/**
 * Script de test pour vérifier les fonctionnalités de backup dans Docker
 */

session_start();

// Vérifier que l'utilisateur est connecté et est un contrôleur
if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'controleur') {
    die('Accès refusé. Ce script nécessite un utilisateur contrôleur connecté.');
}

echo "<h1>Test des Fonctionnalités Docker - Backup et PDF</h1>";

echo "<h2>1. Test des outils MariaDB</h2>";

// Tester mysqldump
echo "<h3>Test de mysqldump</h3>";
$output = [];
$returnCode = 0;
exec('mysqldump --version 2>&1', $output, $returnCode);

if ($returnCode === 0) {
    echo "✅ mysqldump disponible: " . implode(' ', $output) . "<br>";
} else {
    echo "❌ mysqldump non disponible<br>";
    echo "Erreur: " . implode(' ', $output) . "<br>";
}

// Tester mysql client
echo "<h3>Test du client MySQL</h3>";
$output = [];
$returnCode = 0;
exec('mysql --version 2>&1', $output, $returnCode);

if ($returnCode === 0) {
    echo "✅ Client MySQL disponible: " . implode(' ', $output) . "<br>";
} else {
    echo "❌ Client MySQL non disponible<br>";
    echo "Erreur: " . implode(' ', $output) . "<br>";
}

echo "<h2>2. Test de la connectivité base de données</h2>";

try {
    require_once(__DIR__ . '/../config/database.php');
    $db = Database::getInstance()->getConnection();
    echo "✅ Connexion à la base de données: OK<br>";

    // Tester une requête simple
    $stmt = $db->query("SELECT COUNT(*) AS COUNT FROM users");
    $result = $stmt->fetch();
    echo "✅ Nombre d'utilisateurs dans la base: " . $result['count'] . "<br>";

    // Tester les informations de connexion pour mysqldump
    $stmt = $db->query("SELECT DATABASE() as db_name");
    $result = $stmt->fetch();
    echo "✅ Base de données actuelle: " . $result['db_name'] . "<br>";

} catch (Exception $e) {
    echo "❌ Erreur de base de données: " . $e->getMessage() . "<br>";
}

echo "<h2>3. Test des dossiers et permissions</h2>";

$directories = [
    __DIR__ . '/../backups' => 'Dossier de sauvegarde',
    __DIR__ . '/../pdf_exports' => 'Dossier d\'export PDF',
    __DIR__ . '/../temp' => 'Dossier temporaire',
    __DIR__ . '/../temp/mpdf' => 'Dossier temporaire mPDF'
];

foreach ($directories as $dir => $description) {
    echo "<strong>$description:</strong> ";
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            echo "✅ OK (accessible en écriture)<br>";
        } else {
            echo "❌ Pas d'accès en écriture<br>";
        }
    } else {
        echo "❌ Dossier inexistant<br>";
    }
}

echo "<h2>4. Test de mPDF</h2>";

if (file_exists(__DIR__ . '/../vendor/autoload.php')) {
    require_once(__DIR__ . '/../vendor/autoload.php');
    echo "✅ Autoloader Composer chargé.<br>";

    if (class_exists('\Mpdf\Mpdf')) {
        echo "✅ Classe mPDF disponible.<br>";

        try {
            $tempDir = __DIR__ . '/../temp/mpdf';
            $mpdf = new \Mpdf\Mpdf([
                'tempDir' => $tempDir,
                'mode' => 'utf-8',
                'format' => 'A4'
            ]);
            echo "✅ Instance mPDF créée avec succès.<br>";
        } catch (Exception $e) {
            echo "❌ Erreur mPDF: " . $e->getMessage() . "<br>";
        }
    } else {
        echo "❌ Classe mPDF non disponible.<br>";
    }
} else {
    echo "❌ Autoloader Composer non trouvé.<br>";
}

echo "<h2>5. Test de backup complet</h2>";

if (class_exists('Backup')) {
    require_once(__DIR__ . '/../lib/backup.php');

    // Test de vérification de l'espace disque
    $diskSpace = Backup::checkDiskSpace(__DIR__ . '/../backups');
    if ($diskSpace['success']) {
        echo "✅ Espace disque disponible: " . $diskSpace['free_space_gb'] . " GB<br>";
    } else {
        echo "❌ Erreur espace disque: " . $diskSpace['message'] . "<br>";
    }

    // Test de création de backup (sans l'exécuter réellement)
    echo "✅ Classe Backup disponible pour les tests<br>";
} else {
    echo "❌ Classe Backup non disponible<br>";
}

echo "<h2>6. Informations sur l'environnement Docker</h2>";

echo "<ul>";
echo "<li>Version PHP: " . PHP_VERSION . "</li>";
echo "<li>Système d'exploitation: " . PHP_OS . "</li>";
echo "<li>Architecture: " . php_uname('m') . "</li>";
echo "<li>Utilisateur web: " . get_current_user() . "</li>";
echo "<li>Dossier de travail: " . getcwd() . "</li>";

// Fonction pour détecter l'environnement
function detectEnvironment()
{
    // Méthode 1: Variables d'environnement Docker
    if (getenv('DOCKER_CONTAINER') !== false || getenv('HOSTNAME') !== false) {
        $hostname = getenv('HOSTNAME');
        if ($hostname && (strpos($hostname, 'docker') !== false || strlen($hostname) === 12)) {
            return 'Docker';
        }
    }

    // Méthode 2: Vérifier les variables d'environnement spécifiques à notre setup
    if (getenv('DB_HOST') === 'db' || getenv('APACHE_RUN_USER') === 'www-data') {
        return 'Docker';
    }

    // Méthode 3: Vérifier le fichier /.dockerenv (fallback)
    if (file_exists('/.dockerenv')) {
        return 'Docker';
    }

    // Méthode 4: Vérifier les processus (si proc est disponible)
    if (is_readable('/proc/1/cgroup')) {
        $cgroup = file_get_contents('/proc/1/cgroup');
        if (strpos($cgroup, 'docker') !== false || strpos($cgroup, 'containerd') !== false) {
            return 'Docker';
        }
    }

    // Méthode 5: Vérifier le répertoire de travail typique de Docker
    $cwd = getcwd();
    if (strpos($cwd, '/var/www/html') === 0 && file_exists('/var/www/html/docker-compose.yml')) {
        return 'Docker';
    }

    return 'Standard';
}

// Vérifier l'environnement
$environment = detectEnvironment();
if ($environment === 'Docker') {
    echo "<li>✅ Environnement: Docker</li>";
} else {
    echo "<li>⚠️ Environnement: $environment</li>";
}

// Vérifier les variables d'environnement Docker
$dockerEnvVars = ['DB_HOST', 'DB_NAME', 'DB_USER', 'DB_PASS'];
foreach ($dockerEnvVars as $var) {
    $value = getenv($var);
    if ($value !== false) {
        echo "<li>$var: " . ($var === 'DB_PASS' ? '***' : $value) . "</li>";
    }
}

echo "</ul>";

echo "<h2>7. Actions de test disponibles</h2>";
echo "<ul>";
echo "<li><a href='/tests.php'>← Retour aux Tests</a></li>";
echo "<li><a href='/backup.php'>Tester l'interface de sauvegarde</a></li>";
echo "<li><a href='/tests/test_mpdf.php'>Tester la génération PDF</a></li>";
echo "<li><a href='/tests/check_permissions.php'>Vérifier les permissions</a></li>";
echo "<li><a href='/pv.php'>Tester la génération PDF depuis un PV</a></li>";
echo "</ul>";

echo "<h2>8. Commandes Docker utiles</h2>";
echo "<pre>";
echo "# Construire l'image\n";
echo "docker-compose build\n\n";
echo "# Démarrer les services\n";
echo "docker-compose up -d\n\n";
echo "# Voir les logs\n";
echo "docker-compose logs web\n\n";
echo "# Accéder au conteneur\n";
echo "docker-compose exec web bash\n\n";
echo "# Tester mysqldump dans le conteneur\n";
echo "docker-compose exec web mysqldump --version\n";
echo "</pre>";

?>

<style>
    body {
        font-family: Arial, sans-serif;
        margin: 20px;
    }

    h1 {
        color: #2563eb;
    }

    h2 {
        color: #1e40af;
        border-bottom: 1px solid #e5e7eb;
        padding-bottom: 5px;
    }

    h3 {
        color: #374151;
    }

    ul {
        margin-left: 20px;
    }

    pre {
        background-color: #f3f4f6;
        padding: 15px;
        border-radius: 5px;
        overflow-x: auto;
    }

    a {
        color: #2563eb;
        text-decoration: none;
    }

    a:hover {
        text-decoration: underline;
    }
</style>
