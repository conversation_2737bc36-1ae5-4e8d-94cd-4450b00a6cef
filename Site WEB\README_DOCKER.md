# FluidMotion - Configuration Docker Avancée

## 🚀 Vue d'ensemble

Cette application utilise une configuration Docker moderne avec :

- **Multi-stage Dockerfile** pour optimiser les builds
- **GitHub Actions** pour l'intégration continue
- **Configuration sécurisée** avec variables d'environnement
- **Health checks** et monitoring intégrés
- **Support développement/production**

## 🐳 Quick Start

### Prérequis

- Docker 20.10+
- Docker Compose 2.0+
- Git

### Configuration initiale

```bash
# Naviguer vers le répertoire du projet
cd "Site WEB"

# Copier et configurer les variables d'environnement
cp .env.example .env

# Éditer le fichier .env avec vos valeurs
nano .env
```

### Lancement de l'application

```bash
# Développement (avec Xdebug et PhpMyAdmin)
docker-compose up -d

# Production
BUILD_TARGET=production docker-compose up -d

# Avec cache Redis
docker-compose --profile cache up -d

# Utiliser l'image pré-construite depuis GitHub Container Registry
docker run -d -p 8080:80 ghcr.io/laprovidenceamiens/ciel2_bancdetest:latest
```

### Utilisation des images GitHub Container Registry

Les images sont automatiquement publiées sur GitHub Container Registry :

```bash
# Image de production (latest)
docker pull ghcr.io/laprovidenceamiens/ciel2_bancdetest:latest

# Image de développement
docker pull ghcr.io/laprovidenceamiens/ciel2_bancdetest:dev

# Version spécifique
docker pull ghcr.io/laprovidenceamiens/ciel2_bancdetest:v1.0.0
```

## 📋 Services et Architecture

### Services disponibles

| Service        | Port | Description                      | Profil      |
|----------------|------|----------------------------------|-------------|
| **web**        | 8080 | Application PHP avec Apache      | Toujours    |
| **db**         | 3306 | Base de données MariaDB          | Toujours    |
| **phpmyadmin** | 8081 | Interface d'administration       | Development |
| **redis**      | 6379 | Cache Redis                      | Cache       |

### Multi-stage Dockerfile

- **Base** : Dépendances système et PHP
- **Development** : Xdebug + outils de développement
- **Production** : Optimisé pour la performance et sécurité

## ⚙️ Configuration

### Variables d'environnement (.env)

```bash
# Application
APP_ENV=production
APP_DEBUG=false
APP_URL=http://localhost:8080

# Base de données
DB_NAME=verins_db
DB_USER=verins_user
DB_PASS=your_secure_password
DB_ROOT_PASSWORD=your_root_password

# Ports
WEB_PORT=8080
PHPMYADMIN_PORT=8081
REDIS_PORT=6379

# Sécurité
JWT_SECRET=your_jwt_secret
PASSWORD_SALT=your_password_salt
```

### Génération de secrets sécurisés

```bash
# Mot de passe base64 (32 caractères)
openssl rand -base64 32

# Clé JWT hexadécimale (64 caractères)
openssl rand -hex 32
```

## 🔄 GitHub Actions CI/CD

### Workflow automatique

Le projet inclut un workflow GitHub Actions (`.github/workflows/docker-build.yml`) qui :

- **Build automatique** des images Docker sur push/PR
- **Multi-platform** : linux/amd64 et linux/arm64
- **Publication** sur GitHub Container Registry (ghcr.io)
- **Scan de sécurité** avec Trivy
- **Cache intelligent** pour optimiser les builds
- **Authentification automatique** avec GITHUB_TOKEN

### Configuration automatique

Le workflow utilise automatiquement :

- **Registry** : `ghcr.io` (GitHub Container Registry)
- **Image name** : `ghcr.io/laprovidenceamiens/ciel2_bancdetest` (basé sur le repository, converti en minuscules)
- **Authentification** : `GITHUB_TOKEN` (automatique, aucun secret requis)
- **Permissions** : `packages: write` pour publier les images
- **Conversion automatique** : Le nom du repository est automatiquement converti en minuscules pour respecter les conventions GHCR

### Tags automatiques

- `latest` : branche principale (main)
- `dev` : branche develop
- `v1.0.0` : tags sémantiques
- `develop` : branche develop
- `main` : branche main

## 🚀 Commandes d'utilisation

### Opérations de base

```bash
# Démarrer tous les services
docker-compose up -d

# Arrêter les services
docker-compose down

# Voir les logs
docker-compose logs -f web

# Redémarrer un service
docker-compose restart web

# Reconstruire les images
docker-compose build --no-cache
```

### Développement

```bash
# Accéder au shell du conteneur web
docker-compose exec web bash

# Accéder à la base de données
docker-compose exec db mysql -u root -p

# Vérifier la configuration PHP
docker-compose exec web php -v
docker-compose exec web php -m

# Installer/mettre à jour les dépendances
docker-compose exec web composer install
```

### Health checks et monitoring

```bash
# Vérifier la santé de l'application
curl http://localhost:8080/health.php

# Statut des conteneurs
docker-compose ps

# Statistiques en temps réel
docker stats

# Logs avec suivi
docker-compose logs -f --tail=100 web
```

### Opérations de base de données

```bash
# Créer une sauvegarde manuelle
docker-compose exec db mysqldump -u root -p${DB_ROOT_PASSWORD} ${DB_NAME} > backup.sql

# Restaurer depuis une sauvegarde
docker-compose exec -T db mysql -u root -p${DB_ROOT_PASSWORD} ${DB_NAME} < backup.sql

# Accès direct à la base de données
docker-compose exec db mysql -u root -p

# Vérifier la connectivité
docker-compose exec web mysql -h db -u ${DB_USER} -p${DB_PASS} -e "SELECT 1"
```

## 🧪 Tests et validation

### Health check intégré

```bash
# Point de contrôle de santé complet
curl -s http://localhost:8080/health.php | jq

# Vérifications incluses :
# - Connexion base de données
# - Répertoires critiques
# - Extensions PHP
# - Utilisation mémoire
```

### Tests automatisés

```bash
# Suite de tests complète (utilisateurs contrôleur uniquement)
open http://localhost:8080/tests.php

# Tests individuels
open http://localhost:8080/tests/test_docker_backup.php  # Fonctionnalité Docker
open http://localhost:8080/tests/check_permissions.php   # Permissions
open http://localhost:8080/tests/test_mpdf.php          # mPDF
```

### Tests manuels recommandés

1. **Authentification** : Créer un compte utilisateur et se connecter
2. **Sauvegarde** : Tester la création et téléchargement de sauvegardes
3. **PDF** : Générer un PDF depuis un PV
4. **Restauration** : Uploader et restaurer une sauvegarde
5. **Health check** : Vérifier `/health.php`

## 📁 Volumes et persistance

### Volumes nommés

```yaml
volumes:
  # Données critiques
  db_data: ./data/mysql              # Données MariaDB
  app_backups: ./backups             # Sauvegardes application
  app_pdf_exports: ./pdf_exports     # Exports PDF

  # Données temporaires
  app_temp: volume temporaire        # Fichiers temporaires
  app_logs: volume logs              # Logs application
  redis_data: volume Redis           # Cache Redis
```

### Sauvegarde des volumes

```bash
# Sauvegarder tous les volumes
docker run --rm -v fluidmotion_db_data:/data -v $(pwd):/backup alpine tar czf /backup/db_backup.tar.gz /data

# Restaurer un volume
docker run --rm -v fluidmotion_db_data:/data -v $(pwd):/backup alpine tar xzf /backup/db_backup.tar.gz -C /
```

## 🔒 Sécurité

### Fonctionnalités de sécurité implémentées

- ✅ **Utilisateur non-root** dans les conteneurs
- ✅ **Variables d'environnement** pour les secrets
- ✅ **Headers de sécurité** Apache (HSTS, CSP, etc.)
- ✅ **Health checks** pour tous les services
- ✅ **Scan de vulnérabilités** automatique (Trivy)
- ✅ **Configuration PHP sécurisée**
- ✅ **Isolation réseau** avec sous-réseaux dédiés

### Configuration de production

**⚠️ Actions obligatoires en production :**

1. **Changer tous les mots de passe par défaut**
2. **Configurer HTTPS avec certificats SSL**
3. **Désactiver PhpMyAdmin** (profil development)
4. **Utiliser Docker secrets** pour les données sensibles
5. **Configurer un firewall** approprié
6. **Mettre en place la rotation des logs**
7. **Activer la surveillance** et les alertes

### Secrets recommandés

```bash
# Générer des secrets forts
DB_ROOT_PASSWORD=$(openssl rand -base64 32)
DB_PASS=$(openssl rand -base64 32)
JWT_SECRET=$(openssl rand -hex 32)
PASSWORD_SALT=$(openssl rand -base64 32)
REDIS_PASSWORD=$(openssl rand -base64 32)
```

## 🐛 Dépannage

### Problèmes courants

**Le conteneur ne démarre pas :**

```bash
# Vérifier les logs
docker-compose logs web
docker-compose logs db

# Reconstruire l'image
docker-compose build --no-cache

# Vérifier la configuration
docker-compose config
```

**Échec de connexion à la base de données :**

```bash
# Vérifier que la base de données fonctionne
docker-compose ps
docker-compose exec db mysqladmin ping

# Tester la connectivité réseau
docker-compose exec web ping db

# Vérifier les variables d'environnement
docker-compose exec web env | grep DB_
```

**Erreurs de permissions :**

```bash
# Corriger les permissions
docker-compose exec web chown -R www-data:www-data /var/www/html
docker-compose exec web chmod -R 755 /var/www/html/backups /var/www/html/pdf_exports /var/www/html/temp

# Vérifier les permissions
docker-compose exec web ls -la /var/www/html/
```

**Problèmes de health check :**

```bash
# Vérifier manuellement le health check
curl -v http://localhost:8080/health.php

# Logs du health check
docker inspect --format='{{.State.Health}}' fluidmotion_web
```

### Commandes de nettoyage

```bash
# Nettoyage complet
docker-compose down -v
docker system prune -a --volumes

# Reconstruction complète
docker-compose down -v
docker-compose build --no-cache
docker-compose up -d

# Nettoyage sélectif
docker volume prune
docker image prune -a
```

## 📊 Monitoring et performance

### Métriques importantes

```bash
# Utilisation des ressources
docker stats --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"

# Espace disque
docker system df

# Logs avec timestamps
docker-compose logs -t --tail=50 web
```

### Optimisation des performances

1. **Ajuster la mémoire** : Modifier `PHP_MEMORY_LIMIT` et `innodb-buffer-pool-size`
2. **Utiliser Redis** : Activer le profil cache pour améliorer les performances
3. **Optimiser les volumes** : Utiliser des volumes nommés plutôt que des bind mounts
4. **Monitoring continu** : Surveiller les métriques avec le health check

## 🔄 Mises à jour

### Mise à jour de l'application

```bash
# Récupérer les dernières modifications
git pull

# Reconstruire et redémarrer
docker-compose down
docker-compose build
docker-compose up -d
```

### Mise à jour des dépendances

```bash
# Mettre à jour Composer
docker-compose exec web composer update

# Mettre à jour les images Docker
docker-compose pull
docker-compose up -d
```

## � Support et ressources

### Diagnostic rapide

1. **Vérifier les logs** : `docker-compose logs -f`
2. **Health check** : `curl http://localhost:8080/health.php`
3. **Tests automatisés** : http://localhost:8080/tests.php
4. **Tests Docker** : http://localhost:8080/tests/test_docker_backup.php
5. **Permissions** : http://localhost:8080/tests/check_permissions.php

### Ressources utiles

- **Configuration GitHub Actions** : `.github/workflows/docker-build.yml`
- **Variables d'environnement** : `.env.example`
- **Configuration Docker** : `docker-compose.yml` et `Dockerfile`
- **Images publiées** : https://github.com/LaProvidenceAmiens/CIEL2_BancDeTest/pkgs/container/ciel2_bancdetest

### Commandes de diagnostic

```bash
# État complet du système
docker-compose ps
docker system df
docker stats --no-stream

# Validation de la configuration
docker-compose config --quiet && echo "Configuration valide"

# Test de connectivité
docker-compose exec web curl -f http://localhost/health.php
```

## 🎯 Résumé des améliorations

### Nouvelles fonctionnalités

- ✅ **Multi-stage Dockerfile** (development/production)
- ✅ **GitHub Actions CI/CD** avec build automatique
- ✅ **Health checks** intégrés
- ✅ **Configuration sécurisée** avec variables d'environnement
- ✅ **Support Redis** pour le cache
- ✅ **Scan de sécurité** automatique
- ✅ **Monitoring** et métriques

### Sécurité renforcée

- ✅ **Headers de sécurité** Apache
- ✅ **Utilisateurs non-root**
- ✅ **Isolation réseau**
- ✅ **Secrets management**
- ✅ **Configuration PHP sécurisée**

### DevOps et CI/CD

- ✅ **Build multi-plateforme** (amd64/arm64)
- ✅ **Publication automatique** sur GitHub Container Registry
- ✅ **Cache intelligent** pour optimiser les builds
- ✅ **Tests de sécurité** intégrés
- ✅ **Authentification automatique** avec GITHUB_TOKEN
- ✅ **Aucun secret externe requis**

---

**🚀 Votre application FluidMotion est maintenant prête pour le développement et la production avec une configuration Docker moderne et sécurisée !**
