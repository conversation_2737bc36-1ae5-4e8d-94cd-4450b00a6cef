<?php
session_start();
require_once(__DIR__ . '/../lib/user.php');

// Vérifier l'authentification et les droits
if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'controleur') {
    header('Location: /auth/login.php');
    exit;
}

// Inclure les tests
require_once(__DIR__ . '/unit/AffaireTest.php');
require_once(__DIR__ . '/unit/EssaiTest.php');
require_once(__DIR__ . '/unit/RendementTest.php');

$test_suite = $_GET['suite'] ?? 'all';
$format = $_GET['format'] ?? 'html';

if ($format === 'json') {
    header('Content-Type: application/json');
    echo json_encode(runTestSuite($test_suite));
    exit;
}

function runTestSuite($suite)
{
    $results = [];
    $total_assertions = 0;
    $total_failures = 0;
    $start_time = microtime(true);

    switch ($suite) {
        case 'affaire':
            $test = new AffaireTest();
            $results['AffaireTest'] = $test->runAllTests();
            break;
        case 'essai':
            $test = new EssaiTest();
            $results['EssaiTest'] = $test->runAllTests();
            break;
        case 'rendement':
            $test = new RendementTest();
            $results['RendementTest'] = $test->runAllTests();
            break;
        case 'all':
        default:
            $affaire_test = new AffaireTest();
            $results['AffaireTest'] = $affaire_test->runAllTests();

            $essai_test = new EssaiTest();
            $results['EssaiTest'] = $essai_test->runAllTests();

            $rendement_test = new RendementTest();
            $results['RendementTest'] = $rendement_test->runAllTests();
            break;
    }

    // Calculer les totaux
    foreach ($results as $test_class => $result) {
        $total_assertions += $result['assertions'];
        $total_failures += $result['failures'];
    }

    $end_time = microtime(true);
    $duration = round(($end_time - $start_time) * 1000, 2);

    return [
        'suite' => $suite,
        'total_assertions' => $total_assertions,
        'total_failures' => $total_failures,
        'success_rate' => $total_assertions > 0 ?
            round((($total_assertions - $total_failures) / $total_assertions) * 100, 2) : 0,
        'duration' => $duration,
        'results' => $results,
        'timestamp' => date('Y-m-d H:i:s')
    ];
}

$test_results = runTestSuite($test_suite);

ob_start();
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FluidMotion Labs - Test Runner</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .test-result {
            padding: 8px 12px;
            margin: 4px 0;
            border-radius: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .test-result.success {
            background-color: #d1fae5;
            border-left: 4px solid #10b981;
        }

        .test-result.failure {
            background-color: #fee2e2;
            border-left: 4px solid #ef4444;
        }

        .test-name {
            font-weight: 500;
        }

        .test-duration {
            font-size: 0.875rem;
            color: #6b7280;
        }

        .test-error {
            font-size: 0.875rem;
            color: #dc2626;
            margin-left: 8px;
        }
    </style>
</head>
<body class="bg-gray-100 dark:bg-gray-900">
<div class="container mx-auto px-4 py-8">
    <div class="max-w-6xl mx-auto">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                    FluidMotion Labs - Test Suite
                </h1>
                <div class="flex space-x-2">
                    <a href="?suite=all" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                        Tous les tests
                    </a>
                    <a href="?suite=affaire" class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700">
                        Tests Affaire
                    </a>
                    <a href="?suite=essai" class="px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700">
                        Tests Essai
                    </a>
                    <a href="?suite=rendement" class="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700">
                        Tests Rendement
                    </a>
                </div>
            </div>

            <!-- Résumé des tests -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
                <div class="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg">
                    <div class="text-blue-600 dark:text-blue-300 text-sm font-medium">Total Assertions</div>
                    <div class="text-2xl font-bold text-blue-900 dark:text-blue-100">
                        <?php echo $test_results['total_assertions']; ?>
                    </div>
                </div>
                <div class="bg-red-50 dark:bg-red-900 p-4 rounded-lg">
                    <div class="text-red-600 dark:text-red-300 text-sm font-medium">Échecs</div>
                    <div class="text-2xl font-bold text-red-900 dark:text-red-100">
                        <?php echo $test_results['total_failures']; ?>
                    </div>
                </div>
                <div class="bg-green-50 dark:bg-green-900 p-4 rounded-lg">
                    <div class="text-green-600 dark:text-green-300 text-sm font-medium">Taux de Réussite</div>
                    <div class="text-2xl font-bold text-green-900 dark:text-green-100">
                        <?php echo $test_results['success_rate']; ?>%
                    </div>
                </div>
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <div class="text-gray-600 dark:text-gray-300 text-sm font-medium">Durée</div>
                    <div class="text-2xl font-bold text-gray-900 dark:text-gray-100">
                        <?php echo $test_results['duration']; ?>ms
                    </div>
                </div>
            </div>

            <!-- Résultats détaillés -->
            <div class="space-y-6">
                <?php foreach ($test_results['results'] as $test_class => $result): ?>
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
                                <?php echo $test_class; ?>
                            </h2>
                            <div class="flex space-x-4 text-sm">
                                    <span class="text-blue-600 dark:text-blue-400">
                                        Assertions: <?php echo $result['assertions']; ?>
                                    </span>
                                <span class="text-red-600 dark:text-red-400">
                                        Échecs: <?php echo $result['failures']; ?>
                                    </span>
                                <span class="text-green-600 dark:text-green-400">
                                        Réussite: <?php echo $result['success_rate']; ?>%
                                    </span>
                            </div>
                        </div>

                        <!-- Barre de progression -->
                        <div class="w-full bg-gray-200 rounded-full h-2 mb-4">
                            <div class="bg-green-600 h-2 rounded-full"
                                 style="width: <?php echo $result['success_rate']; ?>%"></div>
                        </div>

                        <!-- Détails des échecs -->
                        <?php if (!empty($result['details'])): ?>
                            <div class="mt-4">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                                    Détails des échecs
                                </h3>
                                <?php foreach ($result['details'] as $detail): ?>
                                    <div class="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded p-3 mb-2">
                                        <div class="text-red-800 dark:text-red-200 font-medium">
                                            <?php echo htmlspecialchars($detail['message']); ?>
                                        </div>
                                        <?php if (isset($detail['trace']) && !empty($detail['trace'])): ?>
                                            <div class="text-red-600 dark:text-red-400 text-sm mt-1">
                                                Fichier: <?php echo htmlspecialchars($detail['trace'][0]['file'] ?? 'Inconnu'); ?>
                                                Ligne: <?php echo htmlspecialchars($detail['trace'][0]['line'] ?? 'Inconnue'); ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- Actions -->
            <div class="mt-8 flex justify-between items-center">
                <div class="text-sm text-gray-500 dark:text-gray-400">
                    Tests exécutés le <?php echo $test_results['timestamp']; ?>
                </div>
                <div class="flex space-x-2">
                    <a href="?<?php echo http_build_query(array_merge($_GET, ['format' => 'json'])); ?>"
                       class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700">
                        Export JSON
                    </a>
                    <button onclick="window.location.reload()"
                            class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                        Relancer les tests
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>

<?php
$pageContent = ob_get_clean();
include __DIR__ . '/../layout.php';
?>
