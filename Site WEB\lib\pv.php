<?php
require_once(__DIR__ . '/../config/database.php');

class PV
{
    public static function create($numero, $affaire_id, $contenu, $created_by)
    {
        $stmt = self::getDb()->prepare("
            INSERT INTO pv (numero, affaire_id, contenu, date_creation, created_by)
            VALUES (?, ?, ?, CURRENT_DATE, ?)
        ");
        return $stmt->execute([$numero, $affaire_id, $contenu, $created_by]);
    }

    // Créer un nouveau PV

    private static function getDb()
    {
        return Database::getInstance()->getConnection();
    }

    // Récupérer un PV par son ID

    public static function getById($id)
    {
        $stmt = self::getDb()->prepare("
            SELECT p.*, u.username AS created_by_name, a.numero AS numero_affaire
            FROM pv p
            LEFT JOIN users u ON p.created_by = u.id
            LEFT JOIN affaires a ON p.affaire_id = a.id
            WHERE p.id = ?
        ");
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    // Mettre à jour un PV
    public static function update($id, $numero, $contenu, $statut)
    {
        $stmt = self::getDb()->prepare("
            UPDATE pv
            SET numero = ?, contenu = ?, statut = ?
            WHERE id = ?
        ");
        return $stmt->execute([$numero, $contenu, $statut, $id]);
    }

    // Supprimer un PV
    public static function delete($id)
    {
        $stmt = self::getDb()->prepare("DELETE FROM pv WHERE id = ?");
        return $stmt->execute([$id]);
    }

    // Récupérer tous les PV d'une affaire
    public static function getByAffaireId($affaire_id)
    {
        $stmt = self::getDb()->prepare("
            SELECT p.*, u.username AS created_by_name
            FROM pv p
            LEFT JOIN users u ON p.created_by = u.id
            WHERE p.affaire_id = ?
            ORDER BY p.date_creation DESC
        ");
        $stmt->execute([$affaire_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Mettre à jour le statut d'un PV
    public static function updateStatut($id, $statut)
    {
        $stmt = self::getDb()->prepare("UPDATE pv SET statut = ? WHERE id = ?");
        return $stmt->execute([$statut, $id]);
    }

    // Vérifier si un numéro de PV existe déjà
    public static function numeroExists($numero)
    {
        $stmt = self::getDb()->prepare("SELECT COUNT(*) FROM pv WHERE numero = ?");
        $stmt->execute([$numero]);
        return $stmt->fetchColumn() > 0;
    }

    // Récupérer tous les PV
    public static function getAll($limit = null, $offset = 0)
    {
        $sql = "
            SELECT p.*, u.username AS created_by_name, a.numero AS numero_affaire
            FROM pv p
            LEFT JOIN users u ON p.created_by = u.id
            LEFT JOIN affaires a ON p.affaire_id = a.id
            ORDER BY p.date_creation DESC
        ";
        if ($limit !== null) {
            $sql .= " LIMIT ? OFFSET ?";
        }
        $stmt = self::getDb()->prepare($sql);
        if ($limit !== null) {
            $stmt->execute([$limit, $offset]);
        } else {
            $stmt->execute();
        }
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Créer un PV avec essai_id
     */
    public static function createWithEssai($numero, $essai_id, $contenu, $statut, $created_by)
    {
        // Récupérer l'affaire_id à partir de l'essai_id en utilisant la classe Essai
        require_once(__DIR__ . '/essais.php');
        $affaire_id = Essai::getAffaireId($essai_id);

        if (!$affaire_id) {
            return false;
        }

        $stmt = self::getDb()->prepare("
            INSERT INTO pv (numero, affaire_id, essai_id, contenu, date_creation, statut, created_by, is_synthetic)
            VALUES (?, ?, ?, ?, CURRENT_DATE, ?, ?, 1)
        ");
        return $stmt->execute([$numero, $affaire_id, $essai_id, $contenu, $statut, $created_by]);
    }

    /**
     * Supprimer tous les PV synthétiques
     */
    public static function deleteSynthetic()
    {
        $stmt = self::getDb()->prepare("DELETE FROM pv WHERE is_synthetic = 1");
        $stmt->execute();
        return $stmt->rowCount();
    }

    /**
     * Supprimer tous les PV
     */
    public static function deleteAll()
    {
        $stmt = self::getDb()->prepare("DELETE FROM pv");
        $stmt->execute();
        $count = $stmt->rowCount();

        // Réinitialiser l'auto-increment
        self::getDb()->exec("ALTER TABLE pv AUTO_INCREMENT = 1");

        return $count;
    }
}

class PVAttachment
{
    public static function create($pv_id, $filename, $filepath, $mime_type)
    {
        $stmt = self::getDb()->prepare("
            INSERT INTO pv_attachments (pv_id, filename, filepath, mime_type)
            VALUES (?, ?, ?, ?)
        ");
        return $stmt->execute([$pv_id, $filename, $filepath, $mime_type]);
    }

    // Ajouter une pièce jointe

    private static function getDb()
    {
        return Database::getInstance()->getConnection();
    }

    // Récupérer une pièce jointe par son ID

    public static function delete($id)
    {
        $attachment = self::getById($id);
        if ($attachment && file_exists($attachment['filepath'])) {
            unlink($attachment['filepath']);
        }
        $stmt = self::getDb()->prepare("DELETE FROM pv_attachments WHERE id = ?");
        return $stmt->execute([$id]);
    }

    // Récupérer toutes les pièces jointes d'un PV

    public static function getById($id)
    {
        $stmt = self::getDb()->prepare("
            SELECT * FROM pv_attachments WHERE id = ?
        ");
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    // Supprimer une pièce jointe

    public static function deleteByPVId($pv_id)
    {
        $attachments = self::getByPVId($pv_id);
        foreach ($attachments as $attachment) {
            if (file_exists($attachment['filepath'])) {
                unlink($attachment['filepath']);
            }
        }
        $stmt = self::getDb()->prepare("DELETE FROM pv_attachments WHERE pv_id = ?");
        return $stmt->execute([$pv_id]);
    }

    // Supprimer toutes les pièces jointes d'un PV

    public static function getByPVId($pv_id)
    {
        $stmt = self::getDb()->prepare("
            SELECT * FROM pv_attachments
            WHERE pv_id = ?
            ORDER BY uploaded_at DESC
        ");
        $stmt->execute([$pv_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
