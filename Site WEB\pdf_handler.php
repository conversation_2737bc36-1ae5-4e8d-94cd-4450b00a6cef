<?php
session_start();
require_once(__DIR__ . '/lib/pdf_generator.php');

if (!isset($_SESSION['user'])) {
    header('Location: /auth/login.php');
    exit;
}

// Traitement des actions GET (téléchargement)
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    $action = $_GET['action'] ?? '';

    switch ($action) {
        case 'download':
            $filename = $_GET['filename'] ?? '';
            if (!empty($filename)) {
                $pdfPath = __DIR__ . '/pdf_exports/' . basename($filename);
                if (file_exists($pdfPath)) {
                    header('Content-Type: application/pdf');
                    header('Content-Disposition: attachment; filename="' . basename($filename) . '"');
                    header('Content-Length: ' . filesize($pdfPath));
                    header('Cache-Control: no-cache, must-revalidate');
                    header('Expires: 0');
                    readfile($pdfPath);
                    exit;
                } else {
                    header('HTTP/1.0 404 Not Found');
                    echo 'Fichier PDF introuvable';
                    exit;
                }
            }
            break;

        case 'generate':
            $pvId = $_GET['pv_id'] ?? '';
            if (!empty($pvId)) {
                $result = PDFGenerator::generatePVPDF($pvId);
                if ($result['success']) {
                    // Rediriger vers le téléchargement du PDF généré
                    header('Location: ?action=download&filename=' . urlencode($result['filename']));
                    exit;
                } else {
                    header('Location: ' . $_SERVER['HTTP_REFERER'] . '?error=' . urlencode($result['message']));
                    exit;
                }
            }
            break;
    }
}

// Traitement des actions POST (suppression)
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    switch ($action) {
        case 'delete':
            $filename = $_POST['filename'] ?? '';
            if (!empty($filename)) {
                $pdfPath = __DIR__ . '/pdf_exports/' . basename($filename);
                if (file_exists($pdfPath)) {
                    if (unlink($pdfPath)) {
                        header('Location: ' . $_SERVER['HTTP_REFERER'] . '?success=' . urlencode('Fichier PDF supprimé avec succès'));
                        exit;
                    } else {
                        header('Location: ' . $_SERVER['HTTP_REFERER'] . '?error=' . urlencode('Erreur lors de la suppression du fichier'));
                        exit;
                    }
                } else {
                    header('Location: ' . $_SERVER['HTTP_REFERER'] . '?error=' . urlencode('Fichier PDF introuvable'));
                    exit;
                }
            }
            break;
    }
}

// Si aucune action valide, rediriger vers la page précédente
header('Location: ' . ($_SERVER['HTTP_REFERER'] ?? '/index.php'));
exit;
?>
