# Navigation dans l'Interface

## Structure Générale de l'Interface

FluidMotion Labs utilise une interface moderne et intuitive organisée en plusieurs zones principales :

### Zones de l'Interface

```
┌─────────────────────────────────────────────────────────┐
│                    Barre de Navigation                  │
├─────────────────────────────────────────────────────────┤
│                                                         │
│                   Zone de Contenu                      │
│                     Principal                          │
│                                                         │
├─────────────────────────────────────────────────────────┤
│                  Bouton Retour en Haut                 │
└─────────────────────────────────────────────────────────┘
```

## Barre de Navigation Principale

### Logo et Titre
- **FluidMotion Labs** : Clic pour retourner à l'accueil
- Toujours visible en haut à gauche

### Onglets de Navigation

#### Pour les Opérateurs (4 onglets)
1. **🏠 Accueil** - Tableau de bord et statistiques
2. **📁 Affaires** - Gestion des dossiers clients
3. **🔬 Essais** - Planification et exécution des tests
4. **📄 PV** - Génération des procès-verbaux

#### Pour les Contrôleurs (5 onglets)
1. **🏠 Accueil** - Tableau de bord et statistiques
2. **📁 Affaires** - Gestion des dossiers clients
3. **🔬 Essais** - Planification et exécution des tests
4. **📄 PV** - Génération des procès-verbaux
5. **💾 Sauvegarde** - Outils de sauvegarde et restauration

### Zone Utilisateur (Coin Supérieur Droit)

#### Bouton Thème
- **🌙/☀️ Icône thème** : Basculer entre mode clair et sombre
- Animation de rotation au survol
- Préférence sauvegardée automatiquement

#### Menu Utilisateur
- **👤 Avatar utilisateur** : Accès au menu déroulant

##### Menu Opérateur
- Nom d'utilisateur affiché
- "Déconnexion"

##### Menu Contrôleur
- Nom d'utilisateur affiché
- Badge "Contrôleur • Outils avancés disponibles"
- "⚡ Tests & Diagnostic"
- "🌐 Générateur de Données"
- Séparateur
- "Déconnexion"

## Navigation par Raccourcis Clavier

### Raccourcis Principaux

| Combinaison | Action | Disponible pour |
|-------------|--------|-----------------|
| <kbd>Alt</kbd> + <kbd>H</kbd> | Accueil | Tous |
| <kbd>Alt</kbd> + <kbd>A</kbd> | Affaires | Tous |
| <kbd>Alt</kbd> + <kbd>E</kbd> | Essais | Tous |
| <kbd>Alt</kbd> + <kbd>P</kbd> | PV | Tous |
| <kbd>Alt</kbd> + <kbd>T</kbd> | Tests & Diagnostic | Contrôleur |
| <kbd>Alt</kbd> + <kbd>G</kbd> | Générateur de Données | Contrôleur |

### Utilisation des Raccourcis

<div className="info-box">

**Conseils d'utilisation :**
- Maintenez <kbd>Alt</kbd> enfoncé puis appuyez sur la lettre
- Fonctionne depuis n'importe quelle page
- Particulièrement utile pour la navigation rapide
- Améliore significativement la productivité

</div>

## Navigation dans les Modules

### Structure des Pages de Module

Chaque module suit une structure cohérente :

#### En-tête de Module
- **Titre du module** (ex: "Gestion des Affaires")
- **Boutons d'action** (Nouveau, Filtres, etc.)
- **Informations contextuelles** (nombre d'éléments, pagination)

#### Zone de Filtres
- **Barre de recherche** par critères spécifiques
- **Filtres déroulants** (statut, type, etc.)
- **Boutons** "Filtrer" et "Réinitialiser"

#### Tableau de Données
- **En-têtes de colonnes** avec tri possible
- **Lignes de données** avec survol interactif
- **Actions par ligne** (Modifier, Supprimer, Voir)

#### Pagination
- **Informations** sur les résultats affichés
- **Navigation** entre les pages
- **Contrôle** du nombre d'éléments par page

## Éléments Interactifs

### Boutons et Actions

#### Types de Boutons
- **Primaire** (bleu) : Actions principales (Créer, Enregistrer)
- **Secondaire** (gris) : Actions secondaires (Annuler, Réinitialiser)
- **Succès** (vert) : Actions positives (Voir, Valider)
- **Danger** (rouge) : Actions destructives (Supprimer)
- **Avertissement** (orange) : Actions d'attention

#### États des Boutons
- **Normal** : État par défaut
- **Survol** : Changement de couleur
- **Actif** : Lors du clic
- **Désactivé** : Grisé si non disponible

### Modales et Formulaires

#### Ouverture des Modales
- Clic sur boutons d'action (Nouveau, Modifier)
- Ouverture avec animation fluide
- Arrière-plan assombri

#### Navigation dans les Modales
- **Onglets** pour les formulaires complexes
- **Boutons** de validation et annulation
- **Fermeture** par croix ou échappement

### Indicateurs Visuels

#### Statuts Colorés
- <span className="status-en-cours">En cours</span> : Bleu
- <span className="status-termine">Terminé</span> : Vert
- <span className="status-annule">Annulé</span> : Rouge
- <span className="status-en-attente">En attente</span> : Orange

#### Badges et Étiquettes
- **Nombre d'essais** par affaire
- **Tags** colorés pour l'organisation
- **Indicateurs** de progression

## Responsive Design

### Adaptation Mobile

#### Écrans Moyens (Tablettes)
- Navigation condensée
- Colonnes réorganisées
- Boutons adaptés au tactile

#### Écrans Petits (Mobiles)
- Menu hamburger
- Navigation verticale
- Tableaux défilants horizontalement

### Optimisations Tactiles
- **Zones de clic** agrandies
- **Gestes** de balayage supportés
- **Zoom** adaptatif

## Bouton Retour en Haut

### Fonctionnement
- **Apparition** automatique après défilement
- **Position** fixe en bas à droite
- **Animation** de défilement fluide
- **Masquage** automatique en haut de page

### Utilisation
- Clic pour retour instantané en haut
- Particulièrement utile sur les longues listes
- Animation fluide pour une meilleure expérience

## Conseils de Navigation

### Efficacité Maximale

<div className="success-box">

**Bonnes pratiques :**
1. **Mémorisez** les raccourcis clavier fréquents
2. **Utilisez** les filtres pour réduire les listes
3. **Exploitez** la pagination pour de meilleures performances
4. **Personnalisez** le thème selon vos préférences

</div>

### Navigation Contextuelle

#### Fil d'Ariane
- Visible dans les vues détaillées
- Permet de revenir aux niveaux supérieurs
- Indique la position actuelle

#### Liens Contextuels
- **"Voir"** : Accès aux vues détaillées
- **Numéros d'affaire** : Liens directs
- **Références croisées** entre modules

## Gestion des Erreurs de Navigation

### Erreurs Courantes

#### Page Non Trouvée
- Redirection automatique vers l'accueil
- Message d'information utilisateur

#### Accès Non Autorisé
- Redirection selon le rôle utilisateur
- Message d'explication des permissions

#### Problèmes de Session
- Redirection vers la page de connexion
- Sauvegarde automatique des données en cours

---

:::tip Navigation Rapide
Utilisez <kbd>Alt</kbd> + <kbd>H</kbd> pour revenir rapidement à l'accueil depuis n'importe où.
:::

:::info Prochaine Étape
Maintenant que vous maîtrisez la navigation, explorez [le tableau de bord](../user-interface/dashboard) en détail.
:::
