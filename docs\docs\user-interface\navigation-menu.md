# Menu de Navigation

## Structure du Menu Principal

Le menu de navigation de FluidMotion Labs est conçu pour offrir un accès rapide et intuitif à toutes les fonctionnalités du système. Il s'adapte automatiquement selon le rôle de l'utilisateur connecté.

## Barre de Navigation Supérieure

### Zone Gauche - Identité

#### Logo et Titre
- **FluidMotion Labs** : Logo et nom de l'application
- **Fonction** : Clic pour retour à l'accueil
- **Position** : Coin supérieur gauche
- **Toujours visible** : Oui

### Zone Centrale - Navigation Principale

#### Menu Opérateur (4 onglets)

<div className="role-operateur">

**🏠 Accueil**
- **Fonction** : Tableau de bord principal
- **Raccourci** : <kbd>Alt</kbd> + <kbd>H</kbd>
- **Contenu** : Statistiques et activité récente

**📁 Affaires**
- **Fonction** : Gestion des dossiers clients
- **Raccourci** : <kbd>Alt</kbd> + <kbd>A</kbd>
- **Contenu** : CRUD complet des affaires

**🔬 Essais**
- **Fonction** : Gestion des tests
- **Raccourci** : <kbd>Alt</kbd> + <kbd>E</kbd>
- **Contenu** : Planification et exécution des essais

**📄 PV**
- **Fonction** : Procès-verbaux
- **Raccourci** : <kbd>Alt</kbd> + <kbd>P</kbd>
- **Contenu** : Génération et gestion des rapports

</div>

#### Menu Contrôleur (5 onglets)

<div className="role-controleur">

Tous les onglets opérateur **PLUS** :

**💾 Sauvegarde**
- **Fonction** : Gestion des sauvegardes
- **Raccourci** : Aucun par défaut
- **Contenu** : Backup/restore de la base de données
- **Accès** : Contrôleur uniquement

</div>

### Zone Droite - Utilisateur et Paramètres

#### Bouton Thème
- **Icône** : 🌙 (mode sombre) / ☀️ (mode clair)
- **Fonction** : Basculer entre thèmes
- **Animation** : Rotation au survol
- **Sauvegarde** : Préférence automatique

#### Menu Utilisateur

##### Déclencheur
- **Icône** : 👤 Avatar utilisateur
- **Type** : Menu déroulant
- **Position** : Coin supérieur droit

##### Contenu Opérateur

<div className="role-operateur">

**Informations Utilisateur**
- Nom d'utilisateur affiché
- Rôle : "Opérateur" (implicite)

**Actions**
- "Déconnexion" : Fermeture de session

</div>

##### Contenu Contrôleur

<div className="role-controleur">

**Informations Utilisateur**
- Nom d'utilisateur affiché
- Badge : "Contrôleur • Outils avancés disponibles"

**Outils Avancés**
- "⚡ Tests & Diagnostic" : Outils système
- "🌐 Générateur de Données" : Création de données test

**Actions**
- Séparateur visuel
- "Déconnexion" : Fermeture de session

</div>

## Comportement Responsive

### Écrans Larges (Desktop)
- **Affichage** : Menu horizontal complet
- **Espacement** : Répartition équilibrée
- **Visibilité** : Tous les éléments visibles

### Écrans Moyens (Tablettes)
- **Adaptation** : Compression des espaces
- **Texte** : Réduction possible des libellés
- **Fonctionnalité** : Conservée intégralement

### Écrans Petits (Mobiles)
- **Menu hamburger** : Possible selon configuration
- **Navigation verticale** : Adaptation automatique
- **Priorité** : Fonctions essentielles d'abord

## États Visuels

### Onglet Actif
- **Couleur** : Mise en évidence (bleu par défaut)
- **Indicateur** : Soulignement ou fond coloré
- **Contraste** : Différenciation claire

### Onglet Inactif
- **Couleur** : Gris neutre
- **Survol** : Changement de couleur
- **Transition** : Animation fluide

### Onglet Désactivé
- **Apparence** : Grisé
- **Interaction** : Aucune
- **Cas d'usage** : Maintenance ou permissions

## Raccourcis Clavier

### Navigation Rapide

| Raccourci | Destination | Disponibilité |
|-----------|-------------|---------------|
| <kbd>Alt</kbd> + <kbd>H</kbd> | Accueil | Tous |
| <kbd>Alt</kbd> + <kbd>A</kbd> | Affaires | Tous |
| <kbd>Alt</kbd> + <kbd>E</kbd> | Essais | Tous |
| <kbd>Alt</kbd> + <kbd>P</kbd> | PV | Tous |
| <kbd>Alt</kbd> + <kbd>T</kbd> | Tests & Diagnostic | Contrôleur |
| <kbd>Alt</kbd> + <kbd>G</kbd> | Générateur | Contrôleur |

### Utilisation des Raccourcis

<div className="info-box">

**Conseils d'Efficacité**
- Maintenez <kbd>Alt</kbd> puis appuyez sur la lettre
- Fonctionne depuis n'importe quelle page
- Mémorisez les raccourcis fréquents
- Améliore significativement la productivité

</div>

## Indicateurs Contextuels

### Badges et Notifications
- **Nombre d'éléments** : Affichage possible sur les onglets
- **Alertes** : Indicateurs visuels d'attention
- **Statut** : Information sur l'état du système

### Feedback Visuel
- **Chargement** : Indicateurs de progression
- **Erreurs** : Signalement visuel des problèmes
- **Succès** : Confirmation des actions réussies

## Personnalisation

### Thèmes Disponibles
- **Clair** : Fond blanc, texte sombre
- **Sombre** : Fond sombre, texte clair
- **Auto** : Selon préférences système

### Préférences Utilisateur
- **Sauvegarde automatique** : Choix de thème conservé
- **Synchronisation** : Entre sessions et appareils
- **Réinitialisation** : Retour aux paramètres par défaut

## Accessibilité

### Navigation Clavier
- **Tab** : Navigation séquentielle
- **Entrée/Espace** : Activation des éléments
- **Échap** : Fermeture des menus déroulants

### Lecteurs d'Écran
- **Labels** : Descriptions appropriées
- **Rôles** : Sémantique HTML correcte
- **États** : Communication des changements

### Contraste et Lisibilité
- **Couleurs** : Respect des standards WCAG
- **Tailles** : Texte suffisamment grand
- **Espacement** : Zones de clic appropriées

## Bonnes Pratiques d'Utilisation

### Navigation Efficace

<div className="success-box">

**Recommandations**
1. **Mémorisez** les raccourcis clavier principaux
2. **Utilisez** le logo pour retourner à l'accueil
3. **Exploitez** le menu utilisateur pour les outils avancés
4. **Personnalisez** le thème selon votre environnement

</div>

### Workflow Optimisé
- **Démarrage** : Toujours par le tableau de bord
- **Navigation** : Utilisez les raccourcis pour les tâches répétitives
- **Finalisation** : Déconnexion propre en fin de session

---

:::tip Navigation Rapide
Les raccourcis clavier sont votre meilleur allié pour une navigation efficace. Commencez par mémoriser <kbd>Alt</kbd> + <kbd>H</kbd>, <kbd>Alt</kbd> + <kbd>A</kbd>, <kbd>Alt</kbd> + <kbd>E</kbd> et <kbd>Alt</kbd> + <kbd>P</kbd>.
:::

:::info Adaptation
Le menu s'adapte automatiquement à votre rôle. Les contrôleurs voient des options supplémentaires pour les fonctions avancées.
:::
