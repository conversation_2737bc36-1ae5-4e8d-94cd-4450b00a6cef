#include "Button.h"
#include <Arduino.h>
#include <ArduinoJson.h>

constexpr int BUTTON_PIN = 2;
constexpr unsigned long DEBOUNCE_DELAY = 50; // Min 50ms entre les lectures

unsigned long Button::lastDebounceTime = 0;
bool Button::lastButtonState = HIGH;
bool Button::buttonState = HIGH;

void Button::init() {
    pinMode(BUTTON_PIN, INPUT_PULLUP);
}

void Button::process() {
    bool reading = digitalRead(BUTTON_PIN);
    unsigned long currentTime = millis();
    if (reading != lastButtonState) {
        lastDebounceTime = currentTime;
    }
    if ((currentTime - lastDebounceTime) > DEBOUNCE_DELAY) {
        if (reading != buttonState) {
            buttonState = reading;
            if (buttonState == LOW) {
                // Détection de l'appui sur le bouton
                // Envoi d'une notification via Serial
                StaticJsonDocument<100> doc;
                doc["event"] = "button_press";
                doc["timestamp"] = millis();
                serializeJson(doc, Serial);
                Serial.println();
            }
        }
    }
    lastButtonState = reading;
}
