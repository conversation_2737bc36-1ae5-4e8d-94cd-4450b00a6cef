<?php
/**
 * Migration pour ajouter les colonnes is_synthetic
 * À exécuter une seule fois pour mettre à jour la base de données existante
 */

session_start();

// Vérifier que l'utilisateur est connecté et est un contrôleur
if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'controleur') {
    die('Accès refusé. Cette page nécessite un utilisateur contrôleur connecté.');
}

require_once(__DIR__ . '/config/database.php');

try {
    $db = Database::getInstance()->getConnection();

    echo "<h1>Migration des colonnes is_synthetic</h1>";
    echo "<p>Ajout des colonnes pour marquer les données synthétiques...</p>";

    // Liste des tables à modifier
    $tables = ['affaires', 'essais', 'courbes', 'pv', 'rendements'];

    foreach ($tables as $table) {
        echo "<p>Traitement de la table <strong>$table</strong>...</p>";

        try {
            // Vérifier si la colonne existe déjà
            $stmt = $db->prepare("SHOW COLUMNS FROM $table LIKE 'is_synthetic'");
            $stmt->execute();

            if ($stmt->rowCount() == 0) {
                // Ajouter la colonne
                $sql = "ALTER TABLE $table ADD COLUMN is_synthetic BOOLEAN DEFAULT FALSE";
                $db->exec($sql);
                echo "<span style='color: green;'>✓ Colonne is_synthetic ajoutée à $table</span><br>";

                // Ajouter l'index
                $index_sql = "CREATE INDEX idx_{$table}_synthetic ON $table (is_synthetic)";
                $db->exec($index_sql);
                echo "<span style='color: green;'>✓ Index ajouté pour $table.is_synthetic</span><br>";
            } else {
                echo "<span style='color: orange;'>⚠ Colonne is_synthetic existe déjà dans $table</span><br>";
            }

        } catch (Exception $e) {
            echo "<span style='color: red;'>✗ Erreur pour $table: " . $e->getMessage() . "</span><br>";
        }

        echo "<br>";
    }

    // Ajouter la colonne essai_id à la table PV si elle n'existe pas
    echo "<p>Vérification de la colonne essai_id dans la table pv...</p>";
    try {
        $stmt = $db->prepare("SHOW COLUMNS FROM pv LIKE 'essai_id'");
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            $db->exec("ALTER TABLE pv ADD COLUMN essai_id INT");
            echo "<span style='color: green;'>✓ Colonne essai_id ajoutée à pv</span><br>";

            // Ajouter la contrainte de clé étrangère
            try {
                $db->exec("ALTER TABLE pv ADD CONSTRAINT fk_pv_essai FOREIGN KEY (essai_id) REFERENCES essais(id) ON DELETE CASCADE");
                echo "<span style='color: green;'>✓ Contrainte de clé étrangère ajoutée</span><br>";
            } catch (Exception $e) {
                echo "<span style='color: orange;'>⚠ Contrainte de clé étrangère non ajoutée (peut-être existe déjà): " . $e->getMessage() . "</span><br>";
            }
        } else {
            echo "<span style='color: orange;'>⚠ Colonne essai_id existe déjà dans pv</span><br>";
        }
    } catch (Exception $e) {
        echo "<span style='color: red;'>✗ Erreur pour pv.essai_id: " . $e->getMessage() . "</span><br>";
    }

    // Corriger le champ mesure pour qu'il ait une valeur par défaut
    echo "<p>Correction du champ mesure dans la table courbes...</p>";
    try {
        $db->exec("ALTER TABLE courbes MODIFY COLUMN mesure FLOAT DEFAULT 0.0");
        echo "<span style='color: green;'>✓ Champ mesure modifié avec valeur par défaut</span><br>";
    } catch (Exception $e) {
        echo "<span style='color: orange;'>⚠ Erreur lors de la modification du champ mesure: " . $e->getMessage() . "</span><br>";
    }

    // Vérifier et corriger le champ affaire_id dans la table pv
    echo "<p>Vérification du champ affaire_id dans la table pv...</p>";
    try {
        // Vérifier si le champ affaire_id peut être NULL
        $stmt = $db->prepare("SHOW COLUMNS FROM pv LIKE 'affaire_id'");
        $stmt->execute();
        $column = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($column && $column['Null'] === 'NO') {
            // Rendre le champ nullable temporairement pour éviter les erreurs
            $db->exec("ALTER TABLE pv MODIFY COLUMN affaire_id INT NULL");
            echo "<span style='color: green;'>✓ Champ affaire_id rendu nullable</span><br>";
        } else {
            echo "<span style='color: orange;'>⚠ Champ affaire_id déjà nullable ou n'existe pas</span><br>";
        }
    } catch (Exception $e) {
        echo "<span style='color: orange;'>⚠ Erreur lors de la modification du champ affaire_id: " . $e->getMessage() . "</span><br>";
    }

    echo "<h2 style='color: green;'>Migration terminée avec succès !</h2>";
    echo "<p><a href='/data-generator.php'>Aller au générateur de données</a></p>";
    echo "<p><a href='/index.php'>Retour au tableau de bord</a></p>";

} catch (Exception $e) {
    echo "<h2 style='color: red;'>Erreur lors de la migration</h2>";
    echo "<p>Erreur: " . $e->getMessage() . "</p>";
}
?>
