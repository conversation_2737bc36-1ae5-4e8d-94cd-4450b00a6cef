// Validation en temps réel des champs
function validateField(field) {
    const value = field.value;
    const type = field.type;
    const name = field.name;
    let isValid = true;
    let message = '';

    // Réinitialiser les styles
    field.classList.remove('border-red-500', 'border-green-500');

    // Suppression du message d'erreur existant
    const existingError = field.parentElement.querySelector('.validation-message');
    if (existingError) {
        existingError.remove();
    }

    // Règles de validation spécifiques
    switch (name) {
        case 'numero':
            if (!value.trim()) {
                isValid = false;
                message = 'Le numéro est requis';
            } else if (!/^[A-Z0-9-]+$/i.test(value)) {
                isValid = false;
                message = 'Le numéro ne doit contenir que des lettres, des chiffres et des tirets';
            }
            break;
        case 'client':
            if (!value.trim()) {
                isValid = false;
                message = 'Le nom du client est requis';
            }
            break;
        case 'type':
            if (!value.trim()) {
                isValid = false;
                message = 'Le type est requis';
            }
            break;
        case 'parametre_theorique':
            if (!value.trim()) {
                isValid = false;
                message = 'Le paramètre théorique est requis';
            }
            break;
        case 'contenu':
            if (!value.trim()) {
                isValid = false;
                message = 'Le contenu est requis';
            }
            break;
    }

    // Appliquer les styles et messages
    if (!isValid) {
        field.classList.add('border-red-500');
        const errorDiv = document.createElement('div');
        errorDiv.className = 'validation-message text-red-500 text-sm mt-1';
        errorDiv.textContent = message;
        field.parentElement.appendChild(errorDiv);
    } else {
        field.classList.add('border-green-500');
    }

    return isValid;
}

// Autocomplétion pour les champs
function setupAutocomplete() {
    const clientFields = document.querySelectorAll('input[name="client"]');
    const typeFields = document.querySelectorAll('input[name="type"]');

    // Historique des valeurs (à remplacer par des appels API dans une vraie implémentation)
    const clientHistory = new Set();
    const typeHistory = new Set();

    // Création des datalists
    const clientDatalist = document.createElement('datalist');
    clientDatalist.id = 'client-suggestions';
    document.body.appendChild(clientDatalist);

    const typeDatalist = document.createElement('datalist');
    typeDatalist.id = 'type-suggestions';
    document.body.appendChild(typeDatalist);

    // Configuration de l'autocomplétion pour les champs client
    clientFields.forEach(field => {
        field.setAttribute('list', 'client-suggestions');
        field.addEventListener('input', () => {
            if (field.value.trim()) {
                clientHistory.add(field.value);
                updateDatalist(clientDatalist, Array.from(clientHistory));
            }
        });
    });

    // Configuration de l'autocomplétion pour les champs type
    typeFields.forEach(field => {
        field.setAttribute('list', 'type-suggestions');
        field.addEventListener('input', () => {
            if (field.value.trim()) {
                typeHistory.add(field.value);
                updateDatalist(typeDatalist, Array.from(typeHistory));
            }
        });
    });
}

// Mise à jour des suggestions d'autocomplétion
function updateDatalist(datalist, values) {
    datalist.innerHTML = '';
    values.forEach(value => {
        const option = document.createElement('option');
        option.value = value;
        datalist.appendChild(option);
    });
}

// Ajout des tooltips
function setupTooltips() {
    const fields = document.querySelectorAll('input, textarea, select');

    const tooltips = {
        numero: "Numéro unique d'identification",
        client: "Nom ou raison sociale du client",
        type: "Type d'essai à réaliser",
        parametre_theorique: "Valeur théorique attendue",
        description: "Description détaillée de l'affaire",
        contenu: "Contenu détaillé du procès-verbal",
        mode_operatoire: "Étapes détaillées de réalisation de l'essai",
        resultat: "Résultats obtenus lors de l'essai"
    };

    fields.forEach(field => {
        const name = field.getAttribute('name');
        if (tooltips[name]) {
            // Ajout de l'icone d'aide
            const helpIcon = document.createElement('div');
            helpIcon.innerHTML = `<svg class="w-4 h-4 text-gray-500 ml-2 cursor-help" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"></path>
            </svg>`;
            helpIcon.className = 'tooltip-icon inline-block';

            // Création du conteneur pour le champ et l'icone
            const container = document.createElement('div');
            container.className = 'flex items-center';
            field.parentNode.insertBefore(container, field);
            container.appendChild(field);
            container.appendChild(helpIcon);

            // Configuration du tooltip
            tippy(helpIcon, {
                content: tooltips[name],
                placement: 'right',
                theme: 'custom',
                animation: 'scale'
            });
        }
    });
}

// Initialisation au chargement de la page
document.addEventListener('DOMContentLoaded', () => {
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        const inputs = form.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            input.addEventListener('input', () => validateField(input));
            input.addEventListener('blur', () => validateField(input));
        });
    });

    setupAutocomplete();
    setupTooltips();
});