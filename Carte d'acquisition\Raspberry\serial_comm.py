import serial
import json
from datetime import datetime
import time

class SerialComm:
    def __init__(self, port, baudrate=9600):
        """Initialize serial communication with specified port and baudrate."""
        self.ser = serial.Serial(port, baudrate, timeout=1)
        time.sleep(2)  # Wait for <PERSON><PERSON><PERSON><PERSON> to reset

    def send_command(self, command):
        """Send a command and get parsed response."""
        self.ser.write(f"{command}\n".encode())
        response = self.ser.readline().decode().strip()
        try:
            return json.loads(response)
        except json.JSONDecodeError:
            return None

    def get_pressure(self):
        """Get pressure sensor reading with timestamp."""
        data = self.send_command("GET_PRESSURE")
        if data:
            return self._transform_sensor_data(data)
        return None

    def get_flow(self):
        """Get flow sensor reading with timestamp."""
        data = self.send_command("GET_FLOW")
        if data:
            return self._transform_sensor_data(data)
        return None

    def get_all_sensors(self):
        """Get all sensor readings with timestamp."""
        data = self.send_command("GET_ALL")
        if data:
            return {
                'pressure': data['pressure'],
                'flow': data['flow'],
                'timestamp': datetime.fromtimestamp(data['timestamp'] / 1000.0),
                'pressure_pascal': self._pressure_to_pascal(data['pressure']),
                'flow_lpm': self._flow_to_lpm(data['flow'])
            }
        return None

    def _transform_sensor_data(self, data):
        """Transform raw sensor data into meaningful units."""
        if 'sensor' not in data or 'value' not in data:
            return None
            
        result = {
            'raw_value': data['value'],
            'timestamp': datetime.fromtimestamp(data['timestamp'] / 1000.0)
        }
        
        if data['sensor'] == 'pressure':
            result['pascal'] = self._pressure_to_pascal(data['value'])
        elif data['sensor'] == 'flow':
            result['lpm'] = self._flow_to_lpm(data['value'])
            
        return result

    def _pressure_to_pascal(self, percentage):
        """Convert pressure percentage to Pascal."""
        # Assuming linear conversion and maximum pressure of 100kPa
        return percentage * 1000  # Convert percentage to Pascal

    def _flow_to_lpm(self, percentage):
        """Convert flow percentage to liters per minute."""
        # Assuming linear conversion and maximum flow of 10 L/min
        return percentage * 0.1  # Convert percentage to L/min

    def close(self):
        """Close the serial connection."""
        self.ser.close()
