<?php
session_start();
require_once(__DIR__ . '/lib/affaires.php');
require_once(__DIR__ . '/lib/tag.php');
require_once(__DIR__ . '/lib/performance.php');

if (!isset($_SESSION['user'])) {
    header('Location: /auth/login.php');
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'create':
                try {
                    if (Affaire::create(
                        $_POST['numero'],
                        $_POST['client'],
                        $_POST['description'],
                        array_map('trim', explode(',', $_POST['tags'])),
                        $_SESSION['user']['id']
                    )) {
                        $success = "Affaire créée avec succès";
                    } else {
                        $error = "Erreur lors de la création de l'affaire";
                    }
                } catch (Exception $e) {
                    $error = "Erreur lors de la création de l'affaire : " . $e->getMessage();
                }
                break;
            case 'update':
                try {
                    if (Affaire::update(
                        $_POST['id'],
                        $_POST['numero'],
                        $_POST['client'],
                        $_POST['description'],
                        $_POST['statut'],
                        array_map('trim', explode(',', $_POST['tags'])),
                        $_SESSION['user']['id']
                    )) {
                        $success = "Affaire mise à jour avec succès";
                    } else {
                        $error = "Erreur lors de la mise à jour de l'affaire";
                    }
                } catch (Exception $e) {
                    $error = "Erreur lors de la mise à jour de l'affaire : " . $e->getMessage();
                }
                break;
            case 'delete':
                try {
                    if (Affaire::delete($_POST['id'])) {
                        $success = "Affaire supprimée avec succès";
                    } else {
                        $error = "Erreur lors de la suppression de l'affaire";
                    }
                } catch (Exception $e) {
                    $error = "Erreur lors de la suppression de l'affaire : " . $e->getMessage();
                }
                break;
        }
    }
}

// Récupérer les paramètres de pagination et filtres
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$pageSize = isset($_GET['page_size']) ? min(200, max(10, (int)$_GET['page_size'])) : 50;

$filters = [
    'statut' => $_GET['statut'] ?? '',
    'client' => $_GET['client'] ?? '',
    'numero' => $_GET['numero'] ?? ''
];

// Utiliser la version optimisée avec pagination
$result = Performance::getAffairesOptimized($page, $pageSize, $filters);
$affaires = $result['data'];
$pagination = $result['pagination'];

ob_start();
?>

    <div class="mb-4 flex justify-between items-center">
        <h2 class="text-2xl font-bold dark:text-white">Gestion des Affaires</h2>
        <div class="flex space-x-2">
            <span class="text-sm text-gray-500 dark:text-gray-400 self-center">
                <?php echo $pagination['total_items']; ?> affaires - Page <?php echo $pagination['current_page']; ?>/<?php echo $pagination['total_pages']; ?>
            </span>
            <button data-modal-target="createAffaireModal" data-modal-toggle="createAffaireModal"
                    class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">
                Nouvelle Affaire
            </button>
        </div>
    </div>

    <!-- Filtres -->
    <div class="mb-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <input type="hidden" name="page" value="1">
            <div>
                <label for="numero" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Numéro</label>
                <input type="text" name="numero" id="numero" value="<?php echo htmlspecialchars($filters['numero']); ?>"
                       class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white"
                       placeholder="Rechercher par numéro...">
            </div>
            <div>
                <label for="client" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Client</label>
                <input type="text" name="client" id="client" value="<?php echo htmlspecialchars($filters['client']); ?>"
                       class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white"
                       placeholder="Rechercher par client...">
            </div>
            <div>
                <label for="statut" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Statut</label>
                <select name="statut" id="statut"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                    <option value="">Tous les statuts</option>
                    <option value="En cours" <?php echo $filters['statut'] === 'En cours' ? 'selected' : ''; ?>>En
                        cours
                    </option>
                    <option value="Terminé" <?php echo $filters['statut'] === 'Terminé' ? 'selected' : ''; ?>>Terminé
                    </option>
                    <option value="Annulé" <?php echo $filters['statut'] === 'Annulé' ? 'selected' : ''; ?>>Annulé
                    </option>
                </select>
            </div>
            <div class="flex items-end space-x-2">
                <button type="submit"
                        class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">
                    Filtrer
                </button>
                <a href="?page=1"
                   class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:ring-gray-300 font-medium rounded-lg text-sm px-5 py-2.5 border border-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-600 dark:focus:ring-gray-800">
                    Réinitialiser
                </a>
            </div>
        </form>
    </div>

<?php if (isset($success)): ?>
    <div id="alert-success"
         class="flex items-center p-4 mb-4 text-green-800 rounded-lg bg-green-50 dark:bg-gray-800 dark:text-green-400"
         role="alert">
        <svg class="flex-shrink-0 w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
             viewBox="0 0 20 20">
            <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/>
        </svg>
        <div class="ms-3 text-sm font-medium"><?php echo htmlspecialchars($success); ?></div>
        <button type="button"
                class="ms-auto -mx-1.5 -my-1.5 bg-green-50 text-green-500 rounded-lg focus:ring-2 focus:ring-green-400 p-1.5 hover:bg-green-200 inline-flex items-center justify-center h-8 w-8 dark:bg-gray-800 dark:text-green-400 dark:hover:bg-gray-700"
                data-dismiss-target="#alert-success" aria-label="Close">
            <span class="sr-only">Fermer</span>
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
            </svg>
        </button>
    </div>
<?php endif; ?>

<?php if (isset($error)): ?>
    <div id="alert-error"
         class="flex items-center p-4 mb-4 text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400"
         role="alert">
        <svg class="flex-shrink-0 w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
             viewBox="0 0 20 20">
            <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/>
        </svg>
        <div class="ms-3 text-sm font-medium"><?php echo htmlspecialchars($error); ?></div>
        <button type="button"
                class="ms-auto -mx-1.5 -my-1.5 bg-red-50 text-red-500 rounded-lg focus:ring-2 focus:ring-red-400 p-1.5 hover:bg-red-200 inline-flex items-center justify-center h-8 w-8 dark:bg-gray-800 dark:text-red-400 dark:hover:bg-gray-700"
                data-dismiss-target="#alert-error" aria-label="Close">
            <span class="sr-only">Fermer</span>
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
            </svg>
        </button>
    </div>
<?php endif; ?>

    <div class="relative overflow-x-auto shadow-md sm:rounded-lg">
        <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
            <tr>
                <th scope="col" class="px-6 py-3">Numéro</th>
                <th scope="col" class="px-6 py-3">Client</th>
                <th scope="col" class="px-6 py-3">Description</th>
                <th scope="col" class="px-6 py-3">Date de création</th>
                <th scope="col" class="px-6 py-3">Statut</th>
                <th scope="col" class="px-6 py-3">Actions</th>
            </tr>
            </thead>
            <tbody>
            <?php if (empty($affaires)): ?>
                <tr>
                    <td colspan="6" class="px-6 py-8 text-center text-gray-500 dark:text-gray-400">
                        Aucune affaire trouvée
                    </td>
                </tr>
            <?php else: ?>
                <?php foreach ($affaires as $affaire): ?>
                    <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                            <?php echo htmlspecialchars($affaire['numero']); ?>
                        </th>
                        <td class="px-6 py-4"><?php echo htmlspecialchars($affaire['client']); ?></td>
                        <td class="px-6 py-4">
                            <?php
                            $description = htmlspecialchars($affaire['description'] ?? '');
                            if (mb_strlen($description) > 35) {
                                $truncated = mb_substr($description, 0, 35);
                                $lastSpace = mb_strrpos($truncated, ' ');
                                if ($lastSpace !== false) {
                                    $truncated = mb_substr($truncated, 0, $lastSpace);
                                }
                                echo $truncated . '...';
                            } else {
                                echo $description;
                            }
                            ?>
                        </td>
                        <td class="px-6 py-4"><?php echo htmlspecialchars($affaire['date_creation']); ?></td>
                        <td class="px-6 py-4">
                            <span class="text-xs font-medium mr-2 px-2.5 py-0.5 rounded-md border
                                                <?php
                            if ($affaire['statut'] == 'Annulé') {
                                echo 'bg-red-100 text-red-800 dark:bg-gray-700 dark:text-red-400 border-red-100 dark:border-red-500';
                            } elseif ($affaire['statut'] == 'Terminé') {
                                echo 'bg-green-100 text-green-800 dark:bg-gray-700 dark:text-green-400 border-green-100 dark:border-green-500';
                            } elseif ($affaire['statut'] == 'En cours') {
                                echo 'bg-blue-100 text-blue-800 dark:bg-gray-700 dark:text-blue-400 border-blue-100 dark:border-blue-500';
                            }
                            ?>">
                                <?php echo htmlspecialchars($affaire['statut']); ?>
                            </span>
                            <?php if (isset($affaire['nombre_essais'])): ?>
                                <span class="text-xs text-gray-500 dark:text-gray-400 ml-2">
                                    (<?php echo $affaire['nombre_essais']; ?> essais)
                                </span>
                            <?php endif; ?>
                        </td>
                        <td class="px-6 py-4">
                            <button data-modal-target="editAffaireModal<?php echo $affaire['id']; ?>"
                                    data-modal-toggle="editAffaireModal<?php echo $affaire['id']; ?>"
                                    class="font-medium text-blue-600 dark:text-blue-500 hover:underline">Modifier
                            </button>
                            <button data-modal-target="deleteAffaireModal<?php echo $affaire['id']; ?>"
                                    data-modal-toggle="deleteAffaireModal<?php echo $affaire['id']; ?>"
                                    class="font-medium text-red-600 dark:text-red-500 hover:underline ms-3">Supprimer
                            </button>
                            <a href="/detail/affaire.php?id=<?php echo $affaire['id']; ?>"
                               class="font-medium text-green-600 dark:text-green-500 hover:underline ms-3">Voir</a>
                        </td>
                    </tr>
                <?php endforeach; ?>
            <?php endif; ?>
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
<?php if ($pagination['total_pages'] > 1): ?>
    <div class="flex items-center justify-between mt-4">
        <div class="text-sm text-gray-700 dark:text-gray-400">
            Affichage de <?php echo (($pagination['current_page'] - 1) * $pagination['page_size']) + 1; ?> à
            <?php echo min($pagination['current_page'] * $pagination['page_size'], $pagination['total_items']); ?>
            sur <?php echo $pagination['total_items']; ?> résultats
        </div>

        <nav aria-label="Pagination">
            <ul class="inline-flex -space-x-px text-sm">
                <?php if ($pagination['has_prev']): ?>
                    <li>
                        <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $pagination['current_page'] - 1])); ?>"
                           class="flex items-center justify-center px-3 h-8 ml-0 leading-tight text-gray-500 bg-white border border-gray-300 rounded-l-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                            Précédent
                        </a>
                    </li>
                <?php endif; ?>

                <?php
                $start = max(1, $pagination['current_page'] - 2);
                $end = min($pagination['total_pages'], $pagination['current_page'] + 2);

                for ($i = $start; $i <= $end; $i++):
                    ?>
                    <li>
                        <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"
                           class="flex items-center justify-center px-3 h-8 leading-tight <?php echo $i === $pagination['current_page'] ? 'text-blue-600 border border-gray-300 bg-blue-50 hover:bg-blue-100 hover:text-blue-700 dark:border-gray-700 dark:bg-gray-700 dark:text-white' : 'text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white'; ?>">
                            <?php echo $i; ?>
                        </a>
                    </li>
                <?php endfor; ?>

                <?php if ($pagination['has_next']): ?>
                    <li>
                        <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $pagination['current_page'] + 1])); ?>"
                           class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 rounded-r-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                            Suivant
                        </a>
                    </li>
                <?php endif; ?>
            </ul>
        </nav>
    </div>
<?php endif; ?>

    <div id="createAffaireModal" tabindex="-1" aria-hidden="true"
         class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
        <div class="relative p-4 w-full max-w-md max-h-full">
            <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                        Nouvelle Affaire
                    </h3>
                    <button type="button"
                            class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                            data-modal-hide="createAffaireModal">
                        <svg class="w-3 h-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                        </svg>
                        <span class="sr-only">Fermer</span>
                    </button>
                </div>
                <form class="p-4 md:p-5" method="POST">
                    <input type="hidden" name="action" value="create">
                    <div class="grid gap-4 mb-4">
                        <div>
                            <label for="numero" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Numéro</label>
                            <input type="text" name="numero" id="numero"
                                   class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                   required>
                        </div>
                        <div>
                            <label for="client" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Client</label>
                            <input type="text" name="client" id="client"
                                   class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                   required>
                        </div>
                        <div>
                            <label for="description"
                                   class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Description</label>
                            <textarea name="description" id="description" rows="4"
                                      class="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"></textarea>
                        </div>
                        <div>
                            <label for="tags" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Tags
                                (séparés par des virgules)</label>
                            <input type="text" name="tags" id="tags"
                                   class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                   placeholder="urgent, prioritaire, en attente...">
                        </div>
                    </div>
                    <button type="submit"
                            class="text-white inline-flex items-center bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                        Créer l'affaire
                    </button>
                </form>
            </div>
        </div>
    </div>

<?php foreach ($affaires as $affaire): ?>
    <div id="editAffaireModal<?php echo $affaire['id']; ?>" tabindex="-1" aria-hidden="true"
         class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
        <div class="relative p-4 w-full max-w-md max-h-full">
            <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                        Modifier l'affaire
                    </h3>
                    <button type="button"
                            class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                            data-modal-hide="editAffaireModal<?php echo $affaire['id']; ?>">
                        <svg class="w-3 h-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                        </svg>
                        <span class="sr-only">Fermer</span>
                    </button>
                </div>
                <form class="p-4 md:p-5" method="POST">
                    <input type="hidden" name="action" value="update">
                    <input type="hidden" name="id" value="<?php echo $affaire['id']; ?>">
                    <div class="grid gap-4 mb-4">
                        <div>
                            <label for="numero<?php echo $affaire['id']; ?>"
                                   class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Numéro</label>
                            <input type="text" name="numero" id="numero<?php echo $affaire['id']; ?>"
                                   value="<?php echo htmlspecialchars($affaire['numero']); ?>"
                                   class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                   required>
                        </div>
                        <div>
                            <label for="client<?php echo $affaire['id']; ?>"
                                   class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Client</label>
                            <input type="text" name="client" id="client<?php echo $affaire['id']; ?>"
                                   value="<?php echo htmlspecialchars($affaire['client']); ?>"
                                   class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                   required>
                        </div>
                        <div>
                            <label for="description<?php echo $affaire['id']; ?>"
                                   class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Description</label>
                            <textarea name="description" id="description<?php echo $affaire['id']; ?>" rows="4"
                                      class="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"><?php echo htmlspecialchars($affaire['description']); ?></textarea>
                        </div>
                        <div>
                            <label for="statut<?php echo $affaire['id']; ?>"
                                   class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Statut</label>
                            <select name="statut" id="statut<?php echo $affaire['id']; ?>"
                                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                <option value="En cours" <?php echo $affaire['statut'] === 'En cours' ? 'selected' : ''; ?>>
                                    En cours
                                </option>
                                <option value="Terminé" <?php echo $affaire['statut'] === 'Terminé' ? 'selected' : ''; ?>>
                                    Terminé
                                </option>
                                <option value="Annulé" <?php echo $affaire['statut'] === 'Annulé' ? 'selected' : ''; ?>>
                                    Annulé
                                </option>
                            </select>
                        </div>
                        <div>
                            <label for="tags<?php echo $affaire['id']; ?>"
                                   class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Tags (séparés
                                par des virgules)</label>
                            <?php
                            $tags = Tag::getByAffaireId($affaire['id']);
                            $tagString = implode(', ', array_map(function ($tag) {
                                return $tag['nom'];
                            }, $tags));
                            ?>
                            <input type="text" name="tags" id="tags<?php echo $affaire['id']; ?>"
                                   value="<?php echo htmlspecialchars($tagString); ?>"
                                   class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                   placeholder="urgent, prioritaire, en attente...">
                        </div>
                    </div>
                    <button type="submit"
                            class="text-white inline-flex items-center bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                        Enregistrer les modifications
                    </button>
                </form>
            </div>
        </div>
    </div>

    <div id="deleteAffaireModal<?php echo $affaire['id']; ?>" tabindex="-1" aria-hidden="true"
         class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
        <div class="relative p-4 w-full max-w-md max-h-full">
            <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                        Confirmer la suppression
                    </h3>
                    <button type="button"
                            class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                            data-modal-hide="deleteAffaireModal<?php echo $affaire['id']; ?>">
                        <svg class="w-3 h-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                        </svg>
                        <span class="sr-only">Fermer</span>
                    </button>
                </div>
                <div class="p-4 md:p-5">
                    <p class="text-gray-500 dark:text-gray-300">Êtes-vous sûr de vouloir supprimer cette affaire ? Cette
                        action est irréversible.</p>
                    <form method="POST" class="mt-5">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="id" value="<?php echo $affaire['id']; ?>">
                        <button type="submit"
                                class="text-white bg-red-600 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 dark:focus:ring-red-800 font-medium rounded-lg text-sm inline-flex items-center px-5 py-2.5 text-center me-2">
                            Oui, supprimer
                        </button>
                        <button type="button" data-modal-hide="deleteAffaireModal<?php echo $affaire['id']; ?>"
                                class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900 focus:z-10 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-500 dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-gray-600">
                            Non, annuler
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php endforeach; ?>

<?php
$pageContent = ob_get_clean();
include 'layout.php';
?>