# General
.DS_Store
Thumbs.db
*.log
*.swp
*.bak

# PlatformIO / Arduino
.pio
.pioenvs
.piolibdeps
.clang_complete
.gcc-flags.json
.cache

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
venv/
env/
ENV/
.env
.venv

# Web development
/node_modules
/npm-debug.log
/yarn-error.log
/public/hot
/public/storage
/storage/*.key
.env.local
.env.development.local
.env.test.local
.env.production.local
.phpunit.result.cache

# IDE / Editor specific files
.idea/
.vscode/
*.sublime-project
*.sublime-workspace
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.project
.classpath
.settings/

# Compiled files
*.com
*.class
*.dll
*.exe
*.o
*.so

# Logs and databases
*.log
*.sql
*.sqlite
*.sqlite3

# PDF generation (TCPDF might create temporary files)
temp/

# Vendor directories
vendor/
Site WEB/pdf_exports/
