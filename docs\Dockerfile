# Use the official Bun image
FROM oven/bun:1 AS base
WORKDIR /usr/src/app

# Install dependencies into temp directory for caching
FROM base AS install
RUN mkdir -p /temp/dev
COPY package.json bun.lock /temp/dev/
RUN cd /temp/dev && bun install --frozen-lockfile

# Install production dependencies (exclude devDependencies)
RUN mkdir -p /temp/prod
COPY package.json bun.lock /temp/prod/
RUN cd /temp/prod && bun install --frozen-lockfile --production

# Copy node_modules from temp directory, then copy all project files
FROM base AS prerelease
COPY --from=install /temp/dev/node_modules node_modules
COPY . .

# Build the Docusaurus site
ENV NODE_ENV=production
RUN bun run build

# Final image: only production dependencies and built site
FROM base AS release
WORKDIR /usr/src/app
COPY --from=install /temp/prod/node_modules node_modules
COPY --from=prerelease /usr/src/app/package.json .
COPY --from=prerelease /usr/src/app/bun.lock .
COPY --from=prerelease /usr/src/app/build ./build
COPY --from=prerelease /usr/src/app/docusaurus.config.ts .
# (Optional) Copy any other config or static files needed for serving

# Expose the port Docusaurus serves on
EXPOSE 3000/tcp

# Serve the built site
USER bun
ENTRYPOINT [ "bun", "run", "serve" ]