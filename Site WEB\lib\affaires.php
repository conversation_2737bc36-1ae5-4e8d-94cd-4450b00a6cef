<?php
require_once(__DIR__ . '/../config/database.php');
require_once(__DIR__ . '/../lib/objdiff.php');
require_once(__DIR__ . '/../lib/tag.php');

class Affaire
{
    /**
     * Mettre à jour une affaire existante
     *
     * @param int $id ID de l'affaire à mettre à jour
     * @param string $numero Numéro de l'affaire
     * @param string $client Nom du client
     * @param string $description Description de l'affaire
     * @param string $statut Statut de l'affaire
     * @param array $tags Liste des tags associés à l'affaire
     * @param int $modifie_par ID de l'utilisateur qui a modifié l'affaire
     * @return bool true en cas de succès, false en cas d'erreur
     * @throws Exception
     */
    public static function update(int $id, string $numero, string $client, string $description, string $statut, array $tags, int $modifie_par): bool
    {
        $oldAffaire = self::getById($id);

        Tag::dissocierTousTagsAffaire($id);
        foreach ($tags as $tagName) {
            if (!empty($tagName)) {
                if (!Tag::exists($tagName)) {
                    Tag::create($tagName);
                }
                $tag = Tag::getByNom($tagName);
                Tag::associerAffaire($id, $tag['id']);
            }
        }

        $stmt = self::getDb()->prepare("
            UPDATE affaires
            SET numero = ?, client = ?, description = ?, statut = ?
            WHERE id = ?
        ");
        $success = $stmt->execute([$numero, $client, $description, $statut, $id]);

        if (!$success) {
            throw new Exception("Erreur inconnue");
        }

        $newAffaire = self::getById($id);
        $changes = ObjDiff::compareForHistory($oldAffaire, $newAffaire, $id, $modifie_par);
        ObjDiff::saveToHistory($changes, 'affaires', $id, $modifie_par);

        return $success;
    }

    /**
     * Récupérer une affaire par son ID
     *
     * @param int $id ID de l'affaire à récupérer
     * @return array|false Détails de l'affaire ou false en cas d'erreur
     */
    public static function getById(int $id)
    {
        $stmt = self::getDb()->prepare("
            SELECT a.*, u.username AS created_by_username
            FROM affaires a
            LEFT JOIN users u ON a.created_by = u.id
            WHERE a.id = ?
        ");
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    private static function getDb()
    {
        return Database::getInstance()->getConnection();
    }

    /**
     * Créer une nouvelle affaire
     *
     * @param string $numero Numéro de l'affaire
     * @param string $client Nom du client
     * @param string $description Description de l'affaire
     * @param array $tags Liste des tags associés à l'affaire
     * @param int $created_by ID de l'utilisateur qui a créé l'affaire
     * @return int|false ID de l'affaire créée ou false en cas d'erreur
     * @throws Exception
     */
    public static function create(string $numero, string $client, string $description, array $tags, int $created_by)
    {
        $stmt = self::getDb()->prepare("
            INSERT INTO affaires (numero, client, description, date_creation, created_by)
            VALUES (?, ?, ?, now(), ?)
        ");
        $success = $stmt->execute([$numero, $client, $description, $created_by]);
        $affaire_id = $success ? self::getDb()->lastInsertId() : false;

        if (!$success) {
            throw new Exception("Erreur inconnue");
        }

        foreach ($tags as $tagName) {
            if (!empty($tagName)) {
                if (!Tag::exists($tagName)) {
                    Tag::create($tagName);
                }
                $tag = Tag::getByNom($tagName);
                Tag::associerAffaire($affaire_id, $tag['id']);
            }
        }

        $newAffaire = Affaire::getById($affaire_id);

        $changes = ObjDiff::compareForHistory([], $newAffaire, $affaire_id, $created_by);
        ObjDiff::saveToHistory($changes, 'affaires', $affaire_id, $created_by);

        return $affaire_id;
    }

    /** Supprimer une affaire
     *
     * @param int $id ID de l'affaire à supprimer
     * @return bool true en cas de succès, false en cas d'erreur
     */
    public static function delete(int $id): bool
    {
        $stmt = self::getDb()->prepare("DELETE FROM affaires WHERE id = ?");
        return $stmt->execute([$id]);
    }

    /**
     * Récupérer toutes les affaires
     *
     * @param int|null $limit Limite du nombre d'affaires à récupérer
     * @param int $offset Décalage pour la récupération des affaires
     * @return array Liste des affaires
     */
    public static function getAll(?int $limit = null, int $offset = 0): array
    {
        $sql = "
            SELECT a.*, u.username AS created_by_username
            FROM affaires a
            LEFT JOIN users u ON a.created_by = u.id
            ORDER BY date_creation DESC
        ";
        if ($limit !== null) {
            $sql .= " LIMIT ? OFFSET ?";
            $stmt = self::getDb()->prepare($sql);
            $stmt->execute([$limit, $offset]);
        } else {
            $stmt = self::getDb()->query($sql);
        }
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Récupérer une affaire par son numéro
     */
    public static function getByNumero($numero)
    {
        $stmt = self::getDb()->prepare("
            SELECT a.*, u.username AS created_by_username
            FROM affaires a
            LEFT JOIN users u ON a.created_by = u.id
            WHERE a.numero = ?
        ");
        $stmt->execute([$numero]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Mettre à jour le statut et la date de création d'une affaire
     */
    public static function updateStatusAndDate($id, $statut, $date_creation = null)
    {
        if ($date_creation) {
            $stmt = self::getDb()->prepare("
                UPDATE affaires
                SET statut = ?, date_creation = ?
                WHERE id = ?
            ");
            return $stmt->execute([$statut, $date_creation, $id]);
        } else {
            $stmt = self::getDb()->prepare("
                UPDATE affaires
                SET statut = ?
                WHERE id = ?
            ");
            return $stmt->execute([$statut, $id]);
        }
    }

    /**
     * Marquer une affaire comme synthétique
     */
    public static function markAsSynthetic($id)
    {
        $stmt = self::getDb()->prepare("UPDATE affaires SET is_synthetic = 1 WHERE id = ?");
        return $stmt->execute([$id]);
    }

    /**
     * Supprimer toutes les affaires synthétiques
     */
    public static function deleteSynthetic()
    {
        $stmt = self::getDb()->prepare("DELETE FROM affaires WHERE is_synthetic = 1");
        $stmt->execute();
        return $stmt->rowCount();
    }

    /**
     * Supprimer toutes les affaires
     */
    public static function deleteAll()
    {
        $stmt = self::getDb()->prepare("DELETE FROM affaires");
        $stmt->execute();
        $count = $stmt->rowCount();

        // Réinitialiser l'auto-increment
        self::getDb()->exec("ALTER TABLE affaires AUTO_INCREMENT = 1");

        return $count;
    }
}
