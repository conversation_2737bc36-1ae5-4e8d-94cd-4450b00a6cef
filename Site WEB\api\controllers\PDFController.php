<?php
require_once __DIR__ . '/../core/BaseController.php';
require_once __DIR__ . '/../../lib/pdf_generator.php';

class PDFController extends BaseController
{
    public function handle()
    {
        switch ($this->method) {
            case 'GET':
                return $this->handleGet();
            case 'POST':
                return $this->handlePost();
            case 'DELETE':
                return $this->handleDelete();
            default:
                return $this->error('Méthode non autorisée', 405);
        }
    }

    private function handleGet()
    {
        $action = $_GET['action'] ?? 'list';

        switch ($action) {
            case 'list':
                return $this->listPDFs();
            case 'download':
                return $this->downloadPDF();
            default:
                return $this->error('Action non reconnue', 400);
        }
    }

    /**
     * Lister tous les PDFs générés
     */
    private function listPDFs()
    {
        try {
            $pdfs = PDFGenerator::listGeneratedPDFs();

            return $this->json([
                'success' => true,
                'pdfs' => $pdfs,
                'count' => count($pdfs)
            ]);
        } catch (Exception $e) {
            return $this->error('Erreur lors de la récupération des PDFs: ' . $e->getMessage());
        }
    }

    /**
     * Télécharger un fichier PDF
     */
    private function downloadPDF()
    {
        try {
            $filename = $_GET['filename'] ?? '';

            if (empty($filename)) {
                return $this->error('Nom de fichier requis', 400);
            }

            $pdfPath = __DIR__ . '/../../pdf_exports/' . basename($filename);

            if (!file_exists($pdfPath)) {
                return $this->error('Fichier PDF introuvable', 404);
            }

            // Définir les en-têtes pour le téléchargement
            header('Content-Type: application/pdf');
            header('Content-Disposition: attachment; filename="' . basename($filename) . '"');
            header('Content-Length: ' . filesize($pdfPath));
            header('Cache-Control: no-cache, must-revalidate');
            header('Expires: 0');

            // Lire et envoyer le fichier
            readfile($pdfPath);
            exit;

        } catch (Exception $e) {
            return $this->error('Erreur lors du téléchargement: ' . $e->getMessage());
        }
    }

    private function handlePost()
    {
        $action = $this->data['action'] ?? '';

        switch ($action) {
            case 'generate':
                return $this->generatePDF();
            default:
                return $this->error('Action non reconnue', 400);
        }
    }

    /**
     * Générer un nouveau PDF pour un PV
     */
    private function generatePDF()
    {
        try {
            $pvId = $this->data['pv_id'] ?? null;

            if (!$pvId) {
                return $this->error('ID du PV requis', 400);
            }

            $result = PDFGenerator::generatePVPDF($pvId);

            if ($result['success']) {
                return $this->json($result, 201);
            } else {
                return $this->error($result['message'], 500);
            }
        } catch (Exception $e) {
            return $this->error('Erreur lors de la génération du PDF: ' . $e->getMessage());
        }
    }

    private function handleDelete()
    {
        return $this->deletePDF();
    }

    /**
     * Supprimer un fichier PDF
     */
    private function deletePDF()
    {
        try {
            $filename = $this->data['filename'] ?? '';

            if (empty($filename)) {
                return $this->error('Nom de fichier requis', 400);
            }

            $pdfPath = __DIR__ . '/../../pdf_exports/' . basename($filename);

            if (!file_exists($pdfPath)) {
                return $this->error('Fichier PDF introuvable', 404);
            }

            if (!unlink($pdfPath)) {
                return $this->error('Erreur lors de la suppression du fichier', 500);
            }

            return $this->json([
                'success' => true,
                'message' => 'Fichier PDF supprimé avec succès'
            ]);

        } catch (Exception $e) {
            return $this->error('Erreur lors de la suppression: ' . $e->getMessage());
        }
    }
}
