<?php
/**
 * Test de détection d'environnement
 */

session_start();

// Vérifier que l'utilisateur est connecté et est un contrôleur
if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'controleur') {
    die('Accès refusé. Ce script nécessite un utilisateur contrôleur connecté.');
}

// Fonction utilitaire pour afficher les valeurs de manière sécurisée
function safeDisplayValue($value)
{
    if ($value === null || $value === false) {
        return 'Non défini';
    }

    if (is_array($value)) {
        return 'Array: ' . json_encode($value, JSON_UNESCAPED_UNICODE);
    }

    if (is_object($value)) {
        return 'Object: ' . get_class($value);
    }

    return htmlspecialchars((string)$value);
}

// Fonction pour détecter l'environnement (copie de tests.php)
function detectEnvironment()
{
    // Méthode 1: Variables d'environnement Docker
    if (getenv('DOCKER_CONTAINER') !== false || getenv('HOSTNAME') !== false) {
        $hostname = getenv('HOSTNAME');
        if ($hostname && (strpos($hostname, 'docker') !== false || strlen($hostname) === 12)) {
            return 'Docker';
        }
    }

    // Méthode 2: Vérifier les variables d'environnement spécifiques à notre setup
    if (getenv('DB_HOST') === 'db' || getenv('APACHE_RUN_USER') === 'www-data') {
        return 'Docker';
    }

    // Méthode 3: Vérifier le fichier /.dockerenv (fallback)
    if (file_exists('/.dockerenv')) {
        return 'Docker';
    }

    // Méthode 4: Vérifier les processus (si proc est disponible)
    if (is_readable('/proc/1/cgroup')) {
        $cgroup = file_get_contents('/proc/1/cgroup');
        if (strpos($cgroup, 'docker') !== false || strpos($cgroup, 'containerd') !== false) {
            return 'Docker';
        }
    }

    // Méthode 5: Vérifier le répertoire de travail typique de Docker
    $cwd = getcwd();
    if (strpos($cwd, '/var/www/html') === 0 && file_exists('/var/www/html/docker-compose.yml')) {
        return 'Docker';
    }

    return 'Standard';
}

echo "<h1>Diagnostic de Détection d'Environnement</h1>";

$environment = detectEnvironment();
echo "<h2>Résultat: <span style='color: " . ($environment === 'Docker' ? 'green' : 'blue') . ";'>$environment</span></h2>";

echo "<h2>Tests de Détection Détaillés</h2>";

echo "<h3>1. Variables d'environnement Docker</h3>";
$dockerContainer = getenv('DOCKER_CONTAINER');
$hostname = getenv('HOSTNAME');
echo "<ul>";
echo "<li>DOCKER_CONTAINER: " . safeDisplayValue($dockerContainer) . "</li>";
echo "<li>HOSTNAME: " . safeDisplayValue($hostname) . "</li>";
if ($hostname && $hostname !== false) {
    echo "<li>HOSTNAME contient 'docker': " . (strpos($hostname, 'docker') !== false ? 'Oui' : 'Non') . "</li>";
    echo "<li>HOSTNAME longueur 12 caractères: " . (strlen($hostname) === 12 ? 'Oui' : 'Non') . "</li>";
}
echo "</ul>";

echo "<h3>2. Variables d'environnement spécifiques</h3>";
$dbHost = getenv('DB_HOST');
$apacheUser = getenv('APACHE_RUN_USER');
echo "<ul>";
echo "<li>DB_HOST: " . safeDisplayValue($dbHost) . "</li>";
echo "<li>DB_HOST === 'db': " . ($dbHost === 'db' ? 'Oui' : 'Non') . "</li>";
echo "<li>APACHE_RUN_USER: " . safeDisplayValue($apacheUser) . "</li>";
echo "<li>APACHE_RUN_USER === 'www-data': " . ($apacheUser === 'www-data' ? 'Oui' : 'Non') . "</li>";
echo "</ul>";

echo "<h3>3. Fichier /.dockerenv</h3>";
$dockerEnvExists = file_exists('/.dockerenv');
echo "<ul>";
echo "<li>Fichier /.dockerenv existe: " . ($dockerEnvExists ? 'Oui' : 'Non') . "</li>";
if ($dockerEnvExists) {
    echo "<li>Contenu: " . (filesize('/.dockerenv') > 0 ? file_get_contents('/.dockerenv') : 'Fichier vide') . "</li>";
}
echo "</ul>";

echo "<h3>4. Informations processus (/proc/1/cgroup)</h3>";
$cgroupPath = '/proc/1/cgroup';
echo "<ul>";
echo "<li>Fichier /proc/1/cgroup lisible: " . (is_readable($cgroupPath) ? 'Oui' : 'Non') . "</li>";
if (is_readable($cgroupPath)) {
    $cgroup = file_get_contents($cgroupPath);
    echo "<li>Contient 'docker': " . (strpos($cgroup, 'docker') !== false ? 'Oui' : 'Non') . "</li>";
    echo "<li>Contient 'containerd': " . (strpos($cgroup, 'containerd') !== false ? 'Oui' : 'Non') . "</li>";
    echo "<li>Contenu (premières lignes):<br><pre>" . htmlspecialchars(substr($cgroup, 0, 500)) . "</pre></li>";
}
echo "</ul>";

echo "<h3>5. Répertoire de travail</h3>";
$cwd = getcwd();
$dockerComposeExists = file_exists('/var/www/html/docker-compose.yml');
echo "<ul>";
echo "<li>Répertoire actuel: $cwd</li>";
echo "<li>Commence par '/var/www/html': " . (strpos($cwd, '/var/www/html') === 0 ? 'Oui' : 'Non') . "</li>";
echo "<li>docker-compose.yml existe: " . ($dockerComposeExists ? 'Oui' : 'Non') . "</li>";
echo "</ul>";

echo "<h3>6. Autres variables d'environnement utiles</h3>";
$envVars = ['PATH', 'USER', 'HOME', 'PWD', 'SHELL', 'TERM', 'LANG'];
echo "<ul>";
foreach ($envVars as $var) {
    $value = getenv($var);
    echo "<li>$var: " . safeDisplayValue($value) . "</li>";
}
echo "</ul>";

echo "<h3>7. Variables $_SERVER utiles</h3>";
$serverVars = ['SERVER_SOFTWARE', 'DOCUMENT_ROOT', 'SERVER_NAME', 'HTTP_HOST', 'REQUEST_URI'];
echo "<ul>";
foreach ($serverVars as $var) {
    $value = $_SERVER[$var] ?? null;
    echo "<li>$var: " . safeDisplayValue($value) . "</li>";
}
echo "</ul>";

echo "<h3>8. Informations système</h3>";
echo "<ul>";
echo "<li>PHP SAPI: " . php_sapi_name() . "</li>";
echo "<li>OS: " . PHP_OS . "</li>";
echo "<li>Architecture: " . php_uname('m') . "</li>";
echo "<li>Nom de machine: " . php_uname('n') . "</li>";
echo "<li>Version kernel: " . php_uname('r') . "</li>";
echo "</ul>";

echo "<h2>Recommandations</h2>";
if ($environment === 'Docker') {
    echo "<p style='color: green;'>✅ Environnement Docker correctement détecté.</p>";
    echo "<p>Les tests Docker sont appropriés pour cet environnement.</p>";
} else {
    echo "<p style='color: blue;'>ℹ️ Environnement Standard détecté.</p>";
    echo "<p>Si vous êtes dans Docker mais que ce n'est pas détecté, vérifiez:</p>";
    echo "<ul>";
    echo "<li>Les variables d'environnement DB_HOST, APACHE_RUN_USER</li>";
    echo "<li>La présence du fichier /.dockerenv</li>";
    echo "<li>Le contenu de /proc/1/cgroup</li>";
    echo "</ul>";
}

echo "<p><a href='/tests.php'>← Retour aux Tests</a></p>";
?>

<style>
    body {
        font-family: Arial, sans-serif;
        margin: 20px;
    }

    h1 {
        color: #2563eb;
    }

    h2 {
        color: #1e40af;
        border-bottom: 1px solid #e5e7eb;
        padding-bottom: 5px;
    }

    h3 {
        color: #374151;
    }

    ul {
        margin-left: 20px;
    }

    pre {
        background-color: #f3f4f6;
        padding: 10px;
        border-radius: 3px;
        overflow-x: auto;
    }

    a {
        color: #2563eb;
        text-decoration: none;
    }

    a:hover {
        text-decoration: underline;
    }
</style>
