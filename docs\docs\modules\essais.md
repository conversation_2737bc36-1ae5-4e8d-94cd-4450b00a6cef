# Gestion des Essais

## Vue d'ensemble

Le module **Essais** permet de planifier, exécuter et suivre tous les tests hydrauliques réalisés dans le cadre des affaires. Il offre un système de modèles réutilisables pour standardiser les procédures et garantir la cohérence des tests.

## Interface du Module

### En-tête du Module

#### Boutons d'Action Principaux
- **"Nouveau Modèle"** (gris) : Créer un modèle d'essai réutilisable
- **"Nouvel Essai"** (bleu) : Créer un essai spécifique

### Tableau des Essais

#### Colonnes Affichées

| Colonne | Description | Informations |
|---------|-------------|--------------|
| **ID** | Identifiant unique de l'essai | Numéro auto-généré |
| **Affaire** | Numéro de l'affaire associée | Lien vers l'affaire |
| **Type** | Type d'essai ou procédure | Défini par modèle ou manuel |
| **Date d'essai** | Date de planification/exécution | Format DD/MM/YYYY |
| **Statut** | État actuel de l'essai | Badge coloré |
| **Actions** | Boutons d'action | Modifier, Supprimer, Voir |

### Statuts des Essais

#### En attente
- <span className="status-en-attente">En attente</span>
- Essai planifié mais non démarré
- Couleur : Orange

#### En cours
- <span className="status-en-cours">En cours</span>
- Essai en cours d'exécution
- Couleur : Bleu

#### Terminé
- <span className="status-termine">Terminé</span>
- Essai complété avec résultats
- Couleur : Vert

#### Annulé
- <span className="status-annule">Annulé</span>
- Essai abandonné ou annulé
- Couleur : Rouge

## Modèles d'Essais

### Concept des Modèles

<div className="feature-highlight">

**Avantages des Modèles**
- **Standardisation** des procédures
- **Gain de temps** lors de la création
- **Cohérence** des paramètres
- **Réutilisation** pour des essais similaires

</div>

### Création d'un Modèle

#### Ouverture de la Modale

<div className="step-number">1</div>
<div className="step-content">

Cliquez sur **"Nouveau Modèle"** dans l'en-tête du module.

</div>

#### Formulaire de Modèle

<div className="step-number">2</div>
<div className="step-content">

**Nom du Modèle** (obligatoire)
- Nom descriptif du modèle
- Exemple : "Test de pression standard"

**Type d'Essai** (obligatoire)
- Catégorie ou nature du test
- Exemple : "Pression hydraulique"

**Paramètre Théorique** (obligatoire)
- Valeurs ou conditions théoriques
- Exemple : "Pression max: 350 bar"

**Mode Opératoire** (optionnel)
- Procédure détaillée d'exécution
- Instructions étape par étape

</div>

<div className="step-number">3</div>
<div className="step-content">

Cliquez sur **"Créer le modèle"** pour enregistrer.

</div>

## Création d'un Nouvel Essai

### Méthodes de Création

#### Option 1 : À partir d'un Modèle

<div className="step-number">1</div>
<div className="step-content">

**Sélection de l'Affaire**
- Choisissez l'affaire dans la liste déroulante
- Format affiché : "Numéro - Client"

</div>

<div className="step-number">2</div>
<div className="step-content">

**Choix du Modèle**
- Sélectionnez un modèle dans la liste
- Les champs sont pré-remplis automatiquement
- Les champs manuels sont masqués

</div>

#### Option 2 : Création Manuelle

<div className="step-number">1</div>
<div className="step-content">

**Sélection de l'Affaire**
- Choisissez l'affaire dans la liste déroulante

</div>

<div className="step-number">2</div>
<div className="step-content">

**Sélection "Créer un essai sans modèle"**
- Laissez le champ modèle vide
- Les champs manuels apparaissent

</div>

<div className="step-number">3</div>
<div className="step-content">

**Saisie Manuelle**
- **Type d'essai** : Nature du test
- **Paramètre théorique** : Conditions attendues
- **Mode opératoire** : Procédure à suivre

</div>

### Validation et Enregistrement

<div className="step-number">4</div>
<div className="step-content">

- Cliquez sur **"Créer l'essai"**
- L'essai est créé avec le statut "En attente"
- Date d'essai définie automatiquement à aujourd'hui

</div>

## Modification d'un Essai

### Accès à la Modification

#### Depuis la Liste
- Cliquez sur **"Modifier"** dans la ligne de l'essai
- Ouverture de la modale de modification

### Formulaire de Modification

#### Champs Modifiables

**Affaire Associée**
- Changement possible vers une autre affaire
- Liste déroulante avec toutes les affaires

**Type d'Essai**
- Modification du type ou de la catégorie
- Texte libre

**Paramètre Théorique**
- Mise à jour des conditions théoriques
- Texte libre

**Statut**
- Changement d'état selon l'avancement
- Liste déroulante avec tous les statuts

**Résultat**
- Saisie des résultats obtenus
- Zone de texte libre
- Obligatoire pour finaliser l'essai

**Mode Opératoire**
- Modification de la procédure
- Zone de texte libre

#### Workflow de Statut

```mermaid
graph LR
    A[En attente] --> B[En cours]
    B --> C[Terminé]
    A --> D[Annulé]
    B --> D[Annulé]
```

## Suivi et Exécution des Essais

### Cycle de Vie d'un Essai

#### Phase de Planification
1. **Création** avec statut "En attente"
2. **Définition** des paramètres et procédures
3. **Planification** de la date d'exécution

#### Phase d'Exécution
1. **Démarrage** : Changement vers "En cours"
2. **Exécution** selon le mode opératoire
3. **Mesures** et collecte de données
4. **Saisie** des résultats en temps réel

#### Phase de Finalisation
1. **Analyse** des résultats
2. **Validation** des mesures
3. **Finalisation** : Changement vers "Terminé"
4. **Documentation** complète

### Bonnes Pratiques d'Exécution

<div className="success-box">

**Recommandations :**
1. **Préparez** l'essai en vérifiant le mode opératoire
2. **Documentez** chaque étape importante
3. **Mesurez** avec précision selon les paramètres
4. **Validez** les résultats avant finalisation
5. **Archivez** les données de mesure

</div>

## Gestion des Résultats

### Saisie des Résultats

#### Format Recommandé
- **Valeurs mesurées** avec unités
- **Conditions d'essai** réelles
- **Observations** particulières
- **Écarts** par rapport aux paramètres théoriques

#### Exemple de Résultat
```
Pression maximale atteinte : 348 bar
Température d'essai : 22°C
Débit mesuré : 45 L/min
Observation : Légère fuite détectée au niveau du joint principal
Conformité : Conforme aux spécifications
```

### Validation des Résultats

#### Critères de Validation
- **Cohérence** avec les paramètres théoriques
- **Précision** des mesures
- **Complétude** des informations
- **Traçabilité** des conditions d'essai

## Suppression d'un Essai

### Processus de Suppression

<div className="warning-box">

**⚠️ Attention : Action Irréversible**

La suppression d'un essai entraîne :
- Perte des résultats et mesures
- Suppression des données de courbes associées
- Impact sur les statistiques de l'affaire
- Perte de la traçabilité

</div>

### Étapes de Suppression

<div className="step-number">1</div>
<div className="step-content">

**Confirmation**
- Cliquez sur "Supprimer" dans la ligne de l'essai
- Confirmez dans la modale d'avertissement

</div>

<div className="step-number">2</div>
<div className="step-content">

**Exécution**
- Suppression immédiate et définitive
- Mise à jour des statistiques

</div>

## Intégration avec les Autres Modules

### Lien avec les Affaires
- **Association obligatoire** à une affaire
- **Suivi** de l'avancement global
- **Impact** sur les statistiques d'affaire

### Lien avec les PV
- **Référencement** dans les procès-verbaux
- **Inclusion** des résultats dans les rapports
- **Traçabilité** complète

### Lien avec les Courbes
- **Génération** de données de mesure
- **Analyse** graphique des résultats
- **Archivage** des données brutes

---

:::tip Modèles Efficaces
Créez des modèles pour vos types d'essais récurrents afin de gagner du temps et d'assurer la cohérence.
:::

:::warning Résultats
Saisissez les résultats immédiatement après l'essai pour éviter les oublis et garantir la précision des données.
:::
