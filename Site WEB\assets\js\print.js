/**
 * FluidMotion Labs - Print Functionality
 * Handles browser-based printing with optimized layouts
 */

class PrintManager {
    constructor() {
        this.printStyles = null;
        this.originalContent = null;
        this.init();
    }

    init() {
        // Load print CSS if not already loaded
        this.loadPrintStyles();

        // Add print event listeners
        this.addEventListeners();
    }

    loadPrintStyles() {
        if (!document.querySelector('link[href*="print.css"]')) {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.type = 'text/css';
            link.href = '/assets/css/print.css';
            link.media = 'print';
            document.head.appendChild(link);
        }
    }

    addEventListeners() {
        // Listen for print buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.print-btn, [data-print]')) {
                e.preventDefault();
                const printType = e.target.dataset.print || 'current';
                this.handlePrint(printType, e.target);
            }
        });

        // Listen for keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                this.handlePrint('current');
            }
        });

        // Before print event
        window.addEventListener('beforeprint', () => {
            this.preparePrint();
        });

        // After print event
        window.addEventListener('afterprint', () => {
            this.restoreAfterPrint();
        });
    }

    async handlePrint(type, element = null) {
        try {
            switch (type) {
                case 'pv':
                    await this.printPV(element);
                    break;
                case 'essai':
                    await this.printEssai(element);
                    break;
                case 'affaire':
                    await this.printAffaire(element);
                    break;
                case 'courbes':
                    await this.printCourbes(element);
                    break;
                case 'current':
                default:
                    this.printCurrent();
                    break;
            }
        } catch (error) {
            console.error('Erreur lors de l\'impression:', error);
            this.showError('Erreur lors de la préparation de l\'impression');
        }
    }

    async printPV(element) {
        const pvId = element.dataset.pvId || this.extractIdFromUrl();
        if (!pvId) {
            this.showError('ID du PV non trouvé');
            return;
        }

        // Fetch PV data
        const pvData = await this.fetchPVData(pvId);
        if (!pvData) {
            this.showError('Impossible de charger les données du PV');
            return;
        }

        // Create print-optimized content
        const printContent = this.createPVPrintContent(pvData);
        this.openPrintWindow(printContent, 'PV-' + pvData.numero);
    }

    async printEssai(element) {
        const essaiId = element.dataset.essaiId || this.extractIdFromUrl();
        if (!essaiId) {
            this.showError('ID de l\'essai non trouvé');
            return;
        }

        const essaiData = await this.fetchEssaiData(essaiId);
        if (!essaiData) {
            this.showError('Impossible de charger les données de l\'essai');
            return;
        }

        const printContent = this.createEssaiPrintContent(essaiData);
        this.openPrintWindow(printContent, 'Essai-' + essaiData.id);
    }

    async printAffaire(element) {
        const affaireId = element.dataset.affaireId || this.extractIdFromUrl();
        if (!affaireId) {
            this.showError('ID de l\'affaire non trouvé');
            return;
        }

        const affaireData = await this.fetchAffaireData(affaireId);
        if (!affaireData) {
            this.showError('Impossible de charger les données de l\'affaire');
            return;
        }

        const printContent = this.createAffairePrintContent(affaireData);
        this.openPrintWindow(printContent, 'Affaire-' + affaireData.numero);
    }

    async printCourbes(element) {
        const essaiId = element.dataset.essaiId || this.extractIdFromUrl();
        if (!essaiId) {
            this.showError('ID de l\'essai non trouvé');
            return;
        }

        // Wait for charts to be fully rendered
        await this.waitForCharts();

        const courbesData = await this.fetchCourbesData(essaiId);
        const printContent = this.createCourbesPrintContent(courbesData);
        this.openPrintWindow(printContent, 'Courbes-' + essaiId);
    }

    printCurrent() {
        // Prepare current page for printing
        this.preparePrint();
        window.print();
    }

    preparePrint() {
        // Store original content
        this.originalContent = document.body.innerHTML;

        // Add print classes
        document.body.classList.add('printing');

        // Hide non-printable elements
        const noPrintElements = document.querySelectorAll('.no-print');
        noPrintElements.forEach(el => el.style.display = 'none');

        // Optimize charts for printing
        this.optimizeChartsForPrint();

        // Add print header if not present
        this.addPrintHeader();
    }

    restoreAfterPrint() {
        // Remove print classes
        document.body.classList.remove('printing');

        // Restore hidden elements
        const noPrintElements = document.querySelectorAll('.no-print');
        noPrintElements.forEach(el => el.style.display = '');

        // Restore charts
        this.restoreCharts();
    }

    optimizeChartsForPrint() {
        const charts = document.querySelectorAll('.apexcharts-canvas');
        charts.forEach(chart => {
            chart.style.background = 'white';
            chart.style.color = 'black';
        });
    }

    restoreCharts() {
        // Charts will restore automatically after print
    }

    addPrintHeader() {
        if (document.querySelector('.print-header')) return;

        const header = document.createElement('div');
        header.className = 'print-header no-screen';
        header.innerHTML = `
            <div class="print-title">FluidMotion Labs</div>
            <div class="print-subtitle">Banc d'Essai Hydraulique</div>
            <div class="print-date">Imprimé le ${new Date().toLocaleDateString('fr-FR')}</div>
        `;

        document.body.insertBefore(header, document.body.firstChild);
    }

    async waitForCharts() {
        return new Promise(resolve => {
            const checkCharts = () => {
                const charts = document.querySelectorAll('.apexcharts-canvas');
                if (charts.length === 0) {
                    resolve();
                    return;
                }

                let allLoaded = true;
                charts.forEach(chart => {
                    if (!chart.querySelector('svg')) {
                        allLoaded = false;
                    }
                });

                if (allLoaded) {
                    resolve();
                } else {
                    setTimeout(checkCharts, 100);
                }
            };

            checkCharts();
        });
    }

    openPrintWindow(content, filename = 'document') {
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>${filename}</title>
                <link rel="stylesheet" href="/assets/css/print.css">
                <style>
                    body { margin: 0; padding: 20px; }
                    @media screen { body { background: white; } }
                </style>
            </head>
            <body>
                ${content}
                <script>
                    window.onload = function() {
                        window.print();
                        window.onafterprint = function() {
                            window.close();
                        };
                    };
                </script>
            </body>
            </html>
        `);
        printWindow.document.close();
    }

    createPVPrintContent(pvData) {
        return `
            <div class="print-container pv-print">
                <div class="pv-header">
                    <div class="pv-logo">
                        <img src="/assets/images/logo.png" alt="FluidMotion Labs" class="pv-logo">
                    </div>
                    <div class="pv-info">
                        <div class="pv-number">PV N° ${pvData.numero}</div>
                        <div class="pv-date">${new Date(pvData.date_creation).toLocaleDateString('fr-FR')}</div>
                    </div>
                </div>
                
                <div class="print-section">
                    <div class="print-section-title">Informations Générales</div>
                    <div class="essai-details">
                        <div class="detail-item">
                            <span class="detail-label">Affaire:</span>
                            <span class="detail-value">${pvData.affaire_numero}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Client:</span>
                            <span class="detail-value">${pvData.client}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Type d'essai:</span>
                            <span class="detail-value">${pvData.essai_type}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Date d'essai:</span>
                            <span class="detail-value">${new Date(pvData.date_essai).toLocaleDateString('fr-FR')}</span>
                        </div>
                    </div>
                </div>
                
                <div class="print-section">
                    <div class="print-section-title">Contenu du PV</div>
                    <div>${pvData.contenu}</div>
                </div>
                
                ${pvData.rendement ? this.createRendementSection(pvData.rendement) : ''}
                
                <div class="signatures">
                    <div class="print-section-title">Signatures</div>
                    <div class="signature-grid">
                        <div class="signature-box">
                            <div class="signature-label">Opérateur</div>
                            <div class="signature-date">${new Date().toLocaleDateString('fr-FR')}</div>
                        </div>
                        <div class="signature-box">
                            <div class="signature-label">Contrôleur</div>
                            <div class="signature-date">${new Date().toLocaleDateString('fr-FR')}</div>
                        </div>
                        <div class="signature-box">
                            <div class="signature-label">Client</div>
                            <div class="signature-date">___/___/______</div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    createRendementSection(rendement) {
        return `
            <div class="rendement-summary">
                <div class="rendement-title">Calculs de Rendement</div>
                <div class="rendement-grid">
                    <div class="rendement-item">
                        <div class="rendement-label">Rendement Global</div>
                        <div class="rendement-value">${rendement.rendement_global}%</div>
                    </div>
                    <div class="rendement-item">
                        <div class="rendement-label">Rendement Volumétrique</div>
                        <div class="rendement-value">${rendement.rendement_volumetrique}%</div>
                    </div>
                    <div class="rendement-item">
                        <div class="rendement-label">Rendement Mécanique</div>
                        <div class="rendement-value">${rendement.rendement_mecanique}%</div>
                    </div>
                </div>
            </div>
        `;
    }

    // API calls
    async fetchPVData(id) {
        try {
            const response = await fetch(`/api/pv/${id}`);
            return response.ok ? await response.json() : null;
        } catch (error) {
            console.error('Erreur fetch PV:', error);
            return null;
        }
    }

    async fetchEssaiData(id) {
        try {
            const response = await fetch(`/api/essais/${id}`);
            return response.ok ? await response.json() : null;
        } catch (error) {
            console.error('Erreur fetch essai:', error);
            return null;
        }
    }

    async fetchAffaireData(id) {
        try {
            const response = await fetch(`/api/affaires/${id}`);
            return response.ok ? await response.json() : null;
        } catch (error) {
            console.error('Erreur fetch affaire:', error);
            return null;
        }
    }

    extractIdFromUrl() {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('id');
    }

    showError(message) {
        // Simple error display - could be enhanced with a proper notification system
        alert(message);
    }
}

// Initialize print manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.printManager = new PrintManager();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PrintManager;
}
