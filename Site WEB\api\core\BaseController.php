<?php

abstract class BaseController
{
    protected $method;
    protected $user;
    protected $data;

    public function __construct()
    {
        header('Content-Type: application/json');
        $this->method = $_SERVER['REQUEST_METHOD'];
        $this->data = $this->getRequestData();
    }

    protected function getRequestData()
    {
        if ($this->method === 'GET') return $_GET;
        return json_decode(file_get_contents('php://input'), true);
    }

    public function setUser($user)
    {
        $this->user = $user;
    }

    abstract public function handle();

    protected function error($message, $code = 400)
    {
        $this->json(['error' => $message], $code);
    }

    protected function json($data, $code = 200)
    {
        header('Content-Type: application/json');
        http_response_code($code);
        echo json_encode($data);
        exit;
    }
}
