<?php
require_once(__DIR__ . '/../../lib/essais.php');
require_once(__DIR__ . '/../../lib/affaires.php');
require_once(__DIR__ . '/TestBase.php');

class EssaiTest extends TestBase
{
    private $test_affaire_id;
    private $test_essai_id;

    public function testCreateEssai()
    {
        $type = 'Test Pression';
        $parametre = 'Pression: 100 bar';
        $date_essai = date('Y-m-d');

        $result = Essai::create($this->test_affaire_id, $type, $parametre, $date_essai);
        $this->assertTrue($result, 'La création d\'essai devrait réussir');

        // Vérifier que l'essai existe
        $essais = Essai::getByAffaireId($this->test_affaire_id);
        $found = false;
        foreach ($essais as $essai) {
            if ($essai['type'] === $type) {
                $found = true;
                $this->assertEquals($parametre, $essai['parametre_theorique']);
                $this->assertEquals($date_essai, $essai['date_essai']);
                // Nettoyer
                Essai::delete($essai['id']);
                break;
            }
        }
        $this->assertTrue($found, 'L\'essai créé devrait être trouvé');
    }

    public function testGetEssaiById()
    {
        $essai = Essai::getById($this->test_essai_id);
        $this->assertNotNull($essai, 'L\'essai devrait être trouvé');
        $this->assertEquals($this->test_essai_id, $essai['id']);
        $this->assertEquals('Test Essai', $essai['type']);
    }

    public function testGetEssaiByIdInvalid()
    {
        $essai = Essai::getById(99999);
        $this->assertNull($essai, 'Un essai inexistant devrait retourner null');
    }

    public function testUpdateEssai()
    {
        $nouveau_type = 'Test Modifié';
        $nouveau_parametre = 'Paramètre modifié';
        $nouveau_statut = 'Terminé';
        $resultat = 'Résultat test';

        $result = Essai::update(
            $this->test_essai_id,
            $nouveau_type,
            $nouveau_parametre,
            $nouveau_statut,
            $resultat
        );

        $this->assertTrue($result, 'La mise à jour devrait réussir');

        // Vérifier les modifications
        $essai = Essai::getById($this->test_essai_id);
        $this->assertEquals($nouveau_type, $essai['type']);
        $this->assertEquals($nouveau_parametre, $essai['parametre_theorique']);
        $this->assertEquals($nouveau_statut, $essai['statut']);
        $this->assertEquals($resultat, $essai['resultat']);
    }

    public function testGetEssaisByAffaireId()
    {
        $essais = Essai::getByAffaireId($this->test_affaire_id);
        $this->assertIsArray($essais, 'getByAffaireId devrait retourner un tableau');
        $this->assertGreaterThan(0, count($essais), 'Il devrait y avoir au moins un essai');

        // Vérifier que tous les essais appartiennent à la bonne affaire
        foreach ($essais as $essai) {
            $this->assertEquals($this->test_affaire_id, $essai['affaire_id']);
        }
    }

    public function testGetEssaisByStatut()
    {
        $essais_en_attente = Essai::getByStatut('En attente');
        $this->assertIsArray($essais_en_attente, 'getByStatut devrait retourner un tableau');

        // Vérifier que tous les essais retournés ont le bon statut
        foreach ($essais_en_attente as $essai) {
            $this->assertEquals('En attente', $essai['statut']);
        }
    }

    public function testGetEssaisByType()
    {
        $essais_type = Essai::getByType('Test Essai');
        $this->assertIsArray($essais_type, 'getByType devrait retourner un tableau');

        // Vérifier que tous les essais retournés ont le bon type
        foreach ($essais_type as $essai) {
            $this->assertEquals('Test Essai', $essai['type']);
        }
    }

    public function testDeleteEssai()
    {
        // Créer un essai temporaire pour la suppression
        $essai_id = $this->createTestEssai('Test Delete');
        $this->assertNotNull($essai_id, 'L\'essai à supprimer devrait exister');

        $result = Essai::delete($essai_id);
        $this->assertTrue($result, 'La suppression devrait réussir');

        // Vérifier que l'essai n'existe plus
        $essai = Essai::getById($essai_id);
        $this->assertNull($essai, 'L\'essai supprimé ne devrait plus exister');
    }

    private function createTestEssai($type = 'Test Essai')
    {
        $parametre = 'Paramètre test';
        $date_essai = date('Y-m-d');

        Essai::create($this->test_affaire_id, $type, $parametre, $date_essai);
        $essais = Essai::getByAffaireId($this->test_affaire_id);
        foreach ($essais as $essai) {
            if ($essai['type'] === $type) {
                return $essai['id'];
            }
        }
        return null;
    }

    public function testGetEssaisRecents()
    {
        $essais_recents = Essai::getRecents(5);
        $this->assertIsArray($essais_recents, 'getRecents devrait retourner un tableau');
        $this->assertLessThanOrEqual(5, count($essais_recents), 'Ne devrait pas retourner plus de 5 essais');
    }

    public function testGetAllEssais()
    {
        $essais = Essai::getAll();
        $this->assertIsArray($essais, 'getAll devrait retourner un tableau');
        $this->assertGreaterThan(0, count($essais), 'Il devrait y avoir au moins un essai');
    }

    public function runAllTests()
    {
        $this->setUp();

        echo "<h3>Tests Essai</h3>";

        $this->runTest('testCreateEssai');
        $this->runTest('testGetEssaiById');
        $this->runTest('testGetEssaiByIdInvalid');
        $this->runTest('testUpdateEssai');
        $this->runTest('testGetEssaisByAffaireId');
        $this->runTest('testGetEssaisByStatut');
        $this->runTest('testGetEssaisByType');
        $this->runTest('testDeleteEssai');
        $this->runTest('testGetEssaisRecents');
        $this->runTest('testGetAllEssais');

        $this->tearDown();

        return $this->getResults();
    }

    public function setUp()
    {
        parent::setUp();
        // Créer une affaire de test
        $this->test_affaire_id = $this->createTestAffaire();
        // Créer un essai de test
        $this->test_essai_id = $this->createTestEssai();
    }

    private function createTestAffaire()
    {
        $numero = 'TEST-AFFAIRE-ESSAI-' . time();
        Affaire::create($numero, 'Client Test', 'Description test', ['test'], 1);
        $affaires = Affaire::getAll();
        foreach ($affaires as $affaire) {
            if ($affaire['numero'] === $numero) {
                return $affaire['id'];
            }
        }
        return null;
    }

    public function tearDown()
    {
        // Nettoyer les données de test
        if ($this->test_essai_id) {
            Essai::delete($this->test_essai_id);
        }
        if ($this->test_affaire_id) {
            Affaire::delete($this->test_affaire_id);
        }
        parent::tearDown();
    }
}
