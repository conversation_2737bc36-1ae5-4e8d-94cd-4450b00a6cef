# Configuration Apache pour FluidMotion Labs
# Autoriser l'accès au répertoire racine

# Activer le moteur de réécriture
RewriteEngine On

# Définir les fichiers index par défaut
DirectoryIndex index.php index.html

# Autoriser l'accès depuis tous les clients
Require all granted

# Configuration de sécurité
Options -Indexes -MultiViews +FollowSymLinks

# Protection contre l'accès direct aux fichiers sensibles
<FilesMatch "\.(env|sql|log|bak)$">
    Require all denied
</FilesMatch>

# Configuration des types MIME
AddType application/json .json
AddType text/css .css
AddType application/javascript .js