<?php
/**
 * Script de debug pour vérifier la structure de la session utilisateur
 */

session_start();

echo "<h1>Debug Session Utilisateur</h1>";

if (!isset($_SESSION['user'])) {
    echo "<p style='color: red;'>Aucun utilisateur connecté. Veuillez vous connecter d'abord.</p>";
    echo "<p><a href='/auth/login.php'>Se connecter</a></p>";
    exit;
}

echo "<h2>Structure de \$_SESSION['user']:</h2>";
echo "<pre>";
print_r($_SESSION['user']);
echo "</pre>";

echo "<h2>Clés disponibles:</h2>";
echo "<ul>";
foreach ($_SESSION['user'] as $key => $value) {
    echo "<li><strong>$key</strong>: " . htmlspecialchars($value) . "</li>";
}
echo "</ul>";

echo "<h2>Tests d'affichage:</h2>";
echo "<p>Nom d'utilisateur: " . htmlspecialchars($_SESSION['user']['username'] ?? 'Non défini') . "</p>";
echo "<p>Rôle: " . htmlspecialchars($_SESSION['user']['role'] ?? 'Non défini') . "</p>";
echo "<p>ID: " . htmlspecialchars($_SESSION['user']['id'] ?? 'Non défini') . "</p>";

echo "<p><a href='/tests.php'>Retour aux tests</a></p>";
?>
