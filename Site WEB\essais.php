<?php
session_start();
require_once(__DIR__ . '/lib/essais.php');
require_once(__DIR__ . '/lib/affaires.php');
require_once(__DIR__ . '/lib/modeles.php');

if (!isset($_SESSION['user'])) {
    header('Location: /auth/login.php');
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'create':
                $modele_id = $_POST['modele_id'] ?? null;
                if ($modele_id) {
                    $modele = ModeleEssai::getById($modele_id);
                    if (Essai::createFromModele(
                        $_POST['affaire_id'],
                        $modele,
                        date('Y-m-d')
                    )) {
                        $success = "Essai créé avec succès à partir du modèle";
                    } else {
                        $error = "Erreur lors de la création de l'essai";
                    }
                } else {
                    if (Essai::create(
                        $_POST['affaire_id'],
                        $_POST['type'],
                        $_POST['parametre_theorique'],
                        date('Y-m-d'),
                        $_POST['mode_operatoire'] ?? null
                    )) {
                        $success = "Essai créé avec succès";
                    } else {
                        $error = "Erreur lors de la création de l'essai";
                    }
                }
                break;
            case 'update':
                if (Essai::update(
                    $_POST['id'],
                    $_POST['type'],
                    $_POST['parametre_theorique'],
                    $_POST['statut'],
                    $_POST['resultat'] ?? null,
                    $_POST['mode_operatoire'] ?? null
                )) {
                    $success = "Essai mis à jour avec succès";
                } else {
                    $error = "Erreur lors de la mise à jour de l'essai";
                }
                break;
            case 'delete':
                if (Essai::delete($_POST['id'])) {
                    $success = "Essai supprimé avec succès";
                } else {
                    $error = "Erreur lors de la suppression de l'essai";
                }
                break;
            case 'create_modele':
                if (ModeleEssai::create(
                    $_POST['nom_modele'],
                    $_POST['type_modele'],
                    $_POST['parametre_theorique_modele'],
                    $_POST['mode_operatoire_modele'] ?? null
                )) {
                    $success = "Modèle d'essai créé avec succès";
                } else {
                    $error = "Erreur lors de la création du modèle d'essai";
                }
                break;
        }
    }
}

$essais = Essai::getAll();
$affaires = Affaire::getAll();
$modeles = ModeleEssai::getAll();

ob_start();
?>

    <div class="mb-4 flex justify-between items-center">
        <h2 class="text-2xl font-bold dark:text-white">Gestion des Essais</h2>
        <div>
            <button data-modal-target="createModelEssaiModal" data-modal-toggle="createModelEssaiModal"
                    class="text-gray-900 bg-white border border-gray-300 hover:bg-gray-100 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 focus:outline-none dark:focus:ring-blue-800">
                Nouveau Modèle
            </button>
            <button data-modal-target="createEssaiModal" data-modal-toggle="createEssaiModal"
                    class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">
                Nouvel Essai
            </button>
        </div>
    </div>

<?php if (isset($success)): ?>
    <div id="alert-success"
         class="flex items-center p-4 mb-4 text-green-800 rounded-lg bg-green-50 dark:bg-gray-800 dark:text-green-400"
         role="alert">
        <svg class="flex-shrink-0 w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
             viewBox="0 0 20 20">
            <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/>
        </svg>
        <div class="ms-3 text-sm font-medium"><?php echo htmlspecialchars($success); ?></div>
        <button type="button"
                class="ms-auto -mx-1.5 -my-1.5 bg-green-50 text-green-500 rounded-lg focus:ring-2 focus:ring-green-400 p-1.5 hover:bg-green-200 inline-flex items-center justify-center h-8 w-8 dark:bg-gray-800 dark:text-green-400 dark:hover:bg-gray-700"
                data-dismiss-target="#alert-success" aria-label="Close">
            <span class="sr-only">Fermer</span>
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
            </svg>
        </button>
    </div>
<?php endif; ?>

<?php if (isset($error)): ?>
    <div id="alert-error"
         class="flex items-center p-4 mb-4 text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400"
         role="alert">
        <svg class="flex-shrink-0 w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
             viewBox="0 0 20 20">
            <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/>
        </svg>
        <div class="ms-3 text-sm font-medium"><?php echo htmlspecialchars($error); ?></div>
        <button type="button"
                class="ms-auto -mx-1.5 -my-1.5 bg-red-50 text-red-500 rounded-lg focus:ring-2 focus:ring-red-400 p-1.5 hover:bg-red-200 inline-flex items-center justify-center h-8 w-8 dark:bg-gray-800 dark:text-red-400 dark:hover:bg-gray-700"
                data-dismiss-target="#alert-error" aria-label="Close">
            <span class="sr-only">Fermer</span>
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
            </svg>
        </button>
    </div>
<?php endif; ?>

    <div class="relative overflow-x-auto shadow-md sm:rounded-lg">
        <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
            <tr>
                <th scope="col" class="px-6 py-3">ID</th>
                <th scope="col" class="px-6 py-3">Affaire</th>
                <th scope="col" class="px-6 py-3">Type</th>
                <th scope="col" class="px-6 py-3">Date d'essai</th>
                <th scope="col" class="px-6 py-3">Statut</th>
                <th scope="col" class="px-6 py-3">Actions</th>
            </tr>
            </thead>
            <tbody>
            <?php foreach ($essais as $essai): ?>
                <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                    <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                        <?php echo htmlspecialchars($essai['id']); ?>
                    </th>
                    <td class="px-6 py-4"><?php echo htmlspecialchars($essai['numero_affaire']); ?></td>
                    <td class="px-6 py-4"><?php echo htmlspecialchars($essai['type']); ?></td>
                    <td class="px-6 py-4"><?php echo htmlspecialchars($essai['date_essai']); ?></td>
                    <td class="px-6 py-4">
                        <span class="text-xs font-medium mr-2 px-2.5 py-0.5 rounded-md border 
                                            <?php
                        if ($essai['statut'] == 'Annulé') {
                            echo 'bg-red-100 text-red-800 dark:bg-gray-700 dark:text-red-400 border-red-100 dark:border-red-500';
                        } elseif ($essai['statut'] == 'Terminé') {
                            echo 'bg-green-100 text-green-800 dark:bg-gray-700 dark:text-green-400 border-green-100 dark:border-green-500';
                        } elseif ($essai['statut'] == 'En cours') {
                            echo 'bg-blue-100 text-blue-800 dark:bg-gray-700 dark:text-blue-400 border-blue-100 dark:border-blue-500';
                        } elseif ($essai['statut'] == 'En attente') {
                            echo 'bg-yellow-100 text-yellow-800 dark:bg-gray-700 dark:text-yellow-400 border-yellow-100 dark:border-yellow-500';
                        }
                        ?>">
                            <?php echo htmlspecialchars($essai['statut']); ?>
                        </span>
                    </td>
                    <td class="px-6 py-4">
                        <button data-modal-target="editEssaiModal<?php echo $essai['id']; ?>"
                                data-modal-toggle="editEssaiModal<?php echo $essai['id']; ?>"
                                class="font-medium text-blue-600 dark:text-blue-500 hover:underline">Modifier
                        </button>
                        <button data-modal-target="deleteEssaiModal<?php echo $essai['id']; ?>"
                                data-modal-toggle="deleteEssaiModal<?php echo $essai['id']; ?>"
                                class="font-medium text-red-600 dark:text-red-500 hover:underline ms-3">Supprimer
                        </button>
                        <a href="/detail/essai.php?id=<?php echo $essai['id']; ?>"
                           class="font-medium text-green-600 dark:text-green-500 hover:underline ms-3">Voir</a>
                    </td>
                </tr>
            <?php endforeach; ?>
            </tbody>
        </table>
    </div>

    <div id="createEssaiModal" tabindex="-1" aria-hidden="true"
         class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
        <div class="relative p-4 w-full max-w-md max-h-full">
            <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                        Nouvel Essai
                    </h3>
                    <button type="button"
                            class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                            data-modal-hide="createEssaiModal">
                        <svg class="w-3 h-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                        </svg>
                        <span class="sr-only">Fermer</span>
                    </button>
                </div>
                <form class="p-4 md:p-5" method="POST">
                    <input type="hidden" name="action" value="create">
                    <div class="grid gap-4 mb-4">
                        <div>
                            <label for="affaire_id"
                                   class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Affaire</label>
                            <select name="affaire_id" id="affaire_id"
                                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                    required>
                                <option value="" disabled selected>Sélectionner une affaire</option>
                                <?php foreach ($affaires as $affaire): ?>
                                    <option value="<?php echo $affaire['id']; ?>"><?php echo htmlspecialchars($affaire['numero'] . ' - ' . $affaire['client']); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div>
                            <label for="modele_id" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Modèle
                                d'essai (optionnel)</label>
                            <select name="modele_id" id="modele_id"
                                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                                <option value="">Créer un essai sans modèle</option>
                                <?php foreach ($modeles as $modele): ?>
                                    <option value="<?php echo $modele['id']; ?>"><?php echo htmlspecialchars($modele['nom']); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div id="custom_essai_fields" class="grid gap-4">
                            <div>
                                <label for="type" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Type
                                    d'essai</label>
                                <input type="text" name="type" id="type"
                                       class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            </div>
                            <div>
                                <label for="parametre_theorique"
                                       class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Paramètre
                                    théorique</label>
                                <input type="text" name="parametre_theorique" id="parametre_theorique"
                                       class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            </div>
                            <div>
                                <label for="mode_operatoire"
                                       class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Mode
                                    opératoire</label>
                                <textarea name="mode_operatoire" id="mode_operatoire" rows="4"
                                          class="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"></textarea>
                            </div>
                        </div>
                    </div>
                    <button type="submit"
                            class="text-white inline-flex items-center bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                        Créer l'essai
                    </button>
                </form>
            </div>
        </div>
    </div>

    <div id="createModelEssaiModal" tabindex="-1" aria-hidden="true"
         class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
        <div class="relative p-4 w-full max-w-md max-h-full">
            <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                        Nouveau Modèle d'Essai
                    </h3>
                    <button type="button"
                            class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                            data-modal-hide="createModelEssaiModal">
                        <svg class="w-3 h-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                        </svg>
                        <span class="sr-only">Fermer</span>
                    </button>
                </div>
                <form class="p-4 md:p-5" method="POST">
                    <input type="hidden" name="action" value="create_modele">
                    <div class="grid gap-4 mb-4">
                        <div>
                            <label for="nom_modele"
                                   class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Nom du
                                modèle</label>
                            <input type="text" name="nom_modele" id="nom_modele"
                                   class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                   required>
                        </div>
                        <div>
                            <label for="type_modele"
                                   class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Type
                                d'essai</label>
                            <input type="text" name="type_modele" id="type_modele"
                                   class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                   required>
                        </div>
                        <div>
                            <label for="parametre_theorique_modele"
                                   class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Paramètre
                                théorique</label>
                            <input type="text" name="parametre_theorique_modele" id="parametre_theorique_modele"
                                   class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                   required>
                        </div>
                        <div>
                            <label for="mode_operatoire_modele"
                                   class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Mode
                                opératoire</label>
                            <textarea name="mode_operatoire_modele" id="mode_operatoire_modele" rows="4"
                                      class="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"></textarea>
                        </div>
                    </div>
                    <button type="submit"
                            class="text-white inline-flex items-center bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                        Créer le modèle
                    </button>
                </form>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const modeleSelect = document.getElementById('modele_id');
            const customFields = document.getElementById('custom_essai_fields');

            modeleSelect.addEventListener('change', function () {
                if (this.value === '') {
                    customFields.style.display = 'grid';
                    document.getElementById('type').required = true;
                    document.getElementById('parametre_theorique').required = true;
                } else {
                    customFields.style.display = 'none';
                    document.getElementById('type').required = false;
                    document.getElementById('parametre_theorique').required = false;
                }
            });
        });
    </script>

<?php foreach ($essais as $essai): ?>
    <div id="editEssaiModal<?php echo $essai['id']; ?>" tabindex="-1" aria-hidden="true"
         class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
        <div class="relative p-4 w-full max-w-md max-h-full">
            <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                        Modifier l'essai
                    </h3>
                    <button type="button"
                            class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                            data-modal-hide="editEssaiModal<?php echo $essai['id']; ?>">
                        <svg class="w-3 h-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                        </svg>
                        <span class="sr-only">Fermer</span>
                    </button>
                </div>
                <form class="p-4 md:p-5" method="POST">
                    <input type="hidden" name="action" value="update">
                    <input type="hidden" name="id" value="<?php echo $essai['id']; ?>">

                    <div class="grid gap-4 mb-4">
                        <div>
                            <label for="affaire_id"
                                   class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Affaire</label>
                            <select name="affaire_id" id="affaire_id"
                                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                    required>
                                <?php foreach ($affaires as $affaire): ?>
                                    <option value="<?php echo htmlspecialchars($affaire['id']); ?>" <?php echo ($affaire['id'] == $essai['affaire_id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($affaire['numero'] . ' - ' . $affaire['client']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div>
                            <label for="type" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Type
                                d'essai</label>
                            <input type="text" name="type" id="type"
                                   value="<?php echo htmlspecialchars($essai['type']); ?>"
                                   class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                   required>
                        </div>
                        <div>
                            <label for="parametre_theorique"
                                   class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Paramètre
                                théorique</label>
                            <input type="text" name="parametre_theorique" id="parametre_theorique"
                                   value="<?php echo htmlspecialchars($essai['parametre_theorique']); ?>"
                                   class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                   required>
                        </div>
                        <div>
                            <label for="statut" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Statut</label>
                            <select name="statut" id="statut"
                                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                    required>
                                <option value="En attente" <?php echo ($essai['statut'] == 'En attente') ? 'selected' : ''; ?>>
                                    En attente
                                </option>
                                <option value="En cours" <?php echo ($essai['statut'] == 'En cours') ? 'selected' : ''; ?>>
                                    En cours
                                </option>
                                <option value="Terminé" <?php echo ($essai['statut'] == 'Terminé') ? 'selected' : ''; ?>>
                                    Terminé
                                </option>
                                <option value="Annulé" <?php echo ($essai['statut'] == 'Annulé') ? 'selected' : ''; ?>>
                                    Annulé
                                </option>
                            </select>
                        </div>
                        <div>
                            <label for="resultat" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Résultat</label>
                            <textarea name="resultat" id="resultat" rows="4"
                                      class="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                      required><?php echo htmlspecialchars($essai['resultat'] ?? ''); ?></textarea>
                        </div>
                        <div>
                            <label for="mode_operatoire"
                                   class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Mode
                                opératoire</label>
                            <textarea name="mode_operatoire" id="mode_operatoire" rows="4"
                                      class="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                      required><?php echo htmlspecialchars($essai['mode_operatoire'] ?? ''); ?></textarea>
                        </div>
                    </div>
                    <button type="submit"
                            class="text-white inline-flex items-center bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                        Enregistrer les modifications
                    </button>
                </form>
            </div>
        </div>
    </div>

    <div id="deleteEssaiModal<?php echo $essai['id']; ?>" tabindex="-1" aria-hidden="true"
         class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
        <div class="relative p-4 w-full max-w-md max-h-full">
            <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                        Confirmer la suppression
                    </h3>
                    <button type="button"
                            class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                            data-modal-hide="deleteEssaiModal<?php echo $essai['id']; ?>">
                        <svg class="w-3 h-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                        </svg>
                        <span class="sr-only">Fermer</span>
                    </button>
                </div>
                <div class="p-4 md:p-5">
                    <p class="text-gray-500 dark:text-gray-300">Êtes-vous sûr de vouloir supprimer cet essai ? Cette
                        action est irréversible.</p>
                    <form method="POST" class="mt-5">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="id" value="<?php echo $essai['id']; ?>">
                        <button type="submit"
                                class="text-white bg-red-600 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 dark:focus:ring-red-800 font-medium rounded-lg text-sm inline-flex items-center px-5 py-2.5 text-center me-2">
                            Oui, supprimer
                        </button>
                        <button type="button" data-modal-hide="deleteEssaiModal<?php echo $essai['id']; ?>"
                                class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900 focus:z-10 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-500 dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-gray-600">
                            Non, annuler
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php endforeach; ?>

<?php
$pageContent = ob_get_clean();
include 'layout.php';
?>