<?php
require_once __DIR__ . '/../core/BaseController.php';
require_once __DIR__ . '/../../lib/courbes.php';

class CourbeController extends BaseController
{
    public function handle()
    {
        switch ($this->method) {
            case 'GET':
                return $this->index();
            case 'POST':
                return $this->create();
            case 'PUT':
                return $this->update();
            case 'DELETE':
                return $this->delete();
            default:
                $this->error('Méthode non autorisée', 405);
        }
    }

    private function index()
    {
        if (isset($this->data['id'])) {
            $courbe = Courbe::getById($this->data['id']);
            if (!$courbe) return $this->error('Courbe non trouvée', 404);
            return $this->json($courbe);
        }
        if (isset($this->data['essai_id'])) {
            $type_courbe = isset($this->data['type']) ? $this->data['type'] : null;
            return $this->json(Courbe::getByEssaiId($this->data['essai_id'], $type_courbe));
        }
        return $this->json(Courbe::getRecent());
    }

    private function create()
    {
        if (!isset($this->data['essai_id']) || !isset($this->data['type_courbe']) || !isset($this->data['donnees'])) {
            return $this->error('Données manquantes');
        }

        if (Courbe::create(
            $this->data['essai_id'],
            $this->data['type_courbe'],
            $this->data['donnees']
        )) {
            return $this->json(['message' => 'Courbe créée'], 201);
        }
        return $this->error('Erreur de création', 500);
    }

    private function update()
    {
        if (!isset($this->data['id']) || !isset($this->data['donnees'])) {
            return $this->error('Données manquantes');
        }

        if (Courbe::updateDonnees($this->data['id'], $this->data['donnees'])) {
            return $this->json(['message' => 'Courbe mise à jour']);
        }
        return $this->error('Erreur de mise à jour', 500);
    }

    private function delete()
    {
        if (!isset($this->data['id'])) {
            return $this->error('ID manquant');
        }

        if (Courbe::delete($this->data['id'])) {
            return $this->json(['message' => 'Courbe supprimée']);
        }
        return $this->error('Erreur de suppression', 500);
    }
}
