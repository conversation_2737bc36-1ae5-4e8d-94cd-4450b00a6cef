/* Print-specific styles for FluidMotion Labs */

@media print {
    /* Reset and base styles */
    * {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
    }

    body {
        font-family: 'Arial', sans-serif;
        font-size: 12pt;
        line-height: 1.4;
        color: #000;
        background: white;
        margin: 0;
        padding: 0;
    }

    /* Hide navigation and UI elements */
    .no-print,
    nav,
    .navbar,
    .sidebar,
    .breadcrumb,
    .btn,
    button,
    .modal,
    .dropdown,
    .tooltip,
    .alert,
    .pagination,
    .footer,
    .print-hide {
        display: none !important;
    }

    /* Page layout */
    .print-container {
        width: 100%;
        max-width: none;
        margin: 0;
        padding: 20px;
        box-shadow: none;
        border: none;
    }

    /* Headers and titles */
    .print-header {
        text-align: center;
        margin-bottom: 30px;
        border-bottom: 2px solid #000;
        padding-bottom: 15px;
    }

    .print-title {
        font-size: 18pt;
        font-weight: bold;
        margin-bottom: 10px;
        color: #000;
    }

    .print-subtitle {
        font-size: 14pt;
        color: #333;
        margin-bottom: 5px;
    }

    .print-date {
        font-size: 10pt;
        color: #666;
    }

    /* Content sections */
    .print-section {
        margin-bottom: 25px;
        page-break-inside: avoid;
    }

    .print-section-title {
        font-size: 14pt;
        font-weight: bold;
        margin-bottom: 10px;
        color: #000;
        border-bottom: 1px solid #ccc;
        padding-bottom: 5px;
    }

    /* Tables */
    table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
        font-size: 10pt;
    }

    th, td {
        border: 1px solid #000;
        padding: 8px;
        text-align: left;
        vertical-align: top;
    }

    th {
        background-color: #f0f0f0 !important;
        font-weight: bold;
        color: #000;
    }

    /* Charts and images */
    .chart-container {
        page-break-inside: avoid;
        margin: 20px 0;
    }

    .chart-title {
        font-size: 12pt;
        font-weight: bold;
        text-align: center;
        margin-bottom: 10px;
    }

    /* PV specific styles */
    .pv-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
        border-bottom: 2px solid #000;
        padding-bottom: 15px;
    }

    .pv-logo {
        max-height: 60px;
        max-width: 200px;
    }

    .pv-info {
        text-align: right;
    }

    .pv-number {
        font-size: 16pt;
        font-weight: bold;
        color: #000;
    }

    .pv-date {
        font-size: 12pt;
        color: #333;
    }

    /* Essai details */
    .essai-summary {
        background-color: #f9f9f9 !important;
        border: 1px solid #ddd;
        padding: 15px;
        margin-bottom: 20px;
        page-break-inside: avoid;
    }

    .essai-title {
        font-size: 14pt;
        font-weight: bold;
        margin-bottom: 10px;
        color: #000;
    }

    .essai-details {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
        margin-bottom: 15px;
    }

    .detail-item {
        margin-bottom: 8px;
    }

    .detail-label {
        font-weight: bold;
        color: #333;
        display: inline-block;
        width: 120px;
    }

    .detail-value {
        color: #000;
    }

    /* Rendement display */
    .rendement-summary {
        background-color: #f0f8ff !important;
        border: 2px solid #4a90e2;
        padding: 15px;
        margin: 20px 0;
        page-break-inside: avoid;
    }

    .rendement-title {
        font-size: 14pt;
        font-weight: bold;
        color: #000;
        margin-bottom: 15px;
        text-align: center;
    }

    .rendement-grid {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 15px;
        margin-bottom: 15px;
    }

    .rendement-item {
        text-align: center;
        padding: 10px;
        border: 1px solid #ddd;
        background-color: white !important;
    }

    .rendement-label {
        font-size: 10pt;
        color: #666;
        margin-bottom: 5px;
    }

    .rendement-value {
        font-size: 16pt;
        font-weight: bold;
        color: #000;
    }

    /* Courbes data */
    .courbe-data {
        margin: 20px 0;
        page-break-inside: avoid;
    }

    .courbe-table {
        font-size: 9pt;
    }

    .courbe-table th {
        background-color: #e6f3ff !important;
        color: #000;
    }

    /* Signatures */
    .signatures {
        margin-top: 40px;
        page-break-inside: avoid;
    }

    .signature-grid {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 30px;
        margin-top: 30px;
    }

    .signature-box {
        text-align: center;
        border-top: 1px solid #000;
        padding-top: 10px;
        min-height: 60px;
    }

    .signature-label {
        font-size: 10pt;
        font-weight: bold;
        color: #000;
    }

    .signature-date {
        font-size: 9pt;
        color: #666;
        margin-top: 5px;
    }

    /* Page breaks */
    .page-break {
        page-break-before: always;
    }

    .page-break-avoid {
        page-break-inside: avoid;
    }

    /* Footer */
    .print-footer {
        position: fixed;
        bottom: 20px;
        left: 20px;
        right: 20px;
        text-align: center;
        font-size: 9pt;
        color: #666;
        border-top: 1px solid #ccc;
        padding-top: 10px;
    }

    /* Page numbering */
    @page {
        margin: 2cm;
        @bottom-right {
            content: "Page " counter(page) " sur " counter(pages);
            font-size: 9pt;
            color: #666;
        }
    }

    /* Specific adjustments for different content types */
    .affaire-print {
        /* Styles spécifiques pour l'impression des affaires */
    }

    .essai-print {
        /* Styles spécifiques pour l'impression des essais */
    }

    .pv-print {
        /* Styles spécifiques pour l'impression des PV */
    }

    /* Chart printing adjustments */
    .apexcharts-canvas {
        max-width: 100% !important;
        height: auto !important;
    }

    .apexcharts-svg {
        background: white !important;
    }

    /* Hide interactive elements */
    .apexcharts-toolbar,
    .apexcharts-menu,
    .apexcharts-tooltip {
        display: none !important;
    }

    /* Ensure proper spacing */
    .print-spacer {
        height: 20px;
    }

    .print-spacer-large {
        height: 40px;
    }

    /* Quality adjustments */
    img {
        max-width: 100%;
        height: auto;
        page-break-inside: avoid;
    }

    /* Text adjustments */
    .print-bold {
        font-weight: bold;
        color: #000;
    }

    .print-italic {
        font-style: italic;
    }

    .print-center {
        text-align: center;
    }

    .print-right {
        text-align: right;
    }

    /* Border utilities */
    .print-border {
        border: 1px solid #000;
    }

    .print-border-top {
        border-top: 1px solid #000;
    }

    .print-border-bottom {
        border-bottom: 1px solid #000;
    }
}
