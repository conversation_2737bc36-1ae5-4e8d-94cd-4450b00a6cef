---
sidebar_position: 9
title: Tests de Permissions et Rôles
description: Tests de validation des permissions et contrôles d'accès par rôle
keywords: [permissions, rôles, sécurité, contrôleur, opérateur]
---

# Tests de Permissions et Rôles

Cette section présente les tests de validation des permissions et contrôles d'accès selon les rôles utilisateur.

## 🎯 Objectifs des Tests

- Valider les restrictions d'accès pour les opérateurs
- Vérifier les permissions API par rôle
- Contrôler la sécurité des fonctions administratives
- Tester l'intégrité du système de permissions

## 📊 Vue d'Ensemble

| **Module** | **Permissions et Rôles** |
|------------|---------------------------|
| **Nombre de tests** | **2 tests** |
| **Criticité** | **Critique** |
| **Couverture** | **100%** |
| **Profils concernés** | **Contrôleur + Opérateur** |

## 🧪 Tests Détaillés

### PERM-001 : Restrictions d'Accès Opérateur

| **Aspect** | **Détail** |
|------------|------------|
| **Objectif** | Valider les restrictions d'accès pour les opérateurs |
| **Préconditions** | - Utilisateur opérateur connecté |
| **Étapes de Test** | 1. Tenter d'accéder directement à /backup.php<br />2. Tenter d'accéder à /diagnostic.php<br />3. Tenter d'accéder au générateur de données<br />4. Vérifier les redirections et messages d'erreur |
| **Résultats Attendus** | - Accès refusé aux pages administratives<br />- Redirection vers page d'erreur ou login<br />- Message "Permission refusée" affiché<br />- Logs de sécurité générés |
| **Critères de Réussite** | ✅ Restrictions d'accès respectées |

### PERM-002 : Permissions API par Rôle

| **Aspect** | **Détail** |
|------------|------------|
| **Objectif** | Valider les permissions API par rôle |
| **Préconditions** | - Comptes contrôleur et opérateur<br />- Token JWT valides |
| **Étapes de Test** | 1. Tester les endpoints API avec token opérateur<br />2. Tester les endpoints admin avec token opérateur<br />3. Tester avec token contrôleur<br />4. Vérifier les codes de retour HTTP |
| **Résultats Attendus** | - Endpoints publics accessibles aux deux rôles<br />- Endpoints admin refusés aux opérateurs (403)<br />- Endpoints admin accessibles aux contrôleurs<br />- Codes de retour HTTP corrects |
| **Critères de Réussite** | ✅ Permissions API respectées |

## 🔐 Matrice des Permissions

### 👨‍💼 Permissions Contrôleur

| **Fonctionnalité** | **Accès** | **Description** |
|-------------------|-----------|-----------------|
| **Affaires** | ✅ Complet | CRUD + suppression |
| **Essais** | ✅ Complet | CRUD + gestion avancée |
| **PV** | ✅ Complet | CRUD + tous statuts |
| **Courbes** | ✅ Complet | CRUD + optimisation |
| **Rendements** | ✅ Complet | Calculs + statistiques |
| **Sauvegarde** | ✅ Exclusif | Backup/restore |
| **Diagnostic** | ✅ Exclusif | Monitoring système |
| **Générateur** | ✅ Exclusif | Données de test |
| **API Admin** | ✅ Complet | Tous endpoints |

### 👨‍🔧 Permissions Opérateur

| **Fonctionnalité** | **Accès** | **Description** |
|-------------------|-----------|-----------------|
| **Affaires** | ✅ Limité | CRUD sans suppression |
| **Essais** | ✅ Limité | CRUD de base |
| **PV** | ✅ Limité | CRUD avec restrictions |
| **Courbes** | ✅ Limité | Saisie et consultation |
| **Rendements** | ✅ Limité | Consultation calculs |
| **Sauvegarde** | ❌ Interdit | Aucun accès |
| **Diagnostic** | ❌ Interdit | Aucun accès |
| **Générateur** | ❌ Interdit | Aucun accès |
| **API Admin** | ❌ Interdit | Accès refusé (403) |

## 🛡️ Tests de Sécurité

### 🔒 Contrôles d'Accès

#### URLs Protégées
```
/backup.php          → Contrôleur uniquement
/diagnostic.php      → Contrôleur uniquement
/data-generator.php  → Contrôleur uniquement
/admin/*            → Contrôleur uniquement
```

#### Endpoints API Protégés
```
POST /api/backup     → Contrôleur uniquement
GET /api/diagnostic  → Contrôleur uniquement
POST /api/generate   → Contrôleur uniquement
DELETE /api/*        → Contrôleur uniquement
```

### 🔐 Mécanismes de Sécurité

| **Mécanisme** | **Description** | **Validation** |
|---------------|-----------------|----------------|
| **JWT Token** | Authentification avec rôle | Signature et expiration |
| **Session Check** | Vérification session active | Timeout et invalidation |
| **Role Check** | Contrôle du rôle utilisateur | Middleware de sécurité |
| **CSRF Protection** | Protection contre CSRF | Token CSRF valide |

## 🎭 Tests par Profil

### 👨‍💼 Tests Contrôleur
- **PERM-001** : Validation accès complet ✅
- **PERM-002** : Validation API administrative ✅

### 👨‍🔧 Tests Opérateur
- **PERM-001** : Validation restrictions ✅
- **PERM-002** : Validation API limitée ✅

## 🚨 Points de Vigilance

### Sécurité Critique
- Aucun contournement possible des restrictions
- Messages d'erreur non informatifs pour les attaquants
- Logs de sécurité pour toutes les tentatives

### Cohérence
- Permissions identiques interface/API
- Comportement cohérent entre navigateurs
- Gestion correcte des sessions expirées

### Audit
- Traçabilité des accès refusés
- Monitoring des tentatives d'intrusion
- Alertes sur comportements suspects

## 📋 Checklist de Validation

### Avant les Tests
- [ ] Comptes de test configurés
- [ ] Tokens JWT valides générés
- [ ] Environnement de test sécurisé
- [ ] Logs de sécurité activés

### Tests d'Interface
- [ ] Menus cachés pour opérateurs
- [ ] Redirections sur accès direct
- [ ] Messages d'erreur appropriés
- [ ] Pas de fuite d'information

### Tests d'API
- [ ] Codes HTTP corrects (403, 401)
- [ ] Validation des tokens JWT
- [ ] Respect des permissions par endpoint
- [ ] Gestion des tokens expirés

### Après les Tests
- [ ] Analyse des logs de sécurité
- [ ] Vérification aucun contournement
- [ ] Documentation des vulnérabilités
- [ ] Validation de la robustesse

## 🔗 Liens Connexes

- [**Tests d'Authentification**](./authentification) - Base de la sécurité
- [**Tests de Sauvegarde**](./sauvegarde) - Fonctions contrôleur uniquement
- [**Tests API**](./api) - Sécurité des endpoints
- [**Guide d'Exécution**](./guide-execution) - Méthodologie de test

---

:::tip Conseil Sécurité
Testez toujours les permissions avec des tentatives d'accès direct aux URLs pour vérifier qu'il n'y a pas de contournement possible.
:::

:::warning Attention Critique
Les tests de permissions sont critiques pour la sécurité. Toute faille détectée doit être corrigée immédiatement avant la mise en production.
:::

:::info Navigation
**Précédent** : [Tests de Sauvegarde](./sauvegarde)  
**Suivant** : [Tests API](./api)
:::
