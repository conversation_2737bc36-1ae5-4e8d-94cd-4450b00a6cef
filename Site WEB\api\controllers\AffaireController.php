<?php
require_once __DIR__ . '/../core/BaseController.php';
require_once __DIR__ . '/../../lib/affaires.php';

class AffaireController extends BaseController
{
    public function handle()
    {
        switch ($this->method) {
            case 'GET':
                return $this->index();
            case 'POST':
                return $this->create();
            case 'PUT':
                return $this->update();
            case 'DELETE':
                return $this->delete();
            default:
                $this->error('Méthode non autorisée', 405);
        }
    }

    private function index()
    {
        if (isset($this->data['id'])) {
            $affaire = Affaire::getById($this->data['id']);
            if (!$affaire) return $this->error('Affaire non trouvée', 404);
            return $this->json($affaire);
        }
        return $this->json(Affaire::getAll());
    }

    /**
     * @throws Exception
     */
    private function create()
    {
        if (!isset($this->data['numero']) || !isset($this->data['client'])) {
            return $this->error('Données manquantes');
        }
        try {
            if (Affaire::create(
                $this->data['numero'],
                $this->data['client'],
                $this->data['description'] ?? '',
                array_map('trim', $this->data['tags']),
                $this->user['id']
            )) {
                return $this->json(['message' => 'Affaire créée'], 201);
            }
            return $this->error('Erreur de création', 500);
        } catch (Exception $e) {
            return $this->error('Erreur de création: ' . $e->getMessage(), 500);
        }
    }

    private function update()
    {
        if (!isset($this->data['id'])) {
            return $this->error('ID manquant');
        }

        if (Affaire::update(
            $this->data['id'],
            $this->data['numero'],
            $this->data['client'],
            $this->data['description'] ?? '',
            $this->data['statut'],
            array_map('trim', $this->data['tags']),
            $this->user['id']
        )) {
            return $this->json(['message' => 'Affaire mise à jour']);
        }
        return $this->error('Erreur de mise à jour', 500);
    }

    private function delete()
    {
        if (!isset($this->data['id'])) {
            return $this->error('ID manquant');
        }

        if (Affaire::delete($this->data['id'])) {
            return $this->json(['message' => 'Affaire supprimée']);
        }
        return $this->error('Erreur de suppression', 500);
    }
}
