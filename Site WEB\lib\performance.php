<?php
require_once(__DIR__ . '/../config/database.php');

class Performance
{
    const DEFAULT_PAGE_SIZE = 50;
    const MAX_PAGE_SIZE = 200;
    const CACHE_TTL = 300; // 5 minutes

    private static $cache = [];

    /**
     * Optimiser les requêtes de courbes avec mise en cache
     */
    public static function getCourbesOptimized($essaiId, $typeCourbe = null, $maxPoints = 1000)
    {
        $cacheKey = "courbes_{$essaiId}_{$typeCourbe}_{$maxPoints}";

        return self::cache($cacheKey, function () use ($essaiId, $typeCourbe, $maxPoints) {
            $query = "SELECT * FROM courbes WHERE essai_id = ?";
            $params = [$essaiId];

            if ($typeCourbe) {
                $query .= " AND type_courbe = ?";
                $params[] = $typeCourbe;
            }

            $query .= " ORDER BY timestamp ASC";

            $stmt = self::getDb()->prepare($query);
            $stmt->execute($params);
            $courbes = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Optimiser chaque courbe
            foreach ($courbes as &$courbe) {
                if ($courbe['donnees']) {
                    $data = json_decode($courbe['donnees'], true);
                    if (is_array($data)) {
                        $courbe['donnees_optimisees'] = self::optimizeCourbeData($data, $maxPoints);
                        $courbe['points_originaux'] = count($data);
                        $courbe['points_optimises'] = count($courbe['donnees_optimisees']);
                    }
                }
            }

            return $courbes;
        });
    }

    /**
     * Cache simple en mémoire
     */
    public static function cache($key, $callback, $ttl = self::CACHE_TTL)
    {
        $cacheKey = md5($key);

        // Vérifier si la donnée est en cache et valide
        if (isset(self::$cache[$cacheKey])) {
            $cached = self::$cache[$cacheKey];
            if (time() - $cached['timestamp'] < $ttl) {
                return $cached['data'];
            }
        }

        // Exécuter le callback et mettre en cache
        $data = $callback();
        self::$cache[$cacheKey] = [
            'data' => $data,
            'timestamp' => time()
        ];

        return $data;
    }

    /**
     * Obtenir la connexion à la base de données
     */
    private static function getDb()
    {
        return Database::getInstance()->getConnection();
    }

    /**
     * Optimiser les données de courbes pour l'affichage
     */
    public static function optimizeCourbeData($courbeData, $maxPoints = 1000)
    {
        if (!is_array($courbeData) || count($courbeData) <= $maxPoints) {
            return $courbeData;
        }

        // Échantillonnage intelligent des données
        $step = ceil(count($courbeData) / $maxPoints);
        $optimizedData = [];

        for ($i = 0; $i < count($courbeData); $i += $step) {
            $optimizedData[] = $courbeData[$i];
        }

        // S'assurer d'inclure le dernier point
        if (end($optimizedData) !== end($courbeData)) {
            $optimizedData[] = end($courbeData);
        }

        return $optimizedData;
    }

    /**
     * Optimiser les requêtes d'affaires avec pagination
     */
    public static function getAffairesOptimized($page = 1, $pageSize = self::DEFAULT_PAGE_SIZE, $filters = [])
    {
        $query = "
            SELECT a.*, 
                   COUNT(e.id) AS nombre_essais,
                   MAX(e.date_essai) AS derniere_date_essai
            FROM affaires a
            LEFT JOIN essais e ON a.id = e.affaire_id
        ";

        $params = [];
        $whereConditions = [];

        // Appliquer les filtres
        if (!empty($filters['statut'])) {
            $whereConditions[] = "a.statut = ?";
            $params[] = $filters['statut'];
        }

        if (!empty($filters['client'])) {
            $whereConditions[] = "a.client LIKE ?";
            $params[] = '%' . $filters['client'] . '%';
        }

        if (!empty($filters['numero'])) {
            $whereConditions[] = "a.numero LIKE ?";
            $params[] = '%' . $filters['numero'] . '%';
        }

        if (!empty($whereConditions)) {
            $query .= " WHERE " . implode(" AND ", $whereConditions);
        }

        $query .= " GROUP BY a.id ORDER BY a.date_creation DESC";

        return self::paginate($query, $params, $page, $pageSize);
    }

    /**
     * Paginer les résultats d'une requête
     */
    public static function paginate($query, $params = [], $page = 1, $pageSize = self::DEFAULT_PAGE_SIZE)
    {
        $pageSize = min($pageSize, self::MAX_PAGE_SIZE);
        $offset = ($page - 1) * $pageSize;

        // Compter le total d'éléments
        $countQuery = self::convertToCountQuery($query);
        $stmt = self::getDb()->prepare($countQuery);
        $stmt->execute($params);
        $total = $stmt->fetchColumn();

        // Récupérer les données paginées
        $paginatedQuery = $query . " LIMIT ? OFFSET ?";
        $paginatedParams = array_merge($params, [$pageSize, $offset]);

        $stmt = self::getDb()->prepare($paginatedQuery);
        $stmt->execute($paginatedParams);
        $data = $stmt->fetchAll(PDO::FETCH_ASSOC);

        return [
            'data' => $data,
            'pagination' => [
                'current_page' => $page,
                'page_size' => $pageSize,
                'total_items' => $total,
                'total_pages' => ceil($total / $pageSize),
                'has_next' => $page < ceil($total / $pageSize),
                'has_prev' => $page > 1
            ]
        ];
    }

    /**
     * Convertir une requête SELECT en requête COUNT
     */
    private static function convertToCountQuery($query)
    {
        // Supprimer ORDER BY pour la requête de comptage
        $query = preg_replace('/ORDER BY.*$/i', '', $query);

        // Remplacer SELECT ... FROM par SELECT COUNT(*) FROM
        $query = preg_replace('/SELECT\s+.*?\s+FROM/i', 'SELECT COUNT(*) FROM', $query);

        return $query;
    }

    /**
     * Optimiser les requêtes d'essais avec pagination
     */
    public static function getEssaisOptimized($page = 1, $pageSize = self::DEFAULT_PAGE_SIZE, $filters = [])
    {
        $query = "
            SELECT e.*, 
                   a.numero AS affaire_numero,
                   a.client,
                   COUNT(c.id) AS nombre_courbes,
                   r.rendement_global
            FROM essais e
            LEFT JOIN affaires a ON e.affaire_id = a.id
            LEFT JOIN courbes c ON e.id = c.essai_id
            LEFT JOIN rendements r ON e.id = r.essai_id
        ";

        $params = [];
        $whereConditions = [];

        // Appliquer les filtres
        if (!empty($filters['statut'])) {
            $whereConditions[] = "e.statut = ?";
            $params[] = $filters['statut'];
        }

        if (!empty($filters['type'])) {
            $whereConditions[] = "e.type LIKE ?";
            $params[] = '%' . $filters['type'] . '%';
        }

        if (!empty($filters['affaire_id'])) {
            $whereConditions[] = "e.affaire_id = ?";
            $params[] = $filters['affaire_id'];
        }

        if (!empty($whereConditions)) {
            $query .= " WHERE " . implode(" AND ", $whereConditions);
        }

        $query .= " GROUP BY e.id ORDER BY e.date_essai DESC";

        return self::paginate($query, $params, $page, $pageSize);
    }

    /**
     * Optimiser les requêtes de PV avec pagination
     */
    public static function getPVOptimized($page = 1, $pageSize = self::DEFAULT_PAGE_SIZE, $filters = [])
    {
        $query = "
            SELECT p.*, 
                   a.numero AS affaire_numero,
                   a.client,
                   e.type AS essai_type,
                   u.username AS created_by_name
            FROM pv p
            LEFT JOIN essais e ON p.essai_id = e.id
            LEFT JOIN affaires a ON e.affaire_id = a.id
            LEFT JOIN users u ON p.created_by = u.id
        ";

        $params = [];
        $whereConditions = [];

        // Appliquer les filtres
        if (!empty($filters['statut'])) {
            $whereConditions[] = "p.statut = ?";
            $params[] = $filters['statut'];
        }

        if (!empty($filters['numero'])) {
            $whereConditions[] = "p.numero LIKE ?";
            $params[] = '%' . $filters['numero'] . '%';
        }

        if (!empty($whereConditions)) {
            $query .= " WHERE " . implode(" AND ", $whereConditions);
        }

        $query .= " ORDER BY p.date_creation DESC";

        return self::paginate($query, $params, $page, $pageSize);
    }

    /**
     * Optimiser la génération de PDF pour de gros documents
     */
    public static function optimizePDFGeneration($content, $options = [])
    {
        // Limiter la taille du contenu si nécessaire
        $maxContentLength = $options['max_content_length'] ?? 100000; // 100KB

        if (strlen($content) > $maxContentLength) {
            // Tronquer le contenu et ajouter une note
            $content = substr($content, 0, $maxContentLength);
            $content .= "\n\n[Contenu tronqué pour optimiser la génération PDF]";
        }

        // Optimiser les images si présentes
        $content = self::optimizeImagesInContent($content);

        return $content;
    }

    /**
     * Optimiser les images dans le contenu HTML
     */
    private static function optimizeImagesInContent($content)
    {
        // Remplacer les images trop grandes par des versions optimisées
        $content = preg_replace_callback(
            '/<img[^>]+src="([^"]+)"[^>]*>/i',
            function ($matches) {
                // Ici on pourrait implémenter une logique de redimensionnement d'image
                return $matches[0]; // Pour l'instant, on retourne l'image telle quelle
            },
            $content
        );

        return $content;
    }

    /**
     * Nettoyer le cache
     */
    public static function clearCache()
    {
        self::$cache = [];
    }

    /**
     * Obtenir les statistiques de performance
     */
    public static function getPerformanceStats()
    {
        return [
            'cache_entries' => count(self::$cache),
            'memory_usage' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true),
            'cache_keys' => array_keys(self::$cache)
        ];
    }
}
