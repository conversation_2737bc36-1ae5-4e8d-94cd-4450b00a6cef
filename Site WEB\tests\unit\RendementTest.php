<?php
require_once(__DIR__ . '/../../lib/rendement.php');
require_once(__DIR__ . '/../../lib/essais.php');
require_once(__DIR__ . '/../../lib/affaires.php');
require_once(__DIR__ . '/../../lib/courbes.php');
require_once(__DIR__ . '/TestBase.php');

class RendementTest extends TestBase
{
    private $test_affaire_id;
    private $test_essai_id;

    public function testCalculerRendement()
    {
        $result = Rendement::calculerRendement($this->test_essai_id, 1);

        $this->assertTrue($result['success'], 'Le calcul de rendement devrait réussir');
        $this->assertNotNull($result['rendement_id'], 'Un ID de rendement devrait être retourné');
        $this->assertIsArray($result['rendements'], 'Les rendements devraient être un tableau');
        $this->assertIsArray($result['donnees_calcul'], 'Les données de calcul devraient être un tableau');

        // Vérifier les clés des rendements
        $rendements = $result['rendements'];
        $this->assertTrue(isset($rendements['rendement_volumetrique']), 'Rendement volumétrique devrait être présent');
        $this->assertTrue(isset($rendements['rendement_mecanique']), 'Rendement mécanique devrait être présent');
        $this->assertTrue(isset($rendements['rendement_global']), 'Rendement global devrait être présent');

        // Vérifier que les valeurs sont dans des plages raisonnables
        $this->assertGreaterThan(0, $rendements['rendement_volumetrique'], 'Rendement volumétrique devrait être positif');
        $this->assertGreaterThan(0, $rendements['rendement_mecanique'], 'Rendement mécanique devrait être positif');
        $this->assertGreaterThan(0, $rendements['rendement_global'], 'Rendement global devrait être positif');
    }

    public function testCalculerRendementSansCourbes()
    {
        // Créer un essai sans courbes
        $essai_id = $this->createTestEssai('Test Sans Courbes');

        $result = Rendement::calculerRendement($essai_id, 1);

        $this->assertFalse($result['success'], 'Le calcul devrait échouer sans courbes');
        $this->assertContains('Courbes CPA et CPB requises', $result['message'], 'Le message d\'erreur devrait mentionner les courbes manquantes');

        // Nettoyer
        Essai::delete($essai_id);
    }

    private function createTestEssai($type = 'Test Rendement')
    {
        $parametre = json_encode(['puissance' => 1000]); // 1000W
        $date_essai = date('Y-m-d');

        Essai::create($this->test_affaire_id, $type, $parametre, $date_essai);
        $essais = Essai::getByAffaireId($this->test_affaire_id);
        foreach ($essais as $essai) {
            if ($essai['type'] === $type) {
                return $essai['id'];
            }
        }
        return null;
    }

    public function testCalculerRendementEssaiInexistant()
    {
        $result = Rendement::calculerRendement(99999, 1);

        $this->assertFalse($result['success'], 'Le calcul devrait échouer pour un essai inexistant');
        $this->assertContains('Essai non trouvé', $result['message'], 'Le message d\'erreur devrait mentionner l\'essai non trouvé');
    }

    public function testGetRendementByEssaiId()
    {
        // Calculer un rendement d'abord
        Rendement::calculerRendement($this->test_essai_id, 1);

        $rendement = Rendement::getByEssaiId($this->test_essai_id);

        $this->assertNotNull($rendement, 'Le rendement devrait être trouvé');
        $this->assertEquals($this->test_essai_id, $rendement['essai_id']);
        $this->assertNotNull($rendement['rendement_global'], 'Le rendement global devrait être présent');
        $this->assertNotNull($rendement['date_calcul'], 'La date de calcul devrait être présente');
    }

    public function testGetRendementByEssaiIdInexistant()
    {
        $rendement = Rendement::getByEssaiId(99999);
        $this->assertNull($rendement, 'Aucun rendement ne devrait être trouvé pour un essai inexistant');
    }

    public function testGetAllRendements()
    {
        // Calculer un rendement d'abord
        Rendement::calculerRendement($this->test_essai_id, 1);

        $rendements = Rendement::getAll(10, 0);

        $this->assertIsArray($rendements, 'getAll devrait retourner un tableau');
        $this->assertGreaterThan(0, count($rendements), 'Il devrait y avoir au moins un rendement');

        // Vérifier la structure du premier rendement
        if (count($rendements) > 0) {
            $rendement = $rendements[0];
            $this->assertTrue(isset($rendement['essai_id']), 'L\'ID d\'essai devrait être présent');
            $this->assertTrue(isset($rendement['rendement_global']), 'Le rendement global devrait être présent');
        }
    }

    public function testGetStatistiquesAffaire()
    {
        // Calculer un rendement d'abord
        Rendement::calculerRendement($this->test_essai_id, 1);

        $stats = Rendement::getStatistiquesAffaire($this->test_affaire_id);

        $this->assertIsArray($stats, 'getStatistiquesAffaire devrait retourner un tableau');
        $this->assertTrue(isset($stats['rendement_moyen']), 'Le rendement moyen devrait être présent');
        $this->assertTrue(isset($stats['rendement_max']), 'Le rendement max devrait être présent');
        $this->assertTrue(isset($stats['rendement_min']), 'Le rendement min devrait être présent');
        $this->assertTrue(isset($stats['nombre_calculs']), 'Le nombre de calculs devrait être présent');

        $this->assertGreaterThan(0, $stats['nombre_calculs'], 'Il devrait y avoir au moins un calcul');
    }

    public function testDeleteRendement()
    {
        // Calculer un rendement d'abord
        $result = Rendement::calculerRendement($this->test_essai_id, 1);
        $rendement_id = $result['rendement_id'];

        $delete_result = Rendement::delete($rendement_id);
        $this->assertTrue($delete_result, 'La suppression devrait réussir');

        // Vérifier que le rendement n'existe plus
        $rendement = Rendement::getByEssaiId($this->test_essai_id);
        $this->assertNull($rendement, 'Le rendement supprimé ne devrait plus exister');
    }

    public function runAllTests()
    {
        $this->setUp();

        echo "<h3>Tests Rendement</h3>";

        $this->runTest('testCalculerRendement');
        $this->runTest('testCalculerRendementSansCourbes');
        $this->runTest('testCalculerRendementEssaiInexistant');
        $this->runTest('testGetRendementByEssaiId');
        $this->runTest('testGetRendementByEssaiIdInexistant');
        $this->runTest('testGetAllRendements');
        $this->runTest('testGetStatistiquesAffaire');
        $this->runTest('testDeleteRendement');

        $this->tearDown();

        return $this->getResults();
    }

    public function setUp()
    {
        parent::setUp();
        // Créer une affaire de test
        $this->test_affaire_id = $this->createTestAffaire();
        // Créer un essai de test
        $this->test_essai_id = $this->createTestEssai();
        // Créer des courbes de test
        $this->createTestCourbes();
    }

    private function createTestAffaire()
    {
        $numero = 'TEST-AFFAIRE-RENDEMENT-' . time();
        Affaire::create($numero, 'Client Test', 'Description test', ['test'], 1);
        $affaires = Affaire::getAll();
        foreach ($affaires as $affaire) {
            if ($affaire['numero'] === $numero) {
                return $affaire['id'];
            }
        }
        return null;
    }

    private function createTestCourbes()
    {
        // Créer des données de test pour CPA
        $donnees_cpa = json_encode([
            ['pressure_pascal' => 100000, 'flow_lpm' => 10, 'timestamp' => '2024-01-01 10:00:00'],
            ['pressure_pascal' => 105000, 'flow_lpm' => 11, 'timestamp' => '2024-01-01 10:00:01'],
            ['pressure_pascal' => 102000, 'flow_lpm' => 10.5, 'timestamp' => '2024-01-01 10:00:02']
        ]);

        // Créer des données de test pour CPB
        $donnees_cpb = json_encode([
            ['pressure_pascal' => 95000, 'flow_lpm' => 9.5, 'timestamp' => '2024-01-01 10:00:00'],
            ['pressure_pascal' => 98000, 'flow_lpm' => 10, 'timestamp' => '2024-01-01 10:00:01'],
            ['pressure_pascal' => 96000, 'flow_lpm' => 9.8, 'timestamp' => '2024-01-01 10:00:02']
        ]);

        Courbe::create($this->test_essai_id, 'CPA', $donnees_cpa);
        Courbe::create($this->test_essai_id, 'CPB', $donnees_cpb);
    }

    public function tearDown()
    {
        // Nettoyer les données de test
        if ($this->test_essai_id) {
            // Supprimer les rendements associés
            $rendement = Rendement::getByEssaiId($this->test_essai_id);
            if ($rendement) {
                Rendement::delete($rendement['id']);
            }
            // Supprimer les courbes
            Courbe::deleteByEssaiId($this->test_essai_id);
            Essai::delete($this->test_essai_id);
        }
        if ($this->test_affaire_id) {
            Affaire::delete($this->test_affaire_id);
        }
        parent::tearDown();
    }
}
