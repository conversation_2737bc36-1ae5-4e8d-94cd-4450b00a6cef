<?php
/**
 * Script de test pour vérifier la cohérence physique des données générées
 * Test des améliorations apportées au générateur de données
 */

require_once(__DIR__ . '/lib/data_generator.php');

// Simuler une session utilisateur pour les tests
session_start();
$_SESSION['user'] = [
    'id' => 1,
    'username' => 'test_user',
    'role' => 'controleur'
];

echo "=== TEST DE COHÉRENCE PHYSIQUE DES DONNÉES GÉNÉRÉES ===\n\n";

try {
    $generator = new DataGenerator();
    
    // Test 1: Génération de propriétés réalistes
    echo "1. Test de génération de propriétés réalistes:\n";
    echo "   Génération de 5 jeux de paramètres...\n";
    
    for ($i = 1; $i <= 5; $i++) {
        echo "\n   Jeu $i:\n";
        
        // Utiliser la réflexion pour accéder à la méthode privée
        $reflection = new ReflectionClass($generator);
        $method = $reflection->getMethod('generateRealisticProperties');
        $method->setAccessible(true);
        
        $parametres = $method->invoke($generator);
        
        foreach ($parametres as $param => $valeur) {
            echo "     $param: $valeur\n";
        }
        
        // Test de validation physique
        $validateMethod = $reflection->getMethod('validatePhysicalConsistency');
        $validateMethod->setAccessible(true);
        
        $validation = $validateMethod->invoke($generator, $parametres);
        
        if ($validation['valid']) {
            echo "     ✅ Validation physique: CONFORME\n";
        } else {
            echo "     ❌ Validation physique: ERREURS DÉTECTÉES\n";
            foreach ($validation['errors'] as $error) {
                echo "       - $error\n";
            }
        }
    }
    
    // Test 2: Test des différents pourcentages de variance
    echo "\n\n2. Test des pourcentages de variance:\n";
    $variances = [5, 10, 15, 25];
    
    foreach ($variances as $variance) {
        echo "\n   Test avec variance ±$variance%:\n";
        
        $old_variance = $generator->setVariancePercentage($variance);
        
        // Générer des points de données séquentiels
        $sequentialMethod = $reflection->getMethod('generateSequentialDataPoints');
        $sequentialMethod->setAccessible(true);
        
        $start_time = new DateTime();
        $donnees = $sequentialMethod->invoke($generator, 10, 150000, 15, 50, $start_time);
        
        // Calculer la variance réelle entre points consécutifs
        $variances_reelles = [];
        for ($i = 1; $i < count($donnees); $i++) {
            $prev_pressure = $donnees[$i-1]['pressure_pascal'];
            $curr_pressure = $donnees[$i]['pressure_pascal'];
            $variance_reelle = abs($curr_pressure - $prev_pressure) / $prev_pressure * 100;
            $variances_reelles[] = $variance_reelle;
        }
        
        $variance_moyenne = array_sum($variances_reelles) / count($variances_reelles);
        $variance_max = max($variances_reelles);
        
        echo "     Variance moyenne: " . round($variance_moyenne, 2) . "%\n";
        echo "     Variance maximale: " . round($variance_max, 2) . "%\n";
        
        if ($variance_moyenne <= $variance * 1.2) { // Tolérance de 20%
            echo "     ✅ Variance conforme à la configuration\n";
        } else {
            echo "     ❌ Variance excessive par rapport à la configuration\n";
        }
        
        // Restaurer la variance précédente
        $generator->setVariancePercentage($old_variance);
    }
    
    // Test 3: Test des corrélations physiques
    echo "\n\n3. Test des corrélations physiques:\n";
    
    echo "\n   Test de la corrélation viscosité-température:\n";
    $temperatures = [20, 40, 60, 80];
    
    foreach ($temperatures as $temp) {
        // Calculer la viscosité attendue selon la formule exponentielle
        $viscosite_base = 46;
        $alpha = 0.02;
        $temp_ref = 40;
        $viscosite_attendue = $viscosite_base * exp(-$alpha * ($temp - $temp_ref));
        
        echo "     T=$temp°C => Viscosité attendue: " . round($viscosite_attendue, 1) . " cSt\n";
    }
    
    echo "\n   Test de la corrélation puissance-débit-pression:\n";
    $tests_puissance = [
        ['debit' => 10, 'pression' => 6],
        ['debit' => 15, 'pression' => 8],
        ['debit' => 20, 'pression' => 10]
    ];
    
    foreach ($tests_puissance as $test) {
        $debit = $test['debit'];
        $pression = $test['pression'];
        $densite = 850;
        $g = 9.81;
        $rendement = 0.85;
        
        $puissance_attendue = ($debit * $pression * $densite * $g) / ($rendement * 60000);
        
        echo "     Q={$debit}L/min, P={$pression}bar => Puissance attendue: " . round($puissance_attendue, 0) . " W\n";
    }
    
    echo "\n\n=== TESTS TERMINÉS ===\n";
    echo "Les améliorations de cohérence physique ont été testées avec succès.\n";
    
} catch (Exception $e) {
    echo "❌ ERREUR LORS DES TESTS: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
?>
