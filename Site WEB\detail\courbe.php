<?php
session_start();
require_once(__DIR__ . '/../lib/courbes.php');
require_once(__DIR__ . '/../lib/essais.php');
require_once(__DIR__ . '/../lib/performance.php');

if (!isset($_SESSION['user'])) {
    header('Location: /auth/login.php');
    exit;
}

if (!isset($_GET['essai'])) {
    header('Location: /essais.php');
    exit;
}

$essaiId = $_GET['essai'];
$typeCourbe = isset($_GET['type']) ? $_GET['type'] : null; // Type optionnel maintenant

if (!Essai::getById($essaiId)) {
    header('Location: /essais.php');
    exit;
}

// Paramètres de pagination et filtrage
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$pageSize = isset($_GET['pageSize']) ? min(200, max(10, intval($_GET['pageSize']))) : 50;
$showOptimized = isset($_GET['optimized']) && $_GET['optimized'] === '1';
$maxPoints = isset($_GET['maxPoints']) ? min(10000, max(100, intval($_GET['maxPoints']))) : 1000;

// Récupérer toutes les courbes pour l'essai (pas seulement un type)
if ($showOptimized) {
    $courbesOptimisees = Performance::getCourbesOptimized($essaiId, null, $maxPoints);
    $useOptimization = true;
} else {
    $courbesOptimisees = Courbe::getByEssaiId($essaiId);
    $useOptimization = false;
}

if (!$courbesOptimisees) {
    header('Location: /detail/essai.php?id=' . $essaiId);
    exit;
}

// Grouper les courbes par type comme dans essai.php
$courbeGroupes = [];

foreach ($courbesOptimisees as $courbe) {
    $type = $courbe['type_courbe'];
    if (!isset($courbeGroupes[$type])) {
        $courbeGroupes[$type] = [
            'donnees' => [],
            'timestamps' => [],
            'points_originaux' => $useOptimization ? ($courbe['points_originaux'] ?? 0) : 0,
            'points_optimises' => $useOptimization ? ($courbe['points_optimises'] ?? 0) : 0
        ];
    }

    // Vérifier si les données sont dans le format JSON (nouvelles données cohérentes)
    if (!empty($courbe['donnees'])) {
        $donneesJson = json_decode($courbe['donnees'], true);
        if (is_array($donneesJson) && !empty($donneesJson)) {
            // Utiliser les données JSON détaillées (nouveau format cohérent)
            foreach ($donneesJson as $point) {
                if (isset($point['timestamp']) && isset($point['pressure_bar'])) {
                    $courbeGroupes[$type]['timestamps'][] = $point['timestamp'];
                    $courbeGroupes[$type]['donnees'][] = floatval($point['pressure_bar']);
                }
            }
            // Mettre à jour le nombre de points si pas déjà défini
            if ($courbeGroupes[$type]['points_originaux'] == 0) {
                $courbeGroupes[$type]['points_originaux'] = count($donneesJson);
                $courbeGroupes[$type]['points_optimises'] = count($donneesJson);
            }
        } else {
            // Fallback vers la mesure unique si JSON invalide
            $courbeGroupes[$type]['timestamps'][] = $courbe['TIMESTAMP'];
            $courbeGroupes[$type]['donnees'][] = floatval($courbe['mesure']);
        }
    } else {
        // Format ancien - utiliser la mesure unique
        $courbeGroupes[$type]['timestamps'][] = $courbe['TIMESTAMP'];
        $courbeGroupes[$type]['donnees'][] = floatval($courbe['mesure']);
    }
}

$courbes = $courbeGroupes;

// Si un type spécifique est demandé, récupérer les données paginées pour le tableau
if ($typeCourbe) {
    $paginatedData = Courbe::getByEssaiIdPaginated($essaiId, $typeCourbe, $page, $pageSize);
    $statistics = Courbe::getStatistics($essaiId, $typeCourbe);
} else {
    $paginatedData = null;
    $statistics = null;
}

// Gérer l'export CSV si demandé
if (isset($_GET['export']) && $_GET['export'] === 'csv') {
    $allData = Courbe::getByEssaiId($essaiId, $typeCourbe);

    header('Content-Type: text/csv');
    if ($typeCourbe) {
        header('Content-Disposition: attachment; filename="courbe_' . $typeCourbe . '_essai_' . $essaiId . '.csv"');
    } else {
        header('Content-Disposition: attachment; filename="courbes_essai_' . $essaiId . '.csv"');
    }

    echo "Type_Courbe,Timestamp,Mesure\n";
    foreach ($allData as $row) {
        echo $row['type_courbe'] . ',' . $row['TIMESTAMP'] . ',' . $row['mesure'] . "\n";
    }
    exit;
}

ob_start();
?>

    <script src="https://cdn.jsdelivr.net/npm/apexcharts@3.46.0/dist/apexcharts.min.js"></script>

    <div class="p-4">
        <!-- En-tête avec contrôles -->
        <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-6 gap-4">
            <div>
                <?php if ($typeCourbe): ?>
                    <h2 class="text-2xl font-bold dark:text-white">Détails de la courbe
                        "<?php echo htmlspecialchars($typeCourbe); ?>"</h2>
                <?php else: ?>
                    <h2 class="text-2xl font-bold dark:text-white">Toutes les courbes de l'essai</h2>
                <?php endif; ?>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">Essai #<?php echo $essaiId; ?></p>
            </div>

            <div class="flex flex-wrap gap-2">
                <!-- Bouton d'optimisation -->
                <button id="toggleOptimization"
                        class="<?php echo $showOptimized ? 'bg-green-700 hover:bg-green-800' : 'bg-gray-700 hover:bg-gray-800'; ?> text-white font-medium rounded-lg text-sm px-4 py-2 focus:ring-4 focus:ring-gray-300">
                    <?php echo $showOptimized ? 'Mode Normal' : 'Mode Optimisé'; ?>
                </button>

                <!-- Bouton d'export -->
                <button id="exportData"
                        class="text-white bg-purple-700 hover:bg-purple-800 focus:ring-4 focus:ring-purple-300 font-medium rounded-lg text-sm px-4 py-2">
                    Exporter CSV
                </button>

                <!-- Bouton retour -->
                <a href="/detail/essai.php?id=<?php echo $essaiId; ?>"
                   class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-4 py-2">
                    Retour
                </a>
            </div>
        </div>

        <!-- Affichage des courbes multiples -->
        <?php if ($courbes): ?>
            <div class="grid grid-cols-1 gap-6 mb-6">
                <?php foreach ($courbes as $type => $courbeGroup): ?>
                    <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
                        <div class="flex justify-between items-center mb-4">
                            <div class="grid gap-4 grid-cols-3">
                                <div>
                                    <h5 class="inline-flex items-center text-gray-500 dark:text-gray-400 leading-none font-normal mb-2">
                                        Mesure</h5>
                                    <p class="text-gray-900 dark:text-white text-2xl leading-none font-bold"><?php echo htmlspecialchars($type); ?></p>
                                </div>
                                <div>
                                    <h5 class="inline-flex items-center text-gray-500 dark:text-gray-400 leading-none font-normal mb-2">
                                        Points affichés</h5>
                                    <p class="text-gray-900 dark:text-white text-2xl leading-none font-bold"><?php echo count($courbeGroup['donnees']); ?></p>
                                </div>
                                <?php if ($useOptimization && isset($courbeGroup['points_originaux']) && $courbeGroup['points_originaux'] > 0): ?>
                                    <div>
                                        <h5 class="inline-flex items-center text-gray-500 dark:text-gray-400 leading-none font-normal mb-2">
                                            Optimisation</h5>
                                        <p class="text-green-600 dark:text-green-400 text-sm leading-none font-medium">
                                            <?php echo round(($courbeGroup['points_optimises'] / $courbeGroup['points_originaux']) * 100, 1); ?>
                                            % des données
                                        </p>
                                    </div>
                                <?php elseif (!$useOptimization): ?>
                                    <div>
                                        <h5 class="inline-flex items-center text-gray-500 dark:text-gray-400 leading-none font-normal mb-2">
                                            Mode</h5>
                                        <p class="text-blue-600 dark:text-blue-400 text-sm leading-none font-medium">
                                            Données complètes
                                        </p>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="pt-5 flex flex-col gap-2">
                                <a href="/detail/courbe.php?essai=<?php echo $essaiId; ?>&type=<?php echo urlencode($type); ?>"
                                   class="px-4 py-2 text-sm font-medium text-white inline-flex items-center bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 rounded-lg text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                                    <svg class="w-3.5 h-3.5 text-white me-2" aria-hidden="true"
                                         xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 20">
                                        <path d="M14.066 0H7v5a2 2 0 0 1-2 2H0v11a1.97 1.97 0 0 0 1.934 2h12.132A1.97 1.97 0 0 0 16 18V2a1.97 1.97 0 0 0-1.934-2Zm-3 15H4.828a1 1 0 0 1 0-2h6.238a1 1 0 0 1 0 2Zm0-4H4.828a1 1 0 0 1 0-2h6.238a1 1 0 1 1 0 2Z"/>
                                        <path d="M5 5V.13a2.96 2.96 0 0 0-1.293.749L.879 3.707A2.98 2.98 0 0 0 .13 5H5Z"/>
                                    </svg>
                                    Détails
                                </a>
                                <button onclick="exportCourbeData('<?php echo $type; ?>', <?php echo $essaiId; ?>)"
                                        class="px-4 py-2 text-sm font-medium text-white inline-flex items-center bg-purple-700 hover:bg-purple-800 focus:ring-4 focus:outline-none focus:ring-purple-300 rounded-lg text-center">
                                    <svg class="w-3.5 h-3.5 text-white me-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M13 8V2H7v6H2l8 8 8-8h-5zM0 18h20v2H0v-2z"/>
                                    </svg>
                                    CSV
                                </button>
                            </div>
                        </div>
                        <div id="chart_<?php echo $type . '_' . $essaiId; ?>" class="w-full h-96"></div>
                    </div>

                    <script>
                        var options_<?php echo str_replace(['-', ' ', '.'], '_', $type); ?> = {
                            chart: {
                                type: 'line',
                                height: 384,
                                maxWidth: "100%",
                                fontFamily: "Inter, sans-serif",
                                dropShadow: {
                                    enabled: false,
                                },
                                toolbar: {
                                    show: true,
                                    tools: {
                                        download: true,
                                        selection: true,
                                        zoom: true,
                                        zoomin: true,
                                        zoomout: true,
                                        pan: true,
                                        reset: true
                                    }
                                },
                                zoom: {
                                    enabled: true,
                                    type: 'x',
                                    autoScaleYaxis: true
                                },
                                animations: {
                                    enabled: true,
                                    easing: 'easeinout',
                                    speed: 800
                                }
                            },
                            tooltip: {
                                enabled: true,
                                shared: false,
                                intersect: false,
                                x: {
                                    show: true,
                                    format: 'dd/MM/yyyy HH:mm:ss'
                                },
                                y: {
                                    formatter: function (value) {
                                        return value.toFixed(2) + ' (unité)';
                                    }
                                }
                            },
                            dataLabels: {
                                enabled: false,
                            },
                            stroke: {
                                width: 2,
                                curve: 'smooth'
                            },
                            grid: {
                                show: true,
                                strokeDashArray: 4,
                                padding: {
                                    left: 2,
                                    right: 2,
                                    top: -26
                                },
                            },
                            series: [{
                                name: '<?php echo htmlspecialchars($type); ?>',
                                data: <?php echo json_encode($courbeGroup['donnees']); ?>,
                                color: "#1A56DB"
                            }],
                            xaxis: {
                                categories: <?php echo json_encode($courbeGroup['timestamps']); ?>,
                                title: {
                                    text: 'Temps',
                                    style: {
                                        fontSize: '14px',
                                        fontWeight: 600,
                                        color: '#374151'
                                    }
                                },
                                labels: {
                                    show: true,
                                    rotate: -45,
                                    style: {
                                        fontFamily: "Inter, sans-serif",
                                        cssClass: 'text-xs font-normal fill-gray-500 dark:fill-gray-400'
                                    },
                                    formatter: function (value) {
                                        // Afficher seulement l'heure pour les timestamps
                                        return new Date(value).toLocaleTimeString();
                                    }
                                },
                                axisBorder: {
                                    show: true,
                                },
                                axisTicks: {
                                    show: true,
                                },
                            },
                            yaxis: {
                                title: {
                                    text: 'Mesure',
                                    style: {
                                        fontSize: '14px',
                                        fontWeight: 600,
                                        color: '#374151'
                                    }
                                },
                                show: true,
                                labels: {
                                    formatter: function (value) {
                                        return value.toFixed(2);
                                    }
                                }
                            },
                            legend: {
                                show: true,
                                position: 'top',
                                horizontalAlign: 'right'
                            },
                            markers: {
                                size: 0,
                                hover: {
                                    size: 6
                                }
                            }
                        };

                        var chart_<?php echo str_replace(['-', ' ', '.'], '_', $type); ?> = new ApexCharts(document.querySelector("#chart_<?php echo $type . '_' . $essaiId; ?>"), options_<?php echo str_replace(['-', ' ', '.'], '_', $type); ?>);
                        chart_<?php echo str_replace(['-', ' ', '.'], '_', $type); ?>.render();
                    </script>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow mb-6">
                <p class="text-gray-500 dark:text-gray-400">Aucune courbe disponible pour cet essai.</p>
            </div>
        <?php endif; ?>

        <!-- Statistiques (seulement si un type spécifique est sélectionné) -->
        <?php if ($typeCourbe && $statistics): ?>
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-6">
                <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
                    <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Points</div>
                    <div class="text-2xl font-bold text-gray-900 dark:text-white"><?php echo number_format($statistics['count']); ?></div>
                </div>
                <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
                    <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Minimum</div>
                    <div class="text-2xl font-bold text-blue-600 dark:text-blue-400"><?php echo number_format($statistics['min'], 2); ?></div>
                </div>
                <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
                    <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Maximum</div>
                    <div class="text-2xl font-bold text-red-600 dark:text-red-400"><?php echo number_format($statistics['max'], 2); ?></div>
                </div>
                <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
                    <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Moyenne</div>
                    <div class="text-2xl font-bold text-green-600 dark:text-green-400"><?php echo number_format($statistics['average'], 2); ?></div>
                </div>
                <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
                    <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Médiane</div>
                    <div class="text-2xl font-bold text-purple-600 dark:text-purple-400"><?php echo number_format($statistics['median'], 2); ?></div>
                </div>
                <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
                    <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Écart-type</div>
                    <div class="text-2xl font-bold text-orange-600 dark:text-orange-400"><?php echo number_format($statistics['std_deviation'], 2); ?></div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Graphique principal (seulement si un type spécifique est sélectionné) -->
        <?php if ($typeCourbe && isset($courbes[$typeCourbe])): ?>
            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow mb-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold dark:text-white">Graphique de mesures - <?php echo htmlspecialchars($typeCourbe); ?></h3>
                    <?php if ($showOptimized && isset($courbes[$typeCourbe]['points_originaux'])): ?>
                        <span class="text-sm text-gray-500 dark:text-gray-400">
                        Optimisé: <?php echo number_format($courbes[$typeCourbe]['points_optimises']); ?> / <?php echo number_format($courbes[$typeCourbe]['points_originaux']); ?> points
                    </span>
                    <?php endif; ?>
                </div>
                <div id="chart_courbe_<?php echo $essaiId . '_' . htmlspecialchars($typeCourbe); ?>"
                     class="w-full h-96"></div>
            </div>
        <?php endif; ?>

        <!-- Données brutes avec pagination (seulement si un type spécifique est sélectionné) -->
        <?php if ($typeCourbe && $paginatedData): ?>
            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold dark:text-white">Données brutes - <?php echo htmlspecialchars($typeCourbe); ?></h3>
                    <div class="flex items-center gap-2">
                        <label for="pageSize" class="text-sm text-gray-600 dark:text-gray-400">Lignes par page:</label>
                        <select id="pageSize" onchange="changePageSize(this.value)"
                                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-1 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                            <option value="10" <?php echo $pageSize == 10 ? 'selected' : ''; ?>>10</option>
                            <option value="25" <?php echo $pageSize == 25 ? 'selected' : ''; ?>>25</option>
                            <option value="50" <?php echo $pageSize == 50 ? 'selected' : ''; ?>>50</option>
                            <option value="100" <?php echo $pageSize == 100 ? 'selected' : ''; ?>>100</option>
                        </select>
                    </div>
                </div>

            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                    <tr>
                        <th scope="col" class="px-6 py-3">#</th>
                        <th scope="col" class="px-6 py-3">Timestamp</th>
                        <th scope="col" class="px-6 py-3">Mesure</th>
                        <th scope="col" class="px-6 py-3">Variation</th>
                    </tr>
                    </thead>
                    <tbody>
                    <?php
                    $previousMesure = null;
                    $rowNumber = ($page - 1) * $pageSize + 1;
                    foreach ($paginatedData['data'] as $courbe):
                        $variation = $previousMesure !== null ? $courbe['mesure'] - $previousMesure : 0;
                        $variationClass = $variation > 0 ? 'text-green-600' : ($variation < 0 ? 'text-red-600' : 'text-gray-500');
                        ?>
                        <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                            <td class="px-6 py-4 font-medium"><?php echo $rowNumber++; ?></td>
                            <td class="px-6 py-4"><?php echo htmlspecialchars($courbe['TIMESTAMP']); ?></td>
                            <td class="px-6 py-4 font-mono"><?php echo number_format($courbe['mesure'], 2); ?></td>
                            <td class="px-6 py-4 font-mono <?php echo $variationClass; ?>">
                                <?php echo $previousMesure !== null ? ($variation >= 0 ? '+' : '') . number_format($variation, 2) : '-'; ?>
                            </td>
                        </tr>
                        <?php
                        $previousMesure = $courbe['mesure'];
                    endforeach;
                    ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($paginatedData['pagination']['total_pages'] > 1): ?>
                <div class="flex items-center justify-between mt-4">
                    <div class="text-sm text-gray-700 dark:text-gray-400">
                        Affichage de <?php echo (($page - 1) * $pageSize) + 1; ?> à
                        <?php echo min($page * $pageSize, $paginatedData['pagination']['total_items']); ?>
                        sur <?php echo number_format($paginatedData['pagination']['total_items']); ?> résultats
                    </div>

                    <div class="flex gap-1">
                        <?php if ($paginatedData['pagination']['has_prev']): ?>
                            <a href="?essai=<?php echo $essaiId; ?>&type=<?php echo urlencode($typeCourbe); ?>&page=<?php echo $page - 1; ?>&pageSize=<?php echo $pageSize; ?><?php echo $showOptimized ? '&optimized=1' : ''; ?>"
                               class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                                Précédent
                            </a>
                        <?php endif; ?>

                        <?php
                        $startPage = max(1, $page - 2);
                        $endPage = min($paginatedData['pagination']['total_pages'], $page + 2);

                        for ($i = $startPage; $i <= $endPage; $i++):
                            ?>
                            <a href="?essai=<?php echo $essaiId; ?>&type=<?php echo urlencode($typeCourbe); ?>&page=<?php echo $i; ?>&pageSize=<?php echo $pageSize; ?><?php echo $showOptimized ? '&optimized=1' : ''; ?>"
                               class="px-3 py-2 text-sm font-medium <?php echo $i == $page ? 'text-blue-600 bg-blue-50 border-blue-300 dark:bg-gray-700 dark:text-white' : 'text-gray-500 bg-white border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white'; ?> border rounded-lg">
                                <?php echo $i; ?>
                            </a>
                        <?php endfor; ?>

                        <?php if ($paginatedData['pagination']['has_next']): ?>
                            <a href="?essai=<?php echo $essaiId; ?>&type=<?php echo urlencode($typeCourbe); ?>&page=<?php echo $page + 1; ?>&pageSize=<?php echo $pageSize; ?><?php echo $showOptimized ? '&optimized=1' : ''; ?>"
                               class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                                Suivant
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>

    <script>
        <?php if ($typeCourbe && isset($courbes[$typeCourbe])): ?>
        // Configuration du graphique avec fonctionnalités avancées (pour type spécifique)
        var options = {
            chart: {
                type: 'line',
                height: 384,
                maxWidth: "100%",
                fontFamily: "Inter, sans-serif",
                dropShadow: {
                    enabled: false,
                },
                toolbar: {
                    show: true,
                    tools: {
                        download: true,
                        selection: true,
                        zoom: true,
                        zoomin: true,
                        zoomout: true,
                        pan: true,
                        reset: true
                    }
                },
                zoom: {
                    enabled: true,
                    type: 'x',
                    autoScaleYaxis: true
                },
                animations: {
                    enabled: true,
                    easing: 'easeinout',
                    speed: 800
                }
            },
            tooltip: {
                enabled: true,
                shared: false,
                intersect: false,
                x: {
                    show: true,
                    format: 'dd/MM/yyyy HH:mm:ss'
                },
                y: {
                    formatter: function (value) {
                        return value.toFixed(2) + ' (unité)';
                    }
                }
            },
            dataLabels: {
                enabled: false,
            },
            stroke: {
                width: 2,
                curve: 'smooth'
            },
            grid: {
                show: true,
                strokeDashArray: 4,
                padding: {
                    left: 2,
                    right: 2,
                    top: -26
                },
            },
            series: [{
                name: '<?php echo htmlspecialchars($typeCourbe); ?>',
                data: <?php echo json_encode($courbes[$typeCourbe]['donnees']); ?>,
                color: "#1A56DB"
            }],
            xaxis: {
                categories: <?php echo json_encode($courbes[$typeCourbe]['timestamps']); ?>,
                title: {
                    text: 'Temps',
                    style: {
                        fontSize: '14px',
                        fontWeight: 600,
                        color: '#374151'
                    }
                },
                labels: {
                    show: true,
                    rotate: -45,
                    style: {
                        fontFamily: "Inter, sans-serif",
                        cssClass: 'text-xs font-normal fill-gray-500 dark:fill-gray-400'
                    },
                    formatter: function (value) {
                        // Afficher seulement l'heure pour les timestamps
                        return new Date(value).toLocaleTimeString();
                    }
                },
                axisBorder: {
                    show: true,
                },
                axisTicks: {
                    show: true,
                },
            },
            yaxis: {
                title: {
                    text: 'Mesure',
                    style: {
                        fontSize: '14px',
                        fontWeight: 600,
                        color: '#374151'
                    }
                },
                show: true,
                labels: {
                    formatter: function (value) {
                        return value.toFixed(2);
                    }
                }
            },
            legend: {
                show: true,
                position: 'top',
                horizontalAlign: 'right'
            },
            markers: {
                size: 0,
                hover: {
                    size: 6
                }
            }
        };

        var chart = new ApexCharts(document.querySelector("#chart_courbe_<?php echo $essaiId . '_' . htmlspecialchars($typeCourbe); ?>"), options);
        chart.render();
        <?php endif; ?>

        // Fonctions JavaScript pour l'interactivité
        function changePageSize(newSize) {
            const url = new URL(window.location);
            url.searchParams.set('pageSize', newSize);
            url.searchParams.set('page', '1'); // Reset à la première page
            window.location.href = url.toString();
        }

        // Bouton d'optimisation
        document.getElementById('toggleOptimization')?.addEventListener('click', function () {
            const url = new URL(window.location);
            const isOptimized = url.searchParams.get('optimized') === '1';

            if (isOptimized) {
                url.searchParams.delete('optimized');
            } else {
                url.searchParams.set('optimized', '1');
            }

            window.location.href = url.toString();
        });

        // Export CSV
        document.getElementById('exportData')?.addEventListener('click', function () {
            <?php if ($typeCourbe && $paginatedData): ?>
            const data = <?php echo json_encode($paginatedData['data']); ?>;
            let csv = 'Timestamp,Mesure\n';

            data.forEach(function (row) {
                csv += row.TIMESTAMP + ',' + row.mesure + '\n';
            });

            const blob = new Blob([csv], {type: 'text/csv'});
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'courbe_<?php echo $typeCourbe; ?>_essai_<?php echo $essaiId; ?>.csv';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
            <?php else: ?>
            // Export toutes les courbes
            window.location.href = '?essai=<?php echo $essaiId; ?>&export=csv';
            <?php endif; ?>
        });

        // Fonction d'export pour courbes individuelles (utilisée dans les boutons des cartes)
        function exportCourbeData(type, essaiId) {
            window.location.href = '/detail/courbe.php?essai=' + essaiId + '&type=' + encodeURIComponent(type) + '&export=csv';
        }

        // Loading state pour les changements de page
        document.addEventListener('DOMContentLoaded', function () {
            const links = document.querySelectorAll('a[href*="page="]');
            links.forEach(link => {
                link.addEventListener('click', function () {
                    // Afficher un indicateur de chargement
                    const loader = document.createElement('div');
                    loader.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                    loader.innerHTML = '<div class="bg-white p-4 rounded-lg"><div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div></div>';
                    document.body.appendChild(loader);
                });
            });
        });
    </script>

<?php
$pageContent = ob_get_clean();
include '../layout.php';
?>