#include <Arduino.h>
#include "Sensors.h"
#include "Button.h"
#include <ArduinoJson.h>
#include <HardwareSerial.h>

// Constantes
constexpr int BUTTON_PIN = 2;
constexpr unsigned long DEBOUNCE_DELAY = 50;
constexpr unsigned long SENSOR_DISPLAY_INTERVAL = 500; // Temps entre les affichages des capteurs

// Commands that can be received
enum Command {
  GET_PRESSURE,
  GET_FLOW,
  GET_ALL
};

void setup() {
    Serial.begin(9600);
    Sensors::init();
    Button::init();
}

void sendResponse(const char* sensor, float value) {
  StaticJsonDocument<200> doc;
  doc["sensor"] = sensor;
  doc["value"] = value;
  doc["timestamp"] = millis();

  serializeJson(doc, Serial);
  Serial.println();
}

void handleCommand(const String& command) {
  if (command == "GET_PRESSURE") {
    sendResponse("pressure", Sensors::getPressurePercentage());
  }
  else if (command == "GET_FLOW") {
    sendResponse("flow", Sensors::getFlowPercentage());
  }
  else if (command == "GET_ALL") {
    StaticJsonDocument<200> doc;
    doc["pressure"] = Sensors::getPressurePercentage();
    doc["flow"] = Sensors::getFlowPercentage();
    doc["timestamp"] = millis();
    
    serializeJson(doc, Serial);
    Serial.println();
  }
}

void loop() {
  if (Serial.available() > 0) {
    String command = Serial.readStringUntil('\n');
    command.trim();
    handleCommand(command);
  }
  
  // Traiter l'appui sur le bouton avec anti-rebond
  Button::process();
  
  // Toujours inclure un petit délai pour éviter que les boucles serrées ne monopolisent le CPU
  // Cela aide à la consommation d'énergie et à la génération de chaleur
  delay(10);
}
