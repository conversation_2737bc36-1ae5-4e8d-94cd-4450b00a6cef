<?php
session_start();
require_once(__DIR__ . '/lib/pv.php');
require_once(__DIR__ . '/lib/affaires.php');

if (!isset($_SESSION['user'])) {
    header('Location: /auth/login.php');
    exit;
}

// Récupérer la liste des affaires pour le formulaire de création
$affaires = Affaire::getAll();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'create':
                if (PV::create(
                    $_POST['numero'],
                    $_POST['affaire_id'],
                    $_POST['contenu'],
                    $_SESSION['user']['id']
                )) {
                    $success = "Procès-verbal créé avec succès";
                } else {
                    $error = "Erreur lors de la création du procès-verbal";
                }
                break;
            case 'update':
                if (PV::update(
                    $_POST['id'],
                    $_POST['numero'],
                    $_POST['contenu'],
                    $_POST['statut']
                )) {
                    $success = "Procès-verbal mis à jour avec succès";
                } else {
                    $error = "Erreur lors de la mise à jour du procès-verbal";
                }
                break;
            case 'delete':
                if (PV::delete($_POST['id'])) {
                    $success = "Procès-verbal supprimé avec succès";
                } else {
                    $error = "Erreur lors de la suppression du procès-verbal";
                }
                break;
        }
    }
}

$pvs = PV::getAll();

ob_start();
?>

    <div class="mb-4 flex justify-between items-center">
        <h2 class="text-2xl font-bold dark:text-white">Gestion des Procès-verbaux</h2>
        <button data-modal-target="createPVModal" data-modal-toggle="createPVModal"
                class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">
            Nouveau PV
        </button>
    </div>

<?php if (isset($success)): ?>
    <div id="alert-success"
         class="flex items-center p-4 mb-4 text-green-800 rounded-lg bg-green-50 dark:bg-gray-800 dark:text-green-400"
         role="alert">
        <svg class="flex-shrink-0 w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
             viewBox="0 0 20 20">
            <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/>
        </svg>
        <div class="ms-3 text-sm font-medium"><?php echo htmlspecialchars($success); ?></div>
        <button type="button"
                class="ms-auto -mx-1.5 -my-1.5 bg-green-50 text-green-500 rounded-lg focus:ring-2 focus:ring-green-400 p-1.5 hover:bg-green-200 inline-flex items-center justify-center h-8 w-8 dark:bg-gray-800 dark:text-green-400 dark:hover:bg-gray-700"
                data-dismiss-target="#alert-success" aria-label="Close">
            <span class="sr-only">Fermer</span>
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
            </svg>
        </button>
    </div>
<?php endif; ?>

<?php if (isset($error)): ?>
    <div id="alert-error"
         class="flex items-center p-4 mb-4 text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400"
         role="alert">
        <svg class="flex-shrink-0 w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
             viewBox="0 0 20 20">
            <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/>
        </svg>
        <div class="ms-3 text-sm font-medium"><?php echo htmlspecialchars($error); ?></div>
        <button type="button"
                class="ms-auto -mx-1.5 -my-1.5 bg-red-50 text-red-500 rounded-lg focus:ring-2 focus:ring-red-400 p-1.5 hover:bg-red-200 inline-flex items-center justify-center h-8 w-8 dark:bg-gray-800 dark:text-red-400 dark:hover:bg-gray-700"
                data-dismiss-target="#alert-error" aria-label="Close">
            <span class="sr-only">Fermer</span>
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
            </svg>
        </button>
    </div>
<?php endif; ?>

    <div class="relative overflow-x-auto shadow-md sm:rounded-lg">
        <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
            <tr>
                <th scope="col" class="px-6 py-3">Numéro</th>
                <th scope="col" class="px-6 py-3">Affaire</th>
                <th scope="col" class="px-6 py-3">Date de création</th>
                <th scope="col" class="px-6 py-3">Créé par</th>
                <th scope="col" class="px-6 py-3">Statut</th>
                <th scope="col" class="px-6 py-3">Actions</th>
            </tr>
            </thead>
            <tbody>
            <?php foreach ($pvs as $pv): ?>
                <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                    <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                        <?php echo htmlspecialchars($pv['numero']); ?>
                    </th>
                    <td class="px-6 py-4"><?php echo htmlspecialchars($pv['numero_affaire']); ?></td>
                    <td class="px-6 py-4"><?php echo htmlspecialchars($pv['date_creation']); ?></td>
                    <td class="px-6 py-4"><?php echo htmlspecialchars($pv['created_by_name']); ?></td>
                    <td class="px-6 py-4">
                        <span class="text-xs font-medium mr-2 px-2.5 py-0.5 rounded-md border
                                    <?php
                        if ($pv['statut'] == 'Brouillon') {
                            echo 'bg-yellow-100 text-yellow-800 dark:bg-gray-700 dark:text-yellow-400 border-yellow-100 dark:border-yellow-500';
                        } elseif ($pv['statut'] == 'Finalisé') {
                            echo 'bg-blue-100 text-blue-800 dark:bg-gray-700 dark:text-blue-400 border-blue-100 dark:border-blue-500';
                        } elseif ($pv['statut'] == 'Envoyé') {
                            echo 'bg-green-100 text-green-800 dark:bg-gray-700 dark:text-green-400 border-green-100 dark:border-green-500';
                        } elseif ($pv['statut'] == 'Annulé') {
                            echo 'bg-red-100 text-red-800 dark:bg-gray-700 dark:text-red-400 border-red-100 dark:border-red-500';
                        }
                        ?>">
                            <?php echo htmlspecialchars($pv['statut']); ?>
                        </span>
                    </td>
                    <td class="px-6 py-4">
                        <a href="/pdf_handler.php?action=generate&pv_id=<?php echo $pv['id']; ?>"
                           class="font-medium text-purple-600 dark:text-purple-500 hover:underline" title="Générer PDF">PDF</a>
                        <button data-modal-target="editPVModal<?php echo $pv['id']; ?>"
                                data-modal-toggle="editPVModal<?php echo $pv['id']; ?>"
                                class="font-medium text-blue-600 dark:text-blue-500 hover:underline ms-3">Modifier
                        </button>
                        <button data-modal-target="deletePVModal<?php echo $pv['id']; ?>"
                                data-modal-toggle="deletePVModal<?php echo $pv['id']; ?>"
                                class="font-medium text-red-600 dark:text-red-500 hover:underline ms-3">Supprimer
                        </button>
                        <a href="/detail/pv.php?id=<?php echo $pv['id']; ?>"
                           class="font-medium text-green-600 dark:text-green-500 hover:underline ms-3">Voir</a>
                    </td>
                </tr>
            <?php endforeach; ?>
            </tbody>
        </table>
    </div>

    <!-- Modal de création -->
    <div id="createPVModal" tabindex="-1" aria-hidden="true"
         class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
        <div class="relative p-4 w-full max-w-md max-h-full">
            <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                        Nouveau Procès-verbal
                    </h3>
                    <button type="button"
                            class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                            data-modal-hide="createPVModal">
                        <svg class="w-3 h-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                        </svg>
                        <span class="sr-only">Fermer</span>
                    </button>
                </div>
                <form class="p-4 md:p-5" method="POST">
                    <input type="hidden" name="action" value="create">
                    <div class="grid gap-4 mb-4">
                        <div>
                            <label for="numero" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Numéro</label>
                            <input type="text" name="numero" id="numero"
                                   class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                   required>
                        </div>
                        <div>
                            <label for="affaire_id"
                                   class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Affaire</label>
                            <select name="affaire_id" id="affaire_id"
                                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                    required>
                                <option value="" disabled selected>Sélectionner une affaire</option>
                                <?php foreach ($affaires as $affaire): ?>
                                    <option value="<?php echo $affaire['id']; ?>"><?php echo htmlspecialchars($affaire['numero'] . ' - ' . $affaire['client']); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div>
                            <label for="contenu" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Contenu</label>
                            <textarea name="contenu" id="contenu" rows="4"
                                      class="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                      required></textarea>
                        </div>
                    </div>
                    <button type="submit"
                            class="text-white inline-flex items-center bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                        Créer le PV
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Modals de modification et suppression -->
<?php foreach ($pvs as $pv): ?>
    <!-- Modal de modification -->
    <div id="editPVModal<?php echo $pv['id']; ?>" tabindex="-1" aria-hidden="true"
         class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
        <div class="relative p-4 w-full max-w-md max-h-full">
            <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                        Modifier le PV
                    </h3>
                    <button type="button"
                            class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                            data-modal-hide="editPVModal<?php echo $pv['id']; ?>">
                        <svg class="w-3 h-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                        </svg>
                        <span class="sr-only">Fermer</span>
                    </button>
                </div>
                <form class="p-4 md:p-5" method="POST">
                    <input type="hidden" name="action" value="update">
                    <input type="hidden" name="id" value="<?php echo $pv['id']; ?>">
                    <div class="grid gap-4 mb-4">
                        <div>
                            <label for="affaire_id"
                                   class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Affaire</label>
                            <select name="affaire_id" id="affaire_id"
                                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                    required>
                                <?php foreach ($affaires as $affaire): ?>
                                    <option value="<?php echo htmlspecialchars($affaire['id']); ?>" <?php echo ($affaire['id'] == $pv['affaire_id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($affaire['numero'] . ' - ' . $affaire['client']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div>
                            <label for="numero<?php echo $pv['id']; ?>"
                                   class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Numéro</label>
                            <input type="text" name="numero" id="numero<?php echo $pv['id']; ?>"
                                   value="<?php echo htmlspecialchars($pv['numero']); ?>"
                                   class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                   required>
                        </div>
                        <div>
                            <label for="contenu<?php echo $pv['id']; ?>"
                                   class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Contenu</label>
                            <textarea name="contenu" id="contenu<?php echo $pv['id']; ?>" rows="4"
                                      class="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                      required><?php echo htmlspecialchars($pv['contenu']); ?></textarea>
                        </div>
                        <div>
                            <label for="statut<?php echo $pv['id']; ?>"
                                   class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Statut</label>
                            <select name="statut" id="statut<?php echo $pv['id']; ?>"
                                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                <option value="Brouillon" <?php echo $pv['statut'] === 'Brouillon' ? 'selected' : ''; ?>>
                                    Brouillon
                                </option>
                                <option value="Finalisé" <?php echo $pv['statut'] === 'Finalisé' ? 'selected' : ''; ?>>
                                    Finalisé
                                </option>
                                <option value="Envoyé" <?php echo $pv['statut'] === 'Envoyé' ? 'selected' : ''; ?>>
                                    Envoyé
                                </option>
                                <option value="Annulé" <?php echo $pv['statut'] === 'Annulé' ? 'selected' : ''; ?>>
                                    Annulé
                                </option>
                            </select>
                        </div>
                    </div>
                    <button type="submit"
                            class="text-white inline-flex items-center bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                        Enregistrer les modifications
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal de suppression -->
    <div id="deletePVModal<?php echo $pv['id']; ?>" tabindex="-1" aria-hidden="true"
         class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
        <div class="relative p-4 w-full max-w-md max-h-full">
            <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                        Confirmer la suppression
                    </h3>
                    <button type="button"
                            class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                            data-modal-hide="deletePVModal<?php echo $pv['id']; ?>">
                        <svg class="w-3 h-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                        </svg>
                        <span class="sr-only">Fermer</span>
                    </button>
                </div>
                <div class="p-4 md:p-5">
                    <p class="text-gray-500 dark:text-gray-300">Êtes-vous sûr de vouloir supprimer ce procès-verbal ?
                        Cette action est irréversible.</p>
                    <form method="POST" class="mt-5">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="id" value="<?php echo $pv['id']; ?>">
                        <button type="submit"
                                class="text-white bg-red-600 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 dark:focus:ring-red-800 font-medium rounded-lg text-sm inline-flex items-center px-5 py-2.5 text-center me-2">
                            Oui, supprimer
                        </button>
                        <button type="button" data-modal-hide="deletePVModal<?php echo $pv['id']; ?>"
                                class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900 focus:z-10 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-500 dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-gray-600">
                            Non, annuler
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php endforeach; ?>


<?php
$pageContent = ob_get_clean();
include 'layout.php';
?>