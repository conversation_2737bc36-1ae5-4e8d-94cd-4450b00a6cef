﻿# Multi-stage Dockerfile pour FluidMotion Web Application
# Stage 1: Base avec les dépendances système
FROM php:8.4-apache AS base

# Définir les variables d'environnement
ENV APACHE_DOCUMENT_ROOT=/var/www/html
ENV APACHE_RUN_USER=www-data
ENV APACHE_RUN_GROUP=www-data

# Installer les dépendances système
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        libpng-dev \
        libjpeg-dev \
        libfreetype6-dev \
        libzip-dev \
        libonig-dev \
        libicu-dev \
        libcurl4-openssl-dev \
        libxml2-dev \
        mariadb-client \
        default-mysql-client \
        unzip \
        git \
        procps \
        cron \
        supervisor \
    && rm -rf /var/lib/apt/lists/*

# Configurer et installer les extensions PHP
RUN docker-php-ext-configure gd --with-freetype --with-jpeg && \
    docker-php-ext-install \
        mysqli \
        pdo \
        pdo_mysql \
        mbstring \
        curl \
        xml \
        opcache \
        gd \
        zip \
        intl

# Installer Composer
COPY --from=composer:2 /usr/bin/composer /usr/bin/composer

# Configurer Apache
RUN a2enmod rewrite headers ssl && \
    sed -i 's/AllowOverride None/AllowOverride All/g' /etc/apache2/apache2.conf

# Stage 2: Development avec Xdebug
FROM base AS development

# Installer Xdebug pour le développement
RUN pecl install xdebug && \
    docker-php-ext-enable xdebug

# Configuration Xdebug
COPY <<EOF /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini
xdebug.mode=develop,debug
xdebug.client_host=host.docker.internal
xdebug.start_with_request=yes
xdebug.client_port=9003
xdebug.log=/tmp/xdebug.log
EOF

# Configuration PHP pour le développement
COPY <<EOF /usr/local/etc/php/conf.d/development.ini
display_errors=On
error_reporting=E_ALL
log_errors=On
error_log=/var/log/php_errors.log
memory_limit=512M
upload_max_filesize=100M
post_max_size=100M
max_execution_time=300
EOF

# Stage 3: Production optimisée
FROM base AS production

# Configuration PHP pour la production
COPY <<EOF /usr/local/etc/php/conf.d/production.ini
display_errors=Off
error_reporting=E_ERROR | E_WARNING | E_PARSE
log_errors=On
error_log=/var/log/php_errors.log
memory_limit=256M
upload_max_filesize=50M
post_max_size=50M
max_execution_time=120
opcache.enable=1
opcache.memory_consumption=128
opcache.interned_strings_buffer=8
opcache.max_accelerated_files=4000
opcache.revalidate_freq=2
opcache.fast_shutdown=1
session.cookie_httponly=1
session.cookie_secure=1
session.use_strict_mode=1
EOF

# Créer un utilisateur non-root pour la sécurité
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Créer les répertoires nécessaires avec les bonnes permissions
RUN mkdir -p /var/www/html/backups \
             /var/www/html/pdf_exports \
             /var/www/html/temp/mpdf \
             /var/www/html/vendor \
             /var/log/app \
    && chown -R www-data:www-data /var/www/html \
    && chown -R www-data:www-data /var/log/app \
    && chmod -R 755 /var/www/html/backups \
    && chmod -R 755 /var/www/html/pdf_exports \
    && chmod -R 755 /var/www/html/temp

# Copier les fichiers de l'application
COPY --chown=www-data:www-data . /var/www/html/

# Installer les dépendances Composer
RUN if [ -f /var/www/html/composer.json ]; then \
        cd /var/www/html && \
        composer install --no-dev --optimize-autoloader --no-interaction; \
    fi

# Configuration de sécurité Apache
COPY <<EOF /etc/apache2/conf-available/security.conf
ServerTokens Prod
ServerSignature Off
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Strict-Transport-Security "max-age=63072000; includeSubDomains; preload"
Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' cdn.tailwindcss.com cdnjs.cloudflare.com unpkg.com cdn.jsdelivr.net static.cloudflareinsights.com; style-src 'self' 'unsafe-inline' cdnjs.cloudflare.com unpkg.com cdn.jsdelivr.net; img-src 'self' data:; font-src 'self'"
EOF

# Configuration du répertoire racine Apache
COPY <<EOF /etc/apache2/conf-available/document-root.conf
<Directory /var/www/html>
    Options -Indexes +FollowSymLinks
    AllowOverride All
    Require all granted
    DirectoryIndex index.php index.html
</Directory>

<Directory /var/www/html/config>
    Require all denied
</Directory>

<Directory /var/www/html/lib>
    Require all denied
</Directory>
EOF

RUN a2enconf security && a2enconf document-root

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/health.php || exit 1

# Créer le script d'initialisation
COPY <<EOF /usr/local/bin/docker-entrypoint.sh
#!/bin/bash
set -e

# Fonction de logging
log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1"
}

# Attendre que la base de données soit disponible
wait_for_db() {
    if [ -n "\$DB_HOST" ] && [ -n "\$DB_USER" ] && [ -n "\$DB_PASS" ]; then
        log "Attente de la disponibilité de la base de données..."
        until mysql -h"\$DB_HOST" -u"\$DB_USER" -p"\$DB_PASS" -e "SELECT 1" >/dev/null 2>&1; do
            log "Base de données non disponible, nouvelle tentative dans 5 secondes..."
            sleep 5
        done
        log "Base de données disponible!"
    fi
}

# Configurer les permissions
setup_permissions() {
    log "Configuration des permissions..."
    chown -R www-data:www-data /var/www/html
    chmod -R 755 /var/www/html/backups /var/www/html/pdf_exports /var/www/html/temp

    # Créer les répertoires s'ils n'existent pas
    mkdir -p /var/www/html/backups /var/www/html/pdf_exports /var/www/html/temp/mpdf
    chown -R www-data:www-data /var/www/html/backups /var/www/html/pdf_exports /var/www/html/temp
}

# Vérifier les outils de base de données
check_db_tools() {
    log "Vérification des outils de base de données..."
    mysqldump --version
    mysql --version
}

# Fonction principale
main() {
    log "Démarrage de l'application FluidMotion..."

    setup_permissions
    check_db_tools
    wait_for_db

    log "Démarrage d'Apache..."
    exec apache2-foreground
}

# Exécuter la fonction principale
main "\$@"
EOF

RUN chmod +x /usr/local/bin/docker-entrypoint.sh

# Exposer le port
EXPOSE 80

# Point d'entrée
ENTRYPOINT ["/usr/local/bin/docker-entrypoint.sh"]
