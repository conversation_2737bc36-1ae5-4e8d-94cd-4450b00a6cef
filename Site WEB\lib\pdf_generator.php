<?php
require_once(__DIR__ . '/../config/database.php');
require_once(__DIR__ . '/pv.php');
require_once(__DIR__ . '/affaires.php');
require_once(__DIR__ . '/essais.php');
require_once(__DIR__ . '/courbes.php');

// Essayer de charger mPDF via Composer ou directement
if (file_exists(__DIR__ . '/../vendor/autoload.php')) {
    require_once(__DIR__ . '/../vendor/autoload.php');
} else {
    // Fallback si mPDF n'est pas installé via Composer
    // On utilisera une méthode alternative
}

class PDFGenerator
{
    /**
     * Formater les paramètres théoriques JSON pour affichage PDF
     * @param string $json_string JSON des paramètres théoriques
     * @return string HTML formaté pour PDF
     */
    public static function formatParametresTheoriquesForPDF($json_string)
    {
        if (empty($json_string)) {
            return '<p style="color: #666; font-style: italic;">Non disponible</p>';
        }

        $parametres = json_decode($json_string, true);
        if (!$parametres || !is_array($parametres)) {
            return '<p style="color: #666; font-style: italic;">Format invalide</p>';
        }

        // Mapping des clés vers des libellés plus lisibles
        $labels = [
            'pression_nominale' => 'Pression nominale',
            'debit_nominal' => 'Débit nominal',
            'temperature_fluide' => 'Température du fluide',
            'viscosite' => 'Viscosité',
            'puissance' => 'Puissance',
            'course' => 'Course',
            'diametre_piston' => 'Diamètre du piston',
            'diametre_tige' => 'Diamètre de la tige',
            'pression_max' => 'Pression maximale',
            'debit_max' => 'Débit maximal',
            'force_max' => 'Force maximale'
        ];

        $html = '<table style="width: 100%; border-collapse: collapse; margin: 10px 0;">';

        foreach ($parametres as $key => $value) {
            $label = $labels[$key] ?? ucfirst(str_replace('_', ' ', $key));
            $html .= '<tr style="border-bottom: 1px solid #ddd;">';
            $html .= '<td style="padding: 8px; font-weight: bold; width: 60%;">' . htmlspecialchars($label) . '</td>';
            $html .= '<td style="padding: 8px;">' . htmlspecialchars($value) . '</td>';
            $html .= '</tr>';
        }

        $html .= '</table>';
        return $html;
    }

    /**
     * Générer un PDF pour un essai individuel
     * @param int $essaiId ID de l'essai
     * @return array Résultat de l'opération
     */
    public static function generateEssaiPDF($essaiId)
    {
        try {
            // Récupérer les données de l'essai
            $essai = Essai::getById($essaiId);
            if (!$essai) {
                return [
                    'success' => false,
                    'message' => 'Essai introuvable'
                ];
            }

            // Récupérer les données de l'affaire
            $affaire = Affaire::getByNumero($essai['numero_affaire']);
            if (!$affaire) {
                return [
                    'success' => false,
                    'message' => 'Affaire introuvable'
                ];
            }

            // Récupérer le rendement si disponible
            require_once(__DIR__ . '/rendement.php');
            $rendement = Rendement::getByEssaiId($essai['id']);

            // Récupérer les courbes si disponibles
            $courbes = Courbe::getByEssaiId($essai['id']);

            // Générer le contenu HTML du PDF
            $htmlContent = self::generateEssaiHTML($essai, $affaire, $rendement, $courbes);

            // Créer le dossier PDF s'il n'existe pas
            $pdfDir = __DIR__ . '/../pdf_exports/';
            if (!is_dir($pdfDir)) {
                mkdir($pdfDir, 0755, true);
            }

            // Générer le nom de fichier
            $filename = 'Essai_' . $essai['id'] . '_' . date('Y-m-d_H-i-s') . '.pdf';
            $filepath = $pdfDir . $filename;

            // Utiliser mPDF si disponible, sinon utiliser une solution de fallback
            if (class_exists('\Mpdf\Mpdf')) {
                $result = self::generatePDFWithMpdf($htmlContent, $filepath);
            } else {
                $result = self::generatePDFWithFallback($htmlContent, $filepath);
            }

            if ($result['success']) {
                $result['filename'] = $filename;
                $result['filepath'] = $filepath;
            }

            return $result;

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Erreur lors de la génération du PDF: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Générer un PDF pour un PV donné
     * @param int $pvId ID du PV
     * @return array Résultat de l'opération
     */
    public static function generatePVPDF($pvId)
    {
        try {
            // Récupérer les données du PV
            $pv = PV::getById($pvId);
            if (!$pv) {
                return [
                    'success' => false,
                    'message' => 'PV introuvable'
                ];
            }

            // Récupérer les données de l'affaire
            $affaire = Affaire::getById($pv['affaire_id']);
            if (!$affaire) {
                return [
                    'success' => false,
                    'message' => 'Affaire introuvable'
                ];
            }

            // Récupérer les essais de l'affaire
            $essais = Essai::getByAffaireId($pv['affaire_id']);

            // Générer le contenu HTML du PDF
            $htmlContent = self::generatePVHTML($pv, $affaire, $essais);

            // Créer le dossier PDF s'il n'existe pas
            $pdfDir = __DIR__ . '/../pdf_exports/';
            if (!is_dir($pdfDir)) {
                mkdir($pdfDir, 0755, true);
            }

            // Générer le nom de fichier
            $filename = 'PV_' . $pv['numero'] . '_' . date('Y-m-d_H-i-s') . '.pdf';
            $filepath = $pdfDir . $filename;

            // Utiliser mPDF si disponible, sinon utiliser une solution de fallback
            if (class_exists('\Mpdf\Mpdf')) {
                $result = self::generatePDFWithMpdf($htmlContent, $filepath);
            } else {
                $result = self::generatePDFWithFallback($htmlContent, $filepath);
            }

            if ($result['success']) {
                return [
                    'success' => true,
                    'message' => 'PDF généré avec succès',
                    'filename' => $filename,
                    'filepath' => $filepath,
                    'size' => filesize($filepath)
                ];
            } else {
                return $result;
            }

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Erreur lors de la génération du PDF: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Générer le contenu HTML pour le PV
     */
    private static function generatePVHTML($pv, $affaire, $essais)
    {
        $html = '<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Procès-Verbal ' . htmlspecialchars($pv['numero']) . '</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            line-height: 1.6;
            color: #333;
            font-size: 10pt;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .company-name {
            font-size: 18pt;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 10px;
        }
        .pv-title {
            font-size: 14pt;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .pv-number {
            font-size: 12pt;
            color: #666;
        }
        .section {
            margin-bottom: 25px;
            page-break-inside: avoid;
        }
        .section-title {
            font-size: 12pt;
            font-weight: bold;
            color: #1e40af;
            border-bottom: 1px solid #ccc;
            padding-bottom: 5px;
            margin-bottom: 15px;
        }
        .info-grid {
            width: 100%;
            margin-bottom: 20px;
        }
        .info-grid table {
            width: 100%;
            border-collapse: collapse;
        }
        .info-grid td {
            width: 50%;
            vertical-align: top;
            padding: 5px;
        }
        .info-item {
            margin-bottom: 8px;
        }
        .info-label {
            font-weight: bold;
            color: #374151;
            display: inline-block;
            width: 120px;
        }
        .info-value {
            display: inline;
        }
        .essais-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
            font-size: 9pt;
        }
        .essais-table th,
        .essais-table td {
            border: 1px solid #ccc;
            padding: 6px;
            text-align: left;
        }
        .essais-table th {
            background-color: #f3f4f6;
            font-weight: bold;
        }
        .status-badge {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 8pt;
            font-weight: bold;
        }
        .status-termine {
            background-color: #d1fae5;
            color: #065f46;
        }
        .status-en-cours {
            background-color: #fef3c7;
            color: #92400e;
        }
        .status-en-attente {
            background-color: #e5e7eb;
            color: #374151;
        }
        .content-section {
            background-color: #f9fafb;
            padding: 15px;
            border: 1px solid #e5e7eb;
            margin-top: 15px;
        }
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 8pt;
            color: #6b7280;
            border-top: 1px solid #e5e7eb;
            padding-top: 20px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-name">FluidMotion Labs</div>
        <div class="pv-title">Procès-Verbal d\'Essai Hydraulique</div>
        <div class="pv-number">N° ' . htmlspecialchars($pv['numero']) . '</div>
    </div>

    <div class="section">
        <div class="section-title">Informations Générales</div>
        <div class="info-grid">
            <table>
                <tr>
                    <td>
                        <div class="info-item">
                            <span class="info-label">Affaire:</span>
                            <span class="info-value">' . htmlspecialchars($affaire['numero']) . '</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Client:</span>
                            <span class="info-value">' . htmlspecialchars($affaire['client']) . '</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Date de création PV:</span>
                            <span class="info-value">' . htmlspecialchars($pv['date_creation']) . '</span>
                        </div>
                    </td>
                    <td>
                        <div class="info-item">
                            <span class="info-label">Statut PV:</span>
                            <span class="info-value">' . htmlspecialchars($pv['statut']) . '</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Date affaire:</span>
                            <span class="info-value">' . htmlspecialchars($affaire['date_creation']) . '</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Statut affaire:</span>
                            <span class="info-value">' . htmlspecialchars($affaire['statut']) . '</span>
                        </div>
                    </td>
                </tr>
            </table>
        </div>
    </div>

    <div class="section">
        <div class="section-title">Description de l\'Affaire</div>
        <div class="content-section">
            ' . nl2br(htmlspecialchars($affaire['description'] ?? 'Aucune description disponible')) . '
        </div>
    </div>';

        // Ajouter les essais si disponibles
        if (!empty($essais)) {
            $html .= '
    <div class="section">
        <div class="section-title">Essais Réalisés</div>
        <table class="essais-table">
            <thead>
                <tr>
                    <th>Type d\'Essai</th>
                    <th>Date</th>
                    <th>Statut</th>
                    <th>Paramètres Théoriques</th>
                </tr>
            </thead>
            <tbody>';

            foreach ($essais as $essai) {
                $statusClass = '';
                switch ($essai['statut']) {
                    case 'Terminé':
                        $statusClass = 'status-termine';
                        break;
                    case 'En cours':
                        $statusClass = 'status-en-cours';
                        break;
                    default:
                        $statusClass = 'status-en-attente';
                        break;
                }

                $html .= '
                <tr>
                    <td>' . htmlspecialchars($essai['type']) . '</td>
                    <td>' . htmlspecialchars($essai['date_essai']) . '</td>
                    <td><span class="status-badge ' . $statusClass . '">' . htmlspecialchars($essai['statut']) . '</span></td>
                    <td>' . self::formatParametresTheoriquesForPDF($essai['parametre_theorique']) . '</td>
                </tr>';
            }

            $html .= '
            </tbody>
        </table>
    </div>';
        }

        // Ajouter le contenu du PV
        if (!empty($pv['contenu'])) {
            $html .= '
    <div class="section">
        <div class="section-title">Contenu du Procès-Verbal</div>
        <div class="content-section">
            ' . nl2br(htmlspecialchars($pv['contenu'])) . '
        </div>
    </div>';
        }

        $html .= '
    <div class="footer">
        <p>Document généré automatiquement le ' . date('d/m/Y à H:i:s') . '</p>
        <p>FluidMotion Labs - Système de Gestion des Essais Hydrauliques</p>
    </div>
</body>
</html>';

        return $html;
    }

    /**
     * Générer le contenu HTML pour un essai individuel
     * @param array $essai Données de l'essai
     * @param array $affaire Données de l'affaire
     * @param array|null $rendement Données de rendement
     * @param array $courbes Données des courbes
     * @return string HTML du PDF
     */
    private static function generateEssaiHTML($essai, $affaire, $rendement, $courbes)
    {
        $html = '<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Rapport d\'Essai - ' . htmlspecialchars($essai['type']) . '</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
            line-height: 1.6;
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #2563eb;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 10px;
        }
        .report-title {
            font-size: 20px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 5px;
        }
        .test-number {
            font-size: 16px;
            color: #6b7280;
        }
        .section {
            margin-bottom: 25px;
            page-break-inside: avoid;
        }
        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #1f2937;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 5px;
            margin-bottom: 15px;
        }
        .info-grid table {
            width: 100%;
            border-collapse: collapse;
        }
        .info-grid td {
            width: 50%;
            vertical-align: top;
            padding: 10px;
        }
        .info-item {
            margin-bottom: 10px;
        }
        .info-label {
            font-weight: bold;
            color: #374151;
            display: inline-block;
            width: 150px;
        }
        .info-value {
            color: #1f2937;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-termine {
            background-color: #dcfce7;
            color: #166534;
        }
        .status-en-cours {
            background-color: #dbeafe;
            color: #1d4ed8;
        }
        .status-en-attente {
            background-color: #fef3c7;
            color: #92400e;
        }
        .status-annule {
            background-color: #fee2e2;
            color: #dc2626;
        }
        .content-section {
            background-color: #f9fafb;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #2563eb;
        }
        .rendement-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }
        .rendement-card {
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
            color: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .rendement-card.volumetrique {
            background: linear-gradient(135deg, #059669, #047857);
        }
        .rendement-card.mecanique {
            background: linear-gradient(135deg, #7c3aed, #6d28d9);
        }
        .rendement-label {
            font-size: 12px;
            opacity: 0.9;
            margin-bottom: 5px;
        }
        .rendement-value {
            font-size: 24px;
            font-weight: bold;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .data-table th,
        .data-table td {
            border: 1px solid #d1d5db;
            padding: 8px 12px;
            text-align: left;
        }
        .data-table th {
            background-color: #f3f4f6;
            font-weight: bold;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            text-align: center;
            font-size: 12px;
            color: #6b7280;
        }
        .courbes-summary {
            background-color: #f0f9ff;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #0ea5e9;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-name">FluidMotion Labs</div>
        <div class="report-title">Rapport d\'Essai Hydraulique</div>
        <div class="test-number">Essai #' . htmlspecialchars($essai['id']) . ' - ' . htmlspecialchars($essai['type']) . '</div>
    </div>

    <div class="section">
        <div class="section-title">Informations Générales</div>
        <div class="info-grid">
            <table>
                <tr>
                    <td>
                        <div class="info-item">
                            <span class="info-label">Numéro d\'affaire:</span>
                            <span class="info-value">' . htmlspecialchars($essai['numero_affaire']) . '</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Client:</span>
                            <span class="info-value">' . htmlspecialchars($affaire['client'] ?? 'N/A') . '</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Type d\'essai:</span>
                            <span class="info-value">' . htmlspecialchars($essai['type']) . '</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Date d\'essai:</span>
                            <span class="info-value">' . htmlspecialchars($essai['date_essai']) . '</span>
                        </div>
                    </td>
                    <td>
                        <div class="info-item">
                            <span class="info-label">Statut:</span>
                            <span class="info-value">';

        // Ajouter le badge de statut
        $statusClass = '';
        switch ($essai['statut']) {
            case 'Terminé':
                $statusClass = 'status-termine';
                break;
            case 'En cours':
                $statusClass = 'status-en-cours';
                break;
            case 'Annulé':
                $statusClass = 'status-annule';
                break;
            default:
                $statusClass = 'status-en-attente';
                break;
        }

        $html .= '<span class="status-badge ' . $statusClass . '">' . htmlspecialchars($essai['statut']) . '</span>
                            </span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Date de création:</span>
                            <span class="info-value">' . htmlspecialchars($essai['date_creation'] ?? 'N/A') . '</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Créé par:</span>
                            <span class="info-value">' . htmlspecialchars($essai['cree_par_nom'] ?? 'N/A') . '</span>
                        </div>
                    </td>
                </tr>
            </table>
        </div>
    </div>

    <div class="section">
        <div class="section-title">Paramètres Théoriques</div>
        ' . self::formatParametresTheoriquesForPDF($essai['parametre_theorique']) . '
    </div>';

        // Ajouter les résultats si disponibles
        if (!empty($essai['resultat'])) {
            $html .= '
    <div class="section">
        <div class="section-title">Résultats</div>
        <div class="content-section">
            ' . nl2br(htmlspecialchars($essai['resultat'])) . '
        </div>
    </div>';
        }

        // Ajouter les calculs de rendement si disponibles
        if ($rendement) {
            $html .= '
    <div class="section">
        <div class="section-title">Calculs de Rendement (F15)</div>
        <div class="rendement-grid">
            <div class="rendement-card">
                <div class="rendement-label">Rendement Global</div>
                <div class="rendement-value">' . number_format($rendement['rendement_global'], 2) . '%</div>
            </div>
            <div class="rendement-card volumetrique">
                <div class="rendement-label">Rendement Volumétrique</div>
                <div class="rendement-value">' . number_format($rendement['rendement_volumetrique'], 2) . '%</div>
            </div>
            <div class="rendement-card mecanique">
                <div class="rendement-label">Rendement Mécanique</div>
                <div class="rendement-value">' . number_format($rendement['rendement_mecanique'], 2) . '%</div>
            </div>
        </div>

        <table class="data-table">
            <thead>
                <tr>
                    <th>Paramètre</th>
                    <th>Valeur</th>
                    <th>Unité</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Pression moyenne CPA</td>
                    <td>' . number_format($rendement['pression_moyenne_cpa'], 2) . '</td>
                    <td>Pa</td>
                </tr>
                <tr>
                    <td>Pression moyenne CPB</td>
                    <td>' . number_format($rendement['pression_moyenne_cpb'], 2) . '</td>
                    <td>Pa</td>
                </tr>
                <tr>
                    <td>Débit moyen</td>
                    <td>' . number_format($rendement['debit_moyen'], 2) . '</td>
                    <td>L/min</td>
                </tr>
                <tr>
                    <td>Puissance hydraulique</td>
                    <td>' . number_format($rendement['puissance_hydraulique'], 2) . '</td>
                    <td>W</td>
                </tr>
                <tr>
                    <td>Puissance mécanique</td>
                    <td>' . number_format($rendement['puissance_mecanique'], 2) . '</td>
                    <td>W</td>
                </tr>
            </tbody>
        </table>

        <p style="font-size: 12px; color: #6b7280; margin-top: 15px;">
            Calculé le ' . date('d/m/Y à H:i', strtotime($rendement['date_calcul'])) . '
            par ' . htmlspecialchars($rendement['calcule_par_nom'] ?? 'Inconnu') . '
        </p>
    </div>';
        }

        // Ajouter le mode opératoire si disponible
        if (!empty($essai['mode_operatoire'])) {
            $html .= '
    <div class="section">
        <div class="section-title">Mode Opératoire</div>
        <div class="content-section">
            ' . nl2br(htmlspecialchars($essai['mode_operatoire'])) . '
        </div>
    </div>';
        }

        // Ajouter un résumé des courbes si disponibles
        if (!empty($courbes)) {
            $courbeTypes = [];
            foreach ($courbes as $courbe) {
                $type = $courbe['type_courbe'];
                if (!isset($courbeTypes[$type])) {
                    $courbeTypes[$type] = 0;
                }
                $courbeTypes[$type]++;
            }

            $html .= '
    <div class="section">
        <div class="section-title">Données de Mesure</div>
        <div class="courbes-summary">
            <p><strong>Courbes disponibles:</strong></p>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Type de Courbe</th>
                        <th>Nombre de Points</th>
                    </tr>
                </thead>
                <tbody>';

            foreach ($courbeTypes as $type => $count) {
                $html .= '
                    <tr>
                        <td>' . htmlspecialchars($type) . '</td>
                        <td>' . number_format($count) . '</td>
                    </tr>';
            }

            $html .= '
                </tbody>
            </table>
            <p style="font-size: 12px; color: #6b7280; margin-top: 10px;">
                Les graphiques détaillés sont disponibles dans l\'interface web du système.
            </p>
        </div>
    </div>';
        }

        $html .= '
    <div class="footer">
        <p>Document généré automatiquement le ' . date('d/m/Y à H:i:s') . '</p>
        <p>FluidMotion Labs - Système de Gestion des Essais Hydrauliques</p>
    </div>
</body>
</html>';

        return $html;
    }

    /**
     * Générer un PDF avec mPDF
     */
    private static function generatePDFWithMpdf($htmlContent, $filepath)
    {
        try {
            // Créer un dossier temporaire pour mPDF dans notre application
            $tempDir = __DIR__ . '/../temp/mpdf';
            if (!is_dir($tempDir)) {
                if (!mkdir($tempDir, 0755, true)) {
                    throw new Exception('Impossible de créer le dossier temporaire: ' . $tempDir);
                }
            }

            // Vérifier que le dossier est accessible en écriture
            if (!is_writable($tempDir)) {
                throw new Exception('Le dossier temporaire n\'est pas accessible en écriture: ' . $tempDir);
            }

            // Configuration mPDF
            $config = [
                'mode' => 'utf-8',
                'format' => 'A4',
                'default_font_size' => 10,
                'default_font' => 'Arial',
                'margin_left' => 15,
                'margin_right' => 15,
                'margin_top' => 20,
                'margin_bottom' => 20,
                'margin_header' => 10,
                'margin_footer' => 10,
                'orientation' => 'P',
                'tempDir' => $tempDir
            ];

            // Créer une instance mPDF
            $mpdf = new \Mpdf\Mpdf($config);

            // Configuration du PDF
            $mpdf->SetTitle('Procès-Verbal FluidMotion Labs');
            $mpdf->SetAuthor('FluidMotion Labs');
            $mpdf->SetCreator('Système de Gestion des Essais Hydrauliques');
            $mpdf->SetSubject('Procès-Verbal d\'Essai Hydraulique');

            // Ajouter le contenu HTML
            $mpdf->WriteHTML($htmlContent);

            // Sauvegarder le PDF
            $mpdf->Output($filepath, \Mpdf\Output\Destination::FILE);

            return ['success' => true];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Erreur mPDF: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Solution de fallback pour la génération PDF (HTML simple)
     */
    private static function generatePDFWithFallback($htmlContent, $filepath)
    {
        try {
            // Pour le fallback, on sauvegarde en HTML avec extension PDF
            // Dans un environnement de production, on utiliserait une vraie bibliothèque PDF
            file_put_contents($filepath . '.html', $htmlContent);

            // Créer un fichier PDF simple avec le contenu texte
            $textContent = strip_tags(str_replace(['<br>', '<br/>', '<br />'], "\n", $htmlContent));
            file_put_contents($filepath, $textContent);

            return ['success' => true];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Erreur lors de la génération PDF fallback: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Lister les PDFs générés
     */
    public static function listGeneratedPDFs()
    {
        try {
            $pdfDir = __DIR__ . '/../pdf_exports/';
            if (!is_dir($pdfDir)) {
                return [];
            }

            $pdfs = [];

            // Récupérer les PDFs de PV
            $pvFiles = glob($pdfDir . 'PV_*.pdf');
            foreach ($pvFiles as $file) {
                $pdfs[] = [
                    'filename' => basename($file),
                    'path' => $file,
                    'size' => filesize($file),
                    'created' => filemtime($file),
                    'created_formatted' => date('Y-m-d H:i:s', filemtime($file)),
                    'type' => 'PV'
                ];
            }

            // Récupérer les PDFs d'essais individuels
            $essaiFiles = glob($pdfDir . 'Essai_*.pdf');
            foreach ($essaiFiles as $file) {
                $pdfs[] = [
                    'filename' => basename($file),
                    'path' => $file,
                    'size' => filesize($file),
                    'created' => filemtime($file),
                    'created_formatted' => date('Y-m-d H:i:s', filemtime($file)),
                    'type' => 'Essai'
                ];
            }

            // Trier par date de création (plus récent en premier)
            usort($pdfs, function ($a, $b) {
                return $b['created'] - $a['created'];
            });

            return $pdfs;

        } catch (Exception $e) {
            return [];
        }
    }
}
