<?php
/**
 * Script de test pour vérifier le fonctionnement de mPDF
 */

session_start();

// Vérifier que l'utilisateur est connecté
if (!isset($_SESSION['user'])) {
    die('Veuillez vous connecter pour accéder à cette page.');
}

echo "<h1>Test de mPDF</h1>";

// Essayer de charger mPDF
if (file_exists(__DIR__ . '/../vendor/autoload.php')) {
    require_once(__DIR__ . '/../vendor/autoload.php');
    echo "✅ Autoloader Composer chargé.<br>";
} else {
    echo "❌ Autoloader Composer non trouvé.<br>";
}

// Vérifier si mPDF est disponible
if (class_exists('\Mpdf\Mpdf')) {
    echo "✅ Classe mPDF disponible.<br>";

    try {
        // Créer un dossier temporaire pour mPDF
        $tempDir = __DIR__ . '/../temp/mpdf';
        if (!is_dir($tempDir)) {
            if (!mkdir($tempDir, 0755, true)) {
                throw new Exception('Impossible de créer le dossier temporaire: ' . $tempDir);
            }
        }

        // Vérifier les permissions
        if (!is_writable($tempDir)) {
            throw new Exception('Le dossier temporaire n\'est pas accessible en écriture: ' . $tempDir);
        }

        echo "✅ Dossier temporaire configuré: " . $tempDir . "<br>";

        // Créer une instance de test
        $mpdf = new \Mpdf\Mpdf([
            'mode' => 'utf-8',
            'format' => 'A4',
            'default_font_size' => 10,
            'default_font' => 'Arial',
            'tempDir' => $tempDir
        ]);

        echo "✅ Instance mPDF créée avec succès.<br>";

        // Contenu HTML de test
        $html = '
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Test mPDF</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { text-align: center; color: #2563eb; font-size: 18pt; font-weight: bold; }
                .content { margin-top: 20px; line-height: 1.6; }
                .table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                .table th, .table td { border: 1px solid #ccc; padding: 8px; text-align: left; }
                .table th { background-color: #f3f4f6; font-weight: bold; }
            </style>
        </head>
        <body>
            <div class="header">FluidMotion Labs - Test mPDF</div>

            <div class="content">
                <h2>Test de génération PDF</h2>
                <p>Ce document de test vérifie que mPDF fonctionne correctement dans l\'environnement FluidMotion Labs.</p>

                <h3>Informations système</h3>
                <ul>
                    <li>Date de génération: ' . date('d/m/Y H:i:s') . '</li>
                    <li>Version PHP: ' . PHP_VERSION . '</li>
                    <li>Utilisateur connecté: ' . htmlspecialchars($_SESSION['user']['username'] ?? 'Inconnu') . '</li>
                </ul>

                <h3>Tableau de test</h3>
                <table class="table">
                    <thead>
                        <tr>
                            <th>Fonctionnalité</th>
                            <th>Statut</th>
                            <th>Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Génération PDF</td>
                            <td>✅ OK</td>
                            <td>mPDF génère correctement les PDFs</td>
                        </tr>
                        <tr>
                            <td>Formatage HTML</td>
                            <td>✅ OK</td>
                            <td>Le HTML est correctement converti</td>
                        </tr>
                        <tr>
                            <td>Styles CSS</td>
                            <td>✅ OK</td>
                            <td>Les styles CSS sont appliqués</td>
                        </tr>
                        <tr>
                            <td>Caractères UTF-8</td>
                            <td>✅ OK</td>
                            <td>Les caractères accentués fonctionnent: àéèùç</td>
                        </tr>
                    </tbody>
                </table>

                <h3>Conclusion</h3>
                <p>Si vous pouvez lire ce document PDF, alors mPDF fonctionne correctement et est prêt pour la génération des procès-verbaux.</p>
            </div>
        </body>
        </html>';

        // Configurer le PDF
        $mpdf->SetTitle('Test mPDF - FluidMotion Labs');
        $mpdf->SetAuthor('FluidMotion Labs');
        $mpdf->SetCreator('Système de test mPDF');

        // Ajouter le contenu
        $mpdf->WriteHTML($html);

        // Générer le nom de fichier
        $filename = 'test_mpdf_' . date('Y-m-d_H-i-s') . '.pdf';
        $filepath = __DIR__ . '/../pdf_exports/' . $filename;

        // Créer le dossier s'il n'existe pas
        if (!is_dir(__DIR__ . '/../pdf_exports/')) {
            mkdir(__DIR__ . '/../pdf_exports/', 0755, true);
        }

        // Sauvegarder le PDF
        $mpdf->Output($filepath, \Mpdf\Output\Destination::FILE);

        if (file_exists($filepath)) {
            echo "✅ PDF de test généré avec succès: <a href='/pdf_handler.php?action=download&filename=" . urlencode($filename) . "'>Télécharger le PDF de test</a><br>";
            echo "Taille du fichier: " . round(filesize($filepath) / 1024, 2) . " KB<br>";
        } else {
            echo "❌ Erreur: Le fichier PDF n'a pas été créé.<br>";
        }

    } catch (Exception $e) {
        echo "❌ Erreur lors de la création du PDF: " . $e->getMessage() . "<br>";
    }

} else {
    echo "❌ Classe mPDF non disponible.<br>";
    echo "<p>Pour installer mPDF, vous pouvez:</p>";
    echo "<ul>";
    echo "<li>Utiliser Composer: <code>composer require mpdf/mpdf</code></li>";
    echo "<li>Ou utiliser le script d'installation: <a href='/install_mpdf.php'>Installer mPDF</a></li>";
    echo "</ul>";
}

echo "<h2>Informations sur l'environnement</h2>";
echo "<ul>";
echo "<li>Version PHP: " . PHP_VERSION . "</li>";
echo "<li>Extensions chargées: " . implode(', ', get_loaded_extensions()) . "</li>";
echo "<li>Dossier vendor existe: " . (is_dir(__DIR__ . '/../vendor') ? 'Oui' : 'Non') . "</li>";
echo "<li>Autoloader existe: " . (file_exists(__DIR__ . '/../vendor/autoload.php') ? 'Oui' : 'Non') . "</li>";
echo "</ul>";

echo "<p><a href='/tests.php'>← Retour aux Tests</a> | <a href='/pv.php'>Gestion des PV</a></p>";
?>

<style>
    body {
        font-family: Arial, sans-serif;
        margin: 20px;
    }

    h1 {
        color: #2563eb;
    }

    h2 {
        color: #1e40af;
        border-bottom: 1px solid #e5e7eb;
        padding-bottom: 5px;
    }

    ul {
        margin-left: 20px;
    }

    code {
        background-color: #f3f4f6;
        padding: 2px 4px;
        border-radius: 3px;
    }

    a {
        color: #2563eb;
        text-decoration: none;
    }

    a:hover {
        text-decoration: underline;
    }
</style>
