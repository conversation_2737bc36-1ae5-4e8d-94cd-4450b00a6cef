#include "Sensors.h"

constexpr int PRESSURE_SENSOR_PIN = A0;
constexpr int FLOW_SENSOR_PIN = A1;

// Mettre en cache les dernières lectures pour réduire les lectures analogiques redondantes qui sont lentes
static int lastPressureReading = 0;
static int lastFlowReading = 0;
static unsigned long lastPressureReadTime = 0;
static unsigned long lastFlowReadTime = 0;
constexpr unsigned long DEBOUNCE_DELAY = 100; // Min 100ms entre les lectures

void Sensors::init() {
    pinMode(PRESSURE_SENSOR_PIN, INPUT);
    pinMode(FLOW_SENSOR_PIN, INPUT);
    // Lectures initiales
    lastPressureReading = analogRead(PRESSURE_SENSOR_PIN);
    lastFlowReading = analogRead(FLOW_SENSOR_PIN);
    lastPressureReadTime = millis();
    lastFlowReadTime = lastPressureReadTime;
}

int Sensors::readPressureSensor() {
    unsigned long currentTime = millis();
    if (currentTime - lastPressureReadTime >= DEBOUNCE_DELAY) {
        lastPressureReading = analogRead(PRESSURE_SENSOR_PIN);
        lastPressureReadTime = currentTime;
    }
    return lastPressureReading;
}

int Sensors::readFlowSensor() {
    unsigned long currentTime = millis();
    if (currentTime - lastFlowReadTime >= DEBOUNCE_DELAY) {
        lastFlowReading = analogRead(FLOW_SENSOR_PIN);
        lastFlowReadTime = currentTime;
    }
    return lastFlowReading;
}

float Sensors::getPressurePercentage() {
    return (readPressureSensor() / 1023.0f) * 100.0f;
}

float Sensors::getFlowPercentage() {
    return (readFlowSensor() / 1023.0f) * 100.0f;
}
