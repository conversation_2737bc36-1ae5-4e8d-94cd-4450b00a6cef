# Gestion des Procès-Verbaux (PV)

## Vue d'ensemble

Le module **Procès-Verbaux (PV)** permet de créer, gérer et générer les rapports officiels des essais hydrauliques. Ces documents formalisent les résultats des tests et constituent la livraison finale au client.

## Interface du Module

### En-tête du Module

#### Bouton d'Action Principal
- **"Nouveau PV"** (bleu) : Créer un nouveau procès-verbal

### Tableau des PV

#### Colonnes Affichées

| Colonne | Description | Informations |
|---------|-------------|--------------|
| **Numéro** | Identifiant unique du PV | Format libre défini par l'utilisateur |
| **Affaire** | Numéro de l'affaire associée | Lien vers l'affaire source |
| **Date de création** | Date de création du PV | Format DD/MM/YYYY |
| **Créé par** | Nom de l'utilisateur créateur | Traçabilité de l'auteur |
| **Statut** | État actuel du PV | Badge coloré |
| **Actions** | Boutons d'action | PDF, Modifier, Supprimer, Voir |

### Statuts des PV

#### Brouillon
- <span className="status-en-attente">Brouillon</span>
- PV en cours de rédaction
- Couleur : Orange

#### Finalisé
- <span className="status-en-cours">Finalisé</span>
- PV complété et validé
- Couleur : Bleu

#### Envoyé
- <span className="status-termine">Envoyé</span>
- PV transmis au client
- Couleur : Vert

#### Annulé
- <span className="status-annule">Annulé</span>
- PV abandonné ou invalidé
- Couleur : Rouge

## Création d'un Nouveau PV

### Ouverture de la Modale

<div className="step-number">1</div>
<div className="step-content">

Cliquez sur **"Nouveau PV"** dans l'en-tête du module.

</div>

### Formulaire de Création

#### Champs Obligatoires

<div className="step-number">2</div>
<div className="step-content">

**Numéro du PV**
- Identifiant unique du procès-verbal
- Format libre (recommandé : préfixe + numéro)
- Exemple : "PV-2024-001" ou "PV-AFF-001-2024"

**Affaire Associée**
- Sélection dans la liste déroulante
- Format affiché : "Numéro - Client"
- Détermine le contexte du PV

**Contenu**
- Corps principal du procès-verbal
- Zone de texte libre
- Inclut résultats, conclusions, recommandations

</div>

### Validation et Enregistrement

<div className="step-number">3</div>
<div className="step-content">

- Cliquez sur **"Créer le PV"**
- Le PV est créé avec le statut "Brouillon"
- Date de création définie automatiquement

</div>

## Modification d'un PV

### Accès à la Modification

#### Depuis la Liste
- Cliquez sur **"Modifier"** dans la ligne du PV
- Ouverture de la modale de modification

### Formulaire de Modification

#### Champs Modifiables

**Affaire Associée**
- Changement possible vers une autre affaire
- Impact sur le contexte du rapport

**Numéro du PV**
- Modification de l'identifiant
- Attention aux références externes

**Contenu**
- Mise à jour du corps du rapport
- Ajout de résultats, conclusions

**Statut**
- Évolution selon le workflow
- Contrôle de la progression

#### Workflow de Statut

```mermaid
graph LR
    A[Brouillon] --> B[Finalisé]
    B --> C[Envoyé]
    A --> D[Annulé]
    B --> D[Annulé]
```

## Structure Recommandée d'un PV

### En-tête du Document

<div className="info-box">

**Informations Essentielles**
- Numéro du PV
- Date de création
- Référence de l'affaire
- Nom du client
- Objet de l'essai

</div>

### Corps du Rapport

#### Section 1 : Contexte
```
Objet : [Description de la demande client]
Matériel testé : [Référence et description]
Normes appliquées : [Standards et procédures]
```

#### Section 2 : Conditions d'Essai
```
Date d'exécution : [Date des tests]
Température ambiante : [Conditions environnementales]
Équipements utilisés : [Matériel de mesure]
Procédure : [Mode opératoire appliqué]
```

#### Section 3 : Résultats
```
Mesures effectuées :
- Pression maximale : [Valeur] bar
- Débit nominal : [Valeur] L/min
- Température de fonctionnement : [Valeur] °C

Observations :
[Comportement du matériel, anomalies détectées]
```

#### Section 4 : Conclusions
```
Conformité : [Conforme/Non conforme aux spécifications]
Recommandations : [Actions suggérées]
Validité : [Durée de validité du rapport]
```

### Pied de Page
```
Rédigé par : [Nom de l'opérateur]
Validé par : [Nom du contrôleur]
Date : [Date de finalisation]
Signature : [Signature électronique ou manuscrite]
```

## Génération PDF

### Accès à la Génération

#### Depuis la Liste
- Cliquez sur **"PDF"** dans la ligne du PV
- Génération et téléchargement automatiques

#### Depuis la Vue Détaillée
- Bouton "Générer PDF" dans la vue détaillée
- Options de personnalisation disponibles

### Caractéristiques du PDF

#### Format et Présentation
- **Format** : A4 standard
- **Orientation** : Portrait
- **Marges** : Professionnelles
- **Police** : Lisible et standard

#### Contenu Automatique
- **En-tête** avec logo et informations laboratoire
- **Numérotation** des pages
- **Date de génération** automatique
- **Filigrane** si brouillon

#### Métadonnées
- **Titre** : Numéro du PV
- **Auteur** : Nom du créateur
- **Sujet** : Référence de l'affaire
- **Mots-clés** : Tags de l'affaire

### Stockage et Archivage

#### Fichiers Générés
- **Nom** : Format standardisé "PV_[Numéro]_[Date].pdf"
- **Emplacement** : Dossier `/pdf_exports/`
- **Conservation** : Archivage automatique

#### Traçabilité
- **Horodatage** de génération
- **Version** du contenu
- **Utilisateur** générateur

## Workflow Complet d'un PV

### Phase de Rédaction

<div className="step-number">1</div>
<div className="step-content">

**Création en Brouillon**
- Création avec statut "Brouillon"
- Rédaction progressive du contenu
- Sauvegardes multiples possibles

</div>

### Phase de Validation

<div className="step-number">2</div>
<div className="step-content">

**Finalisation**
- Relecture et validation du contenu
- Changement de statut vers "Finalisé"
- Génération du PDF final

</div>

### Phase de Livraison

<div className="step-number">3</div>
<div className="step-content">

**Envoi au Client**
- Transmission du PDF au client
- Changement de statut vers "Envoyé"
- Archivage définitif

</div>

## Bonnes Pratiques

### Rédaction Efficace

<div className="success-box">

**Conseils de Rédaction**
1. **Structurez** le contenu avec des sections claires
2. **Utilisez** un langage technique précis
3. **Incluez** toutes les mesures importantes
4. **Documentez** les conditions d'essai
5. **Concluez** clairement sur la conformité

</div>

### Gestion des Versions

#### Contrôle des Modifications
- **Brouillon** : Modifications libres
- **Finalisé** : Modifications contrôlées
- **Envoyé** : Modifications exceptionnelles

#### Traçabilité
- Historique des modifications
- Identification des auteurs
- Horodatage des changements

### Archivage et Conservation

#### Durée de Conservation
- **PV finalisés** : Conservation permanente
- **Brouillons** : Nettoyage périodique possible
- **PDF générés** : Archivage long terme

#### Organisation
- Classement par affaire
- Indexation par numéro
- Recherche facilitée

## Suppression d'un PV

### Processus de Suppression

<div className="warning-box">

**⚠️ Attention : Action Irréversible**

La suppression d'un PV entraîne :
- Perte du contenu rédigé
- Suppression des PDF générés
- Impact sur la traçabilité de l'affaire
- Perte de la documentation officielle

</div>

### Alternatives à la Suppression

#### Statut "Annulé"
- Préférable à la suppression
- Conservation de l'historique
- Traçabilité maintenue

#### Archivage
- Déplacement vers archives
- Conservation des données
- Accès restreint

---

:::tip Rédaction Efficace
Rédigez vos PV immédiatement après les essais pour garantir la précision et la complétude des informations.
:::

:::warning Validation
Faites relire vos PV par un collègue avant finalisation pour éviter les erreurs et améliorer la qualité.
:::
