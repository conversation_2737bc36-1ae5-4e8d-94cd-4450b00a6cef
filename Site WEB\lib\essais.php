<?php
require_once(__DIR__ . '/../config/database.php');
require_once(__DIR__ . '/modeles.php');

class Essai
{
    public static function create($affaire_id, $type, $parametre_theorique, $date_essai, $mode_operatoire = null)
    {
        $stmt = self::getDb()->prepare("
            INSERT INTO essais (affaire_id, type, parametre_theorique, date_essai, mode_operatoire)
            VALUES (?, ?, ?, ?, ?)
        ");
        $success = $stmt->execute([
            $affaire_id,
            $type,
            $parametre_theorique,
            $date_essai,
            $mode_operatoire ? json_encode($mode_operatoire) : null
        ]);
        return $success ? self::getDb()->lastInsertId() : false;
    }

    // Créer un nouvel essai

    private static function getDb()
    {
        return Database::getInstance()->getConnection();
    }

    public static function createFromModele($affaire_id, $modele, $date_essai)
    {
        $stmt = self::getDb()->prepare("
            INSERT INTO essais (affaire_id, type, parametre_theorique, date_essai, statut, mode_operatoire)
            VALUES (?, ?, ?, ?, 'En attente', ?)
        ");
        return $stmt->execute([
            $affaire_id,
            $modele['type'],
            $modele['parametre_theorique'],
            $date_essai,
            $modele['mode_operatoire']
        ]);
    }

    // Récupérer un essai par son ID
    public static function getById($id)
    {
        $stmt = self::getDb()->prepare("
            SELECT e.*, a.numero AS numero_affaire
            FROM essais e
            LEFT JOIN affaires a ON e.affaire_id = a.id
            WHERE e.id = ?
        ");
        $stmt->execute([$id]);
        $essai = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($essai && $essai['mode_operatoire']) {
            $essai['mode_operatoire'] = json_decode($essai['mode_operatoire'], true);
        }
        return $essai;
    }

    // Mettre à jour un essai
    public static function update($id, $type, $parametre_theorique, $statut, $resultat = null, $mode_operatoire = null)
    {
        $stmt = self::getDb()->prepare("
            UPDATE essais
            SET type = ?, parametre_theorique = ?, statut = ?, resultat = ?, mode_operatoire = ?
            WHERE id = ?
        ");
        return $stmt->execute([
            $type,
            $parametre_theorique,
            $statut,
            $resultat,
            $mode_operatoire ? json_encode($mode_operatoire) : null,
            $id
        ]);
    }

    // Supprimer un essai
    public static function delete($id)
    {
        $stmt = self::getDb()->prepare("DELETE FROM essais WHERE id = ?");
        return $stmt->execute([$id]);
    }

    // Mettre à jour le statut d'un essai
    public static function updateStatut($id, $statut)
    {
        $stmt = self::getDb()->prepare("UPDATE essais SET statut = ? WHERE id = ?");
        return $stmt->execute([$statut, $id]);
    }

    // Récupérer tous les essais d'une affaire
    public static function getByAffaireId($affaire_id)
    {
        $stmt = self::getDb()->prepare("
            SELECT * FROM essais
            WHERE affaire_id = ?
            ORDER BY date_essai DESC
        ");
        $stmt->execute([$affaire_id]);
        $essais = $stmt->fetchAll(PDO::FETCH_ASSOC);
        foreach ($essais as &$essai) {
            if ($essai['mode_operatoire']) {
                $essai['mode_operatoire'] = json_decode($essai['mode_operatoire'], true);
            }
        }
        return $essais;
    }

    // Récupérer les essais par statut
    public static function getByStatut($statut)
    {
        $stmt = self::getDb()->prepare("
            SELECT e.*, a.numero AS numero_affaire
            FROM essais e
            LEFT JOIN affaires a ON e.affaire_id = a.id
            WHERE e.statut = ?
            ORDER BY e.date_essai DESC
        ");
        $stmt->execute([$statut]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Ajouter ou mettre à jour le résultat d'un essai
    public static function updateResultat($id, $resultat)
    {
        $stmt = self::getDb()->prepare("UPDATE essais SET resultat = ? WHERE id = ?");
        return $stmt->execute([$resultat, $id]);
    }

    // Mettre à jour le mode opératoire
    public static function updateModeOperatoire($id, $mode_operatoire)
    {
        $stmt = self::getDb()->prepare("UPDATE essais SET mode_operatoire = ? WHERE id = ?");
        return $stmt->execute([json_encode($mode_operatoire), $id]);
    }

    // Obtenir le nombre total d'essais
    public static function getCount($statut = null)
    {
        $sql = "SELECT COUNT(*) FROM essais";
        if ($statut) {
            $sql .= " WHERE statut = ?";
            $stmt = self::getDb()->prepare($sql);
            $stmt->execute([$statut]);
        } else {
            $stmt = self::getDb()->query($sql);
        }
        return $stmt->fetchColumn();
    }

    public static function getAll()
    {
        $stmt = self::getDb()->query("
            SELECT e.*, a.numero AS numero_affaire
            FROM essais e
            LEFT JOIN affaires a ON e.affaire_id = a.id
            ORDER BY e.date_essai DESC
        ");
        $essais = $stmt->fetchAll(PDO::FETCH_ASSOC);
        foreach ($essais as &$essai) {
            if ($essai['mode_operatoire']) {
                $essai['mode_operatoire'] = json_decode($essai['mode_operatoire'], true);
            }
        }
        return $essais;
    }

    public static function getByType($type)
    {
        $stmt = self::getDb()->prepare("
            SELECT e.*, a.numero AS numero_affaire
            FROM essais e
            LEFT JOIN affaires a ON e.affaire_id = a.id
            WHERE e.type = ?
            ORDER BY e.date_essai DESC
        ");
        $stmt->execute([$type]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Récupérer un essai par critères multiples
     */
    public static function getByMultipleCriteria($affaire_id, $type, $date_essai)
    {
        $stmt = self::getDb()->prepare("
            SELECT id FROM essais
            WHERE affaire_id = ? AND type = ? AND date_essai = ?
            ORDER BY id DESC LIMIT 1
        ");
        $stmt->execute([$affaire_id, $type, $date_essai]);
        return $stmt->fetchColumn();
    }

    /**
     * Mettre à jour le statut et le résultat d'un essai
     */
    public static function updateStatusAndResult($id, $statut, $resultat = null)
    {
        $stmt = self::getDb()->prepare("
            UPDATE essais
            SET statut = ?, resultat = ?
            WHERE id = ?
        ");
        return $stmt->execute([$statut, $resultat, $id]);
    }

    /**
     * Marquer un essai comme synthétique
     */
    public static function markAsSynthetic($id)
    {
        $stmt = self::getDb()->prepare("UPDATE essais SET is_synthetic = 1 WHERE id = ?");
        return $stmt->execute([$id]);
    }

    /**
     * Récupérer le statut d'un essai
     */
    public static function getStatus($id)
    {
        $stmt = self::getDb()->prepare("SELECT statut FROM essais WHERE id = ?");
        $stmt->execute([$id]);
        return $stmt->fetchColumn();
    }

    /**
     * Récupérer un essai avec les informations de l'affaire
     */
    public static function getWithAffaireInfo($id)
    {
        $stmt = self::getDb()->prepare("
            SELECT e.*, a.numero AS affaire_numero, a.client
            FROM essais e
            JOIN affaires a ON e.affaire_id = a.id
            WHERE e.id = ?
        ");
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Récupérer les essais terminés avec informations affaire
     */
    public static function getCompletedWithAffaireInfo($essai_id)
    {
        $stmt = self::getDb()->prepare("
            SELECT e.*, a.numero AS affaire_numero, a.client
            FROM essais e
            JOIN affaires a ON e.affaire_id = a.id
            WHERE e.id = ? AND e.statut = 'Terminé'
        ");
        $stmt->execute([$essai_id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Supprimer tous les essais synthétiques
     */
    public static function deleteSynthetic()
    {
        $stmt = self::getDb()->prepare("DELETE FROM essais WHERE is_synthetic = 1");
        $stmt->execute();
        return $stmt->rowCount();
    }

    /**
     * Récupérer l'affaire_id d'un essai
     */
    public static function getAffaireId($essai_id)
    {
        $stmt = self::getDb()->prepare("SELECT affaire_id FROM essais WHERE id = ?");
        $stmt->execute([$essai_id]);
        return $stmt->fetchColumn();
    }

    /**
     * Supprimer tous les essais
     */
    public static function deleteAll()
    {
        $stmt = self::getDb()->prepare("DELETE FROM essais");
        $stmt->execute();
        $count = $stmt->rowCount();

        // Réinitialiser l'auto-increment
        self::getDb()->exec("ALTER TABLE essais AUTO_INCREMENT = 1");

        return $count;
    }
}
