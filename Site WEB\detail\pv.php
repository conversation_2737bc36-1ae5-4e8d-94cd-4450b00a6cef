<?php
session_start();
require_once(__DIR__ . '/../lib/pv.php');
require_once(__DIR__ . '/../lib/affaires.php');

if (!isset($_SESSION['user'])) {
    header('Location: /auth/login.php');
    exit;
}

if (!isset($_GET['id'])) {
    header('Location: /pv.php');
    exit;
}

$pv = PV::getById($_GET['id']);
if (!$pv) {
    header('Location: /pv.php');
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'update':
                if (PV::update(
                    $_POST['id'],
                    $_POST['numero'],
                    $_POST['contenu'] ?? null,
                    $_POST['statut']
                )) {
                    $success = "PV mis à jour avec succès";
                } else {
                    $error = "Erreur lors de la mise à jour du PV";
                }
                break;
            case 'delete':
                if (PV::delete($_POST['id'])) {
                    header('Location: /pv.php');
                    exit;
                } else {
                    $error = "Erreur lors de la suppression du PV";
                }
                break;
        }
    }
}

ob_start();

// Afficher les messages d'erreur ou de succès
$success = $_GET['success'] ?? '';
$error = $_GET['error'] ?? '';
?>

<?php if ($success): ?>
    <div class="p-4 mb-4 text-sm text-green-800 rounded-lg bg-green-50 dark:bg-gray-800 dark:text-green-400">
        <?php echo htmlspecialchars($success); ?>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400">
        <?php echo htmlspecialchars($error); ?>
    </div>
<?php endif; ?>

<div class="p-4">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-2xl font-bold dark:text-white">Détail du PV #<?php echo htmlspecialchars($pv['numero']); ?></h2>
        <div class="flex space-x-2">
            <a href="/pdf_handler.php?action=generate&pv_id=<?php echo $pv['id']; ?>"
               class="text-white bg-green-700 hover:bg-green-800 focus:ring-4 focus:ring-green-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-green-600 dark:hover:bg-green-700 focus:outline-none dark:focus:ring-green-800 inline-flex items-center">
                <svg class="w-4 h-4 me-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                     viewBox="0 0 24 24">
                    <path fill-rule="evenodd"
                          d="M9 2.221V7H4.221a2 2 0 0 1 .365-.5L8.5 2.586A2 2 0 0 1 9 2.22ZM11 2v5a2 2 0 0 1-2 2H4v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2h-7Z"
                          clip-rule="evenodd"/>
                </svg>
                Générer PDF
            </a>
            <button data-modal-target="editPVModal" data-modal-toggle="editPVModal"
                    class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">
                <svg class="w-4 h-4 me-2 inline" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                     fill="currentColor" viewBox="0 0 24 24">
                    <path d="m14.304 4.844 2.852 2.852M7 7H4a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1v-4.5m2.409-9.91a2.017 2.017 0 0 1 0 2.853l-6.844 6.844L8 14l.713-3.565 6.844-6.844a2.015 2.015 0 0 1 2.846 0Z"/>
                </svg>
                Modifier
            </button>
            <button data-modal-target="deletePVModal" data-modal-toggle="deletePVModal"
                    class="focus:outline-none text-white bg-red-700 hover:bg-red-800 focus:ring-4 focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-900">
                <svg class="w-4 h-4 me-2 inline" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                     fill="currentColor" viewBox="0 0 24 24">
                    <path fill-rule="evenodd"
                          d="M8.586 2.586A2 2 0 0 1 10 2h4a2 2 0 0 1 2 2v2h3a1 1 0 1 1 0 2v12a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V8a1 1 0 0 1 0-2h3V4a2 2 0 0 1 .586-1.414ZM10 6h4V4h-4v2Zm1 4a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Zm4 0a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Z"
                          clip-rule="evenodd"/>
                </svg>
                Supprimer
            </button>
        </div>
    </div>

    <div class="grid md:grid-cols-2 gap-4">
        <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <h3 class="text-lg font-semibold mb-4 dark:text-white">Informations générales</h3>
            <dl class="grid grid-cols-1 gap-4">
                <div>
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Numéro de PV</dt>
                    <dd class="text-lg font-semibold text-gray-900 dark:text-white"><?php echo htmlspecialchars($pv['numero']); ?></dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Numéro d'affaire</dt>
                    <dd class="text-lg font-semibold text-gray-900 dark:text-white">
                        <a href="/detail/affaire.php?id=<?php echo $pv['affaire_id']; ?>"
                           class="text-blue-600 hover:underline">
                            <?php echo htmlspecialchars($pv['numero_affaire']); ?>
                        </a>
                    </dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Date de création</dt>
                    <dd class="text-lg font-semibold text-gray-900 dark:text-white"><?php echo htmlspecialchars($pv['date_creation']); ?></dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Statut</dt>
                    <dd>
                        <span class="text-sm font-medium px-2.5 py-0.5 rounded-md border
                            <?php
                        switch ($pv['statut']) {
                            case 'Annulé':
                                echo 'bg-red-100 text-red-800 dark:bg-gray-700 dark:text-red-400 border-red-100 dark:border-red-500';
                                break;
                            case 'Finalisé':
                            case 'Envoyé':
                                echo 'bg-green-100 text-green-800 dark:bg-gray-700 dark:text-green-400 border-green-100 dark:border-green-500';
                                break;
                            default:
                                echo 'bg-yellow-100 text-yellow-800 dark:bg-gray-700 dark:text-yellow-400 border-yellow-100 dark:border-yellow-500';
                        }
                        ?>">
                            <?php echo htmlspecialchars($pv['statut']); ?>
                        </span>
                    </dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Créé par</dt>
                    <dd class="text-lg font-semibold text-gray-900 dark:text-white"><?php echo htmlspecialchars($pv['created_by_name']); ?></dd>
                </div>
            </dl>
        </div>

        <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <h3 class="text-lg font-semibold mb-4 dark:text-white">Contenu</h3>
            <div class="prose dark:prose-invert">
                <?php if ($pv['contenu']): ?>
                    <p class="text-gray-900 dark:text-white"><?php echo nl2br(htmlspecialchars($pv['contenu'])); ?></p>
                <?php else: ?>
                    <p class="text-gray-500 dark:text-gray-400">Aucun contenu</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Modal de modification -->
<div id="editPVModal" tabindex="-1" aria-hidden="true"
     class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative p-4 w-full max-w-md max-h-full">
        <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
            <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                    Modifier le PV
                </h3>
                <button type="button"
                        class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                        data-modal-hide="editPVModal">
                    <svg class="w-3 h-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                    </svg>
                    <span class="sr-only">Fermer</span>
                </button>
            </div>
            <form class="p-4 md:p-5" method="POST">
                <input type="hidden" name="action" value="update">
                <input type="hidden" name="id" value="<?php echo $pv['id']; ?>">
                <div class="grid gap-4 mb-4">
                    <div>
                        <label for="numero"
                               class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Numéro</label>
                        <input type="text" name="numero" id="numero"
                               value="<?php echo htmlspecialchars($pv['numero']); ?>"
                               class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                               required>
                    </div>
                    <div>
                        <label for="statut"
                               class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Statut</label>
                        <select name="statut" id="statut"
                                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                required>
                            <option value="Brouillon" <?php echo $pv['statut'] == 'Brouillon' ? 'selected' : ''; ?>>
                                Brouillon
                            </option>
                            <option value="Finalisé" <?php echo $pv['statut'] == 'Finalisé' ? 'selected' : ''; ?>>
                                Finalisé
                            </option>
                            <option value="Envoyé" <?php echo $pv['statut'] == 'Envoyé' ? 'selected' : ''; ?>>Envoyé
                            </option>
                            <option value="Annulé" <?php echo $pv['statut'] == 'Annulé' ? 'selected' : ''; ?>>Annulé
                            </option>
                        </select>
                    </div>
                    <div>
                        <label for="contenu" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Contenu</label>
                        <textarea name="contenu" id="contenu" rows="4"
                                  class="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"><?php echo htmlspecialchars($pv['contenu'] ?? ''); ?></textarea>
                    </div>
                </div>
                <button type="submit"
                        class="text-white inline-flex items-center bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                    Enregistrer les modifications
                </button>
            </form>
        </div>
    </div>
</div>

<!-- Modal de suppression -->
<div id="deletePVModal" tabindex="-1" aria-hidden="true"
     class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative p-4 w-full max-w-md max-h-full">
        <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
            <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                    Confirmer la suppression
                </h3>
                <button type="button"
                        class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                        data-modal-hide="deletePVModal">
                    <svg class="w-3 h-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                    </svg>
                    <span class="sr-only">Fermer</span>
                </button>
            </div>
            <div class="p-4 md:p-5">
                <p class="text-gray-500 dark:text-gray-300">Êtes-vous sûr de vouloir supprimer ce PV ? Cette action est
                    irréversible.</p>
                <form method="POST" class="mt-5">
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="id" value="<?php echo $pv['id']; ?>">
                    <button type="submit"
                            class="text-white bg-red-600 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 dark:focus:ring-red-800 font-medium rounded-lg text-sm inline-flex items-center px-5 py-2.5 text-center me-2">
                        Oui, supprimer
                    </button>
                    <button type="button" data-modal-hide="deletePVModal"
                            class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900 focus:z-10 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-500 dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-gray-600">
                        Non, annuler
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script src="/assets/js/print.js"></script>
<link rel="stylesheet" href="/assets/css/print.css" media="print">

<?php
$pageContent = ob_get_clean();
include '../layout.php';
?>
